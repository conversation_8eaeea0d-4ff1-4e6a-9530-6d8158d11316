<!DOCTYPE html>
<html>
<head>
    <title>Private api.video Player</title>
    <style>
        #video-player {
            width: 100%;
            max-width: 640px;
            height: 360px;
        }
    </style>
    <script src="https://unpkg.com/@api.video/player-sdk" defer></script>
</head>
<body>
<div id="video-player"></div>
<script>
    window.onload = function () {
        try {
            const player = new PlayerSdk("#video-player", {
                id: "vi3lw4MYCRWBpvqzQDmg4cjc",
                token: "0f771aeb-64c5-445f-b855-92c7aeb6a6f0",
                controls: true,
            });

            player.addEventListener("play", () => console.log("Playing"));
            player.addEventListener("pause", () => console.log("Paused"));
            player.addEventListener("error", (err) => console.error("Player error:", err));
        } catch (err) {
            console.error("SDK initialization failed:", err);
        }
    };
</script>
</body>
</html>