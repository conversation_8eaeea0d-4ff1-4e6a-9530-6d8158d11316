# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-21T10:45:54+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/class-acf-site-health.php:291
msgid "Update Source"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "デフォルトでは管理者ユーザーのみがこの設定を編集できます。"

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "デフォルトでは特権管理者ユーザーのみがこの設定を編集できます。"

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/class-acf-site-health.php:292
msgid "wordpress.org"
msgstr ""

#: includes/validation.php:136
msgid ""
"ACF was unable to perform validation due to an invalid security nonce being "
"provided."
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr ""

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr ""

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""

#: includes/Blocks/Bindings.php:64
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 includes/Blocks/Bindings.php:72
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[ACFショートコードはこのサイトでは無効化されています]"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr "ビジネスマンアイコン"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr "フォーラムアイコン"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr "YouTube アイコン"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr "Xing アイコン"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr "WordPress (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr "WhatsApp アイコン"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr "ウィジェットメニューアイコン"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr "サイト表示アイコン"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr "詳細アイコン"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr "ページ追加アイコン"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr "動画 (alt3) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr "動画 (alt2) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr "動画 (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr "Twitter (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr "チケット (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr "テキストページアイコン"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr "Spotify アイコン"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr "ショートコードアイコン"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr "保存アイコン"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr "RSS アイコン"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr "REST API アイコン"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr "削除アイコン"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr "Reddit アイコン"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr "プライバシーアイコン"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr "プリンターアイコン"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr "Podioアイコン"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr "プラス (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr "Pinterest アイコン"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr "ペットアイコン"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr "PDF アイコン"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr "ヤシの木アイコン"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr "スプレッドシート アイコン"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr "対話アイコン"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr "ドキュメントアイコン"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr "デフォルトアイコン"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr "ロケーション (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr "LinkedIn アイコン"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr "Instagram アイコン"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr "左回転アイコン"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr "水平に反転アイコン"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr "切り抜きアイコン"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "ID (alt) Icon"
msgstr "ID (alt) アイコン"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr "HTML アイコン"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr "砂時計アイコン"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr "見出しアイコン"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr "Google アイコン"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr "ステータスアイコン"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr "画像アイコン"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr "ギャラリーアイコン"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr "チャットアイコン"

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr "オーディオアイコン"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "bbPress Icon"
msgstr "BbPress アイコン"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr "ブロックデフォルトアイコン"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr "右揃えアイコン"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr "左揃えアイコン"

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr ""

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "無効なリクエストです。"

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "その操作を行う権限がありません。"

#: includes/class-acf-site-health.php:648
msgid "Blocks Using Post Meta"
msgstr "投稿メタを使用したブロック"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO ロゴ"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr "保存するアイコンの値。"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr "保存するアイコンのタイプ。"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr "承認アイコン"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr "WordPress アイコン"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr "翻訳アイコン"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr "マイクアイコン"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr "マーカーアイコン"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr "ロックアイコン"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr "ロケーションアイコン"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr "電球アイコン"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr "レイアウトアイコン"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr "ラップトップアイコン"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr "情報アイコン"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "ID Icon"
msgstr "IDアイコン"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr "非表示アイコン"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr "ハートアイコン"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr "ハンマーアイコン"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr "グループアイコン"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr "グリッドビューアイコン"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr "フォームアイコン"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr "旗アイコン"

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr "フィルターアイコン"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr "フィードバックアイコン"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr "Facebook アイコン"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr "外部アイコン"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr "メールアイコン"

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr "動画アイコン"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr "リンク解除アイコン"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr "下線アイコン"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr "文字の色アイコン"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr "テーブルアイコン"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr "打ち消しアイコン"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr "スペルチェックアイコン"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr "引用アイコン"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr "テキスト貼り付けアイコン"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr "段落アイコン"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr "アウトデントアイコン"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr "イタリックアイコン"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr "挿入アイコン"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr "インデントアイコン"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr "ヘルプアイコン"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr "コードアイコン"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr "改行アイコン"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr "太字アイコン"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr "編集アイコン"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr "ダウンロードアイコン"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr "閉じるアイコン"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr "デスクトップアイコン"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr "ダッシュボードアイコン"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr "にんじんアイコン"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr "投稿アイコン"

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: includes/class-acf-site-health.php:709
msgid "JSON Load Paths"
msgstr ""

#: includes/class-acf-site-health.php:703
msgid "JSON Save Paths"
msgstr ""

#: includes/class-acf-site-health.php:694
msgid "Registered ACF Forms"
msgstr ""

#: includes/class-acf-site-health.php:688
msgid "Shortcode Enabled"
msgstr ""

#: includes/class-acf-site-health.php:680
msgid "Field Settings Tabs Enabled"
msgstr ""

#: includes/class-acf-site-health.php:672
msgid "Field Type Modal Enabled"
msgstr ""

#: includes/class-acf-site-health.php:664
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:655
msgid "Block Preloading Enabled"
msgstr ""

#: includes/class-acf-site-health.php:643
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:638
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:611
msgid "Registered ACF Blocks"
msgstr ""

#: includes/class-acf-site-health.php:605
msgid "Light"
msgstr ""

#: includes/class-acf-site-health.php:605
msgid "Standard"
msgstr ""

#: includes/class-acf-site-health.php:604
msgid "REST API Format"
msgstr ""

#: includes/class-acf-site-health.php:596
msgid "Registered Options Pages (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:582
msgid "Registered Options Pages (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (UI)"
msgstr ""

#: includes/class-acf-site-health.php:547
msgid "Options Pages UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:539
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:527
msgid "Registered Taxonomies (UI)"
msgstr ""

#: includes/class-acf-site-health.php:515
msgid "Registered Post Types (JSON)"
msgstr "登録された投稿タイプ (JSON)"

#: includes/class-acf-site-health.php:503
msgid "Registered Post Types (UI)"
msgstr ""

#: includes/class-acf-site-health.php:490
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: includes/class-acf-site-health.php:483
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:478
msgid "Number of Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:445
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: includes/class-acf-site-health.php:432
msgid "Field Groups Enabled for REST API"
msgstr ""

#: includes/class-acf-site-health.php:420
msgid "Registered Field Groups (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:408
msgid "Registered Field Groups (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:396
msgid "Registered Field Groups (UI)"
msgstr ""

#: includes/class-acf-site-health.php:384
msgid "Active Plugins"
msgstr ""

#: includes/class-acf-site-health.php:358
msgid "Parent Theme"
msgstr ""

#: includes/class-acf-site-health.php:347
msgid "Active Theme"
msgstr ""

#: includes/class-acf-site-health.php:338
msgid "Is Multisite"
msgstr ""

#: includes/class-acf-site-health.php:333
msgid "MySQL Version"
msgstr ""

#: includes/class-acf-site-health.php:328
msgid "WordPress Version"
msgstr ""

#: includes/class-acf-site-health.php:321
msgid "Subscription Expiry Date"
msgstr ""

#: includes/class-acf-site-health.php:313
msgid "License Status"
msgstr ""

#: includes/class-acf-site-health.php:308
msgid "License Type"
msgstr ""

#: includes/class-acf-site-health.php:303
msgid "Licensed URL"
msgstr ""

#: includes/class-acf-site-health.php:297
msgid "License Activated"
msgstr ""

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr ""

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr ""

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr ""

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "タームが一致する"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "ユーザーが選択されていません"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "ユーザーが一致しない"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "投稿が以下に等しい場合"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr ""

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO 機能"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "詳細については、サイト管理者または開発者にお問い合わせください。"

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "さらに詳しく"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "詳細を隠す"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "詳細を表示"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "ACF プロ版を更新する"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "ライセンスを更新"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "ライセンスの管理"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "位置「高」はブロックエディタ―ではサポートされていません"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "ACF プロ版へアップグレード"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "オプションページを追加"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "タイトルのプレースホルダーとして使用されます。"

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "タイトルプレースホルダー"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4ヶ月無料"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "( %s からの複製)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "オプションページを選択"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "タクソノミーを複製"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "タクソノミーを作成"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "投稿タイプを複製"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "投稿タイプを作成"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "フィールドグループ"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "フィールドを追加"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "このフィールド"

#: includes/admin/admin.php:352
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:350
msgid "Feedback"
msgstr "フィードバック"

#: includes/admin/admin.php:348
msgid "Support"
msgstr "サポート"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:323
msgid "is developed and maintained by"
msgstr "によって開発され、維持されている"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "%s を選択したフィールドグループのロケーションルールに追加します。"

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "ターゲットフィールド"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "双方向"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s フィールド"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "複数選択"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "WP Engine ロゴ"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "小文字、アンダースコア、ダッシュのみ、最大32文字"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "投稿などでこのタクソノミーのタームを設定するための権限"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "ターム割り当て"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "このタクソノミーのタームを削除するための権限"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "ターム削除機能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "このタクソノミーのタームを編集するための権限"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "ターム編集機能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "このタクソノミーの管理画面へのアクセスを許可する権限"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "ターム管理機能"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr "サイト内検索やタクソノミーアーカイブから投稿を除外するかどうかを設定。"

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP Engine 他のツール"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "%s のチームによって、WordPressで構築する人々のために構築された"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "価格とアップグレードを見る"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "さらに詳しく"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s フィールド"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "タームはありません"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "投稿タイプはありません"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "投稿はありません"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "タクソノミーはありません"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "フィールドグループはありません"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "フィールドはありません"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "説明はありません"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "すべての投稿ステータス"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"このタクソノミーのキーはすでに ACF の他のタクソノミーで使用されているので使用"
"できません。"

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"タクソノミーのキーは小文字の英数字、アンダースコア、またはダッシュのみが含ま"
"れます。"

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "タクソノミーのキーは32字以内にする必要があります。"

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "ゴミ箱内にタクソノミーが見つかりませんでした。"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "タクソノミーが見つかりません"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "タクソノミーを検索"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "タクソノミーを表示"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "新規タクソノミー"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "タクソノミーを編集"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "新規タクソノミーを追加"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "ゴミ箱内に投稿タイプが見つかりませんでした。"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "投稿タイプが見つかりません。"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "投稿タイプを検索"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "投稿タイプを表示"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "新規投稿タイプ"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "投稿タイプを編集"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "新規投稿タイプを追加"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"この投稿タイプキーは、ACF の外側で登録された別の投稿タイプによってすでに使用"
"されているため、使用できません。"

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"この投稿タイプキーは、ACF の別の投稿タイプによってすでに使用されているため、"
"使用できません。"

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "投稿タイプのキーは20字以内にする必要があります。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "このフィールドの ACF ブロックでの使用は推奨されません。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "リッチ エディター (WYSIWYG)"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"データオブジェクト間のリレーションシップを作成するために使用できる、1人または"
"複数のユーザーを選択できます。"

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "ウェブアドレスを保存するために特別に設計されたテキスト入力。"

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "テキストの段落を保存するための標準的な入力テキストエリア。"

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "基本的なテキスト入力で、単一の文字列値を格納するのに便利です。"

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "投稿ステータスで絞り込む"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "数値のみの入力"

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"データと編集画面をよりよく整理するために、フィールドをグループに構造化する方"
"法を提供します。"

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "メールアドレスを保存するために特別に設計されたテキスト入力。"

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"ユーザーが1つ、または指定した複数の値を選択できるチェックボックス入力のグルー"
"プ。"

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"あなたが指定した値を持つボタンのグループ、ユーザーは提供された値から1つのオプ"
"ションを選択することができます。"

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"コンテンツの編集中に表示される折りたたみ可能なパネルに、カスタムフィールドを"
"グループ化して整理することができます。大規模なデータセットを整理整頓するのに"
"便利です。"

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"これは、スライド、チームメンバー、行動喚起表示などのコンテンツを繰り返し表示"
"するためのソリューションで、繰り返し表示できる一連のサブフィールドの親として"
"機能します。"

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"これは、添付ファイルのコレクションを管理するためのインタラクティブなインター"
"フェイスを提供します。ほとんどの設定は画像フィールドタイプと似ています。追加"
"設定では、ギャラリーで新しい添付ファイルを追加する場所と、許可される添付ファ"
"イルの最小/最大数を指定できます。"

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "複製"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "高度"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (新しい)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "オリジナル"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "無効な投稿 ID です。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "レビューには無効な投稿タイプが選択されています。"

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "詳細"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "チュートリアル"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "フィールド選択"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "よく使うフィールド"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "「%s」に合う検索結果はありません"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "フィールドを検索..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "フィールドタイプを選択する"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "人気"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "タクソノミーを追加"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "最初のタクソノミーを追加"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "ジャンル"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "ジャンル"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "ジャンル"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "この投稿タイプをREST APIで公開する。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "クエリ変数名をカスタマイズ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "このタクソノミーが親子関係を持つかどうか。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "URLに使用されるスラッグをカスタマイズする"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "このタクソノミーのパーマリンクは無効です。"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"タクソノミーのキーをスラッグとして使用してURLを書き換えます。パーマリンク構造"
"は次のようになります"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "タクソノミーキー"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "このタクソノミーに使用するパーマリンクのタイプを選択します。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "投稿タイプの一覧画面にタクソノミーの項目を表示します。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "管理画面でカラムを表示"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "タクソノミーをクイック/一括編集パネルで表示"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "クイック編集"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "タグクラウドウィジェットにタクソノミーを表示"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "タグクラウド"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"メタボックスから保存されたタクソノミーデータをサニタイズするために呼び出され"
"るPHP関数名。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "メタボックスのサニタイズコールバック"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "メタボックスなし"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "カスタムメタボックス"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "メタボックス"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "タグメタボックス"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "タグへのリンク"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "%s へのリンク"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "タグリンク"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← タグへ戻る"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "項目に戻る"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← %s へ戻る"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "タグ一覧"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "テーブルの非表示見出しにテキストを割り当てます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "タグリストナビゲーション"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "テーブルのページ送りの非表示見出しにテキストを割り当てます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "カテゴリーで絞り込む"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "アイテムをフィルタリング"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "%s で絞り込む"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "フィールドディスクリプションの説明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"階層化するには親のタームを指定します。たとえば「ジャズ」というタームを「ビ"
"バップ」や「ビッグバンド」の親として指定します。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "タグの編集画面の親フィールドの説明文です。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "親フィールドの説明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"「スラッグ」は、URL に適した形式の名前です。通常はすべて小文字で、文字、数"
"字、ハイフンのみが使用されます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "タグなし"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "項目はありません"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "No %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "タグが見つかりませんでした"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "見つかりません"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "最も使われている"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "よく使うものから選択"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "最もよく使われている%sから選択"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "タグの追加もしくは削除"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "項目の追加または削除"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s を追加または削除する"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "タグはコンマで区切ってください"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "項目が複数ある場合はコンマで区切ってください"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "%s が複数ある場合はコンマで区切る"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "人気のタグ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "よく使う項目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "人気の %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "タグを検索"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "検索項目のテキストを割り当てます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "親カテゴリー:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "親カテゴリー"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "親項目"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "親 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "新規タグ名"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "新規項目名"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "新規 %s 名"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "新規タグを追加"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "タグを更新"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "アイテムを更新"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "%s を更新"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "タグを表示"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "タグを編集"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "すべての タグ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "すべての項目のテキストを割り当てます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "メニュー名のテキストを割り当てます。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "メニューラベル"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "タクソノミーの説明的要約。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "タームの説明的要約。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "タームの説明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "単語。スペースは不可、アンダースコアとダッシュは使用可能。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "タームスラッグ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "項目名"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "デフォルト項目"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "キーワード並び替え"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "投稿タイプを追加する"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "最初の投稿タイプを追加"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "高度な設定"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "階層的"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "一般公開"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "動画"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "小文字、アンダースコア、ダッシュのみ、最大20文字。"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "映画"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "単数ラベル"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "映画"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "複数ラベル"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "コントローラークラス"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "名前空間ルート"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "ベース URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "REST API で表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "クエリ変数名をカスタマイズ。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "クエリー可変"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "カスタムクエリー変数"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "クエリ変数のサポート"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "公開クエリ可"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "アーカイブスラッグ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "アーカイブ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "ページ送り"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "フィード URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "フロント URL プレフィックス"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "URL に使用されるスラッグをカスタマイズする。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL スラッグ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "カスタムパーマリンク"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "投稿タイプキー"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "パーマリンクのリライト"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "ユーザーと一緒に削除"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "エクスポート可能"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "複数の権限名"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "リネーム機能"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "検索から除外する"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "管理バーの表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "メニュー アイコン"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "メニューの位置"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "UI に表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "投稿へのリンク。"

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "リンク項目の説明"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "%s へのリンク。"

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "投稿リンク"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "項目のリンク"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s リンク"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "投稿を更新しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "項目を更新しました"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s を更新しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "投稿を予約しました."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "公開予約済み項目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s を予約しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "投稿を下書きに戻しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "下書きに戻された項目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s を下書きに戻しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "投稿を限定公開しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "限定公開された項目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "投稿を公開しました."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "公開済み項目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s を公開しました。"

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "投稿リスト"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "項目リスト"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s リスト"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "%s 日時で絞り込み"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "投稿リストの絞り込み"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "項目一覧の絞り込み"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "%s リストを絞り込み"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "この項目にアップロード"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "この %s にアップロードされました"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "投稿に挿入"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "%s に挿入"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "アイキャッチ画像を使用"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "アイキャッチ画像を削除"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "アイキャッチ画像を削除"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "アイキャッチ画像を設定"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "アイキャッチ画像を設定する際のボタンラベルとして"

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "アイキャッチ画像を設定"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "アイキャッチ画像"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "投稿の属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "属性メタボックス"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s の属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "投稿アーカイブ"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "ナビメニューをアーカイブする"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s アーカイブ"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "ゴミ箱に%sはありません"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "投稿が見つかりません"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "項目が見つかりませんでした"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "%s が見つかりませんでした。"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "投稿を検索"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "項目を検索"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "%s を検索"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "親ページ:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "親の%s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "新規投稿"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "新規項目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "新規 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "新規投稿を追加"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "新規項目を追加"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "新規%sを追加"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "投稿一覧を表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "アイテムを表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "投稿を表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "項目を表示"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s を表示"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "投稿の編集"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "項目を編集"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s を編集"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "投稿一覧"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "すべての項目"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "%s 一覧"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "投稿タイプの管理メニュー名。"

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "メニュー名"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "再生成"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "カスタムの追加"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "投稿フォーマット"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "エディター"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "トラックバック"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "フィールドを見る"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "インポート対象がありません"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "インポートした %s 項目"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "エクスポート"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "タクソノミーを選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "投稿タイプを選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "カテゴリー"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "タグ"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s タクソノミーが作成されました"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s タクソノミーの更新"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "タクソノミーを保存する。"

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "タクソノミーを削除しました。"

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "タクソノミーを更新しました。"

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "%s タクソノミーを複製しました。"

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "%s タクソノミーを有効化しました。"

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "規約"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "投稿タイプ %s が同期されました。"

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "投稿タイプ %s が複製されました。"

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "投稿タイプ %s が無効化されました。"

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "投稿タイプ %s が有効化されました。"

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "投稿タイプ"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "高度な設定"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "基本設定"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"この投稿タイプは、このキーが別のプラグインまたはテーマによって登録された別の"
"投稿タイプによって使用されているため、登録できませんでした。"

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "固定ページ"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s 投稿タイプを作成しました"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "フィールドの追加 %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s 投稿タイプを更新しました"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "投稿タイプを送信しました。"

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "投稿タイプを保存しました。"

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "投稿タイプを更新しました。"

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "投稿タイプが削除されました。"

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "入力して検索…"

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "PRO 限定"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:46 includes/admin/admin.php:352
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "タクソノミー"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "投稿タイプ"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "完了"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "フィールドグループ"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "1つまたは複数のフィールド グループを選択します..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "リンクするフィールドグループを選択してください。"

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "フィールドグループが正常にリンクされました。"

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "登録に失敗しました"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "パーミッション"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "可視性"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "ラベル"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "フィールド設定タブ"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "モーダルを閉じる"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "フィールドは他のグループに移動しました"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "モーダルを閉じる"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "新規タブグループ"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "他の選択肢を保存"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "他の選択肢を許可"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "すべてのトグルを追加"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "カスタム値を保存"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "カスタム値の許可"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "更新"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields ロゴ"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "変更内容を保存"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "フィールドグループタイトル"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "タイトルを追加"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "フィールドグループを追加する"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "最初のフィールドグループを追加"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "設定ページ"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF Blocks"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "ギャラリーフィールド"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "柔軟コンテンツフィールド"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "リピーターフィールド"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "フィールドグループを削除"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "グループ設定"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "ロケーションルール"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "最初のフィールドを追加"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "No."

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "フィールドを追加"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "プレゼンテーション"

#: includes/fields.php:383
msgid "Validation"
msgstr "検証"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "全般"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "JSON をインポート"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "JSON をエクスポート"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "無効化"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "この項目を無効化する"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "有効化"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "この項目を有効化する"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr ""

#: acf.php:501 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "無効"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:559
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields と Advanced Custom Fields PRO を同時に有効化しないでく"
"ださい。\n"
"Advanced Custom Fields PROを自動的に無効化しました。"

#: acf.php:557
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields と Advanced Custom Fields PRO を同時に有効化しないでく"
"ださい。\n"
"Advanced Custom Fields を自動的に無効化しました。"

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s は有効なユーザー ID である必要があります。"

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "無効なリクエストです。"

#: includes/fields/class-acf-field-select.php:635
msgid "%1$s is not one of %2$s"
msgstr "%1$s は %2$s に当てはまりません"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s はターム %2$s である必要があります。"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s は投稿タイプ %2$s である必要があります。"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s は有効な投稿 ID である必要があります。"

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s には有効な添付ファイル ID が必要です。"

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "REST API で表示"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "透明度の有効化"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA 配列"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA 文字列"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "16進値文字列"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "プロ版にアップグレード"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "有効"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' は有効なメールアドレスではありません"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "明度"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "デフォルトの色を選択"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "色をクリア"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "ブロック"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "オプション"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "ユーザー"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "メニュー項目"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "ウィジェット"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "添付ファイル"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "タクソノミー"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "投稿"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "最終更新日: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "このフィールドグループは diff 比較に使用できません。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "無効なフィールドグループパラメータ。"

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "保存待ち"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "保存しました"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "インポート"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "変更をレビュー"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "位置: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "プラグイン中の位置: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "テーマ内の位置: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "各種"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "変更を同期"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "差分を読み込み中"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "ローカルの JSON 変更をレビュー"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "サイトへ移動"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "詳細を表示"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "バージョン %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "情報"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ヘルプデスク</a>。サポートの専門家がお客様の"
"より詳細な技術的課題をサポートします。"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ディスカッション</a>。コミュニティフォーラム"
"には、活発でフレンドリーなコミュニティがあり、ACFの世界の「ハウツー」を理解す"
"る手助けをしてくれるかもしれません。"

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ドキュメンテーション</a>。私たちの豊富なド"
"キュメントには、お客様が遭遇する可能性のあるほとんどの状況に対するリファレン"
"スやガイドが含まれています。"

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"私たちはサポートを非常に重要視しており、ACF を使ったサイトを最大限に活用して"
"いただきたいと考えています。何か問題が発生した場合には、複数の場所でサポート"
"を受けることができます:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "ヘルプとサポート"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "お困りの際は「ヘルプとサポート」タブからお問い合わせください。"

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"初めてフィールドグループを作成する前にまず<a href=\"%s\" target=\"_blank\">ス"
"タートガイド</a>に目を通して、プラグインの理念やベストプラクティスを理解する"
"ことをおすすめします。"

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields プラグインは、WordPress の編集画面を追加フィールドでカ"
"スタマイズするためのビジュアルフォームビルダーと、カスタムフィールドの値を任"
"意のテーマテンプレートファイルに表示するための直感的な API を提供します。"

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "概要"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "位置タイプ「%s」はすでに登録されています。"

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "クラス \"%s\" は存在しません。"

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "無効な nonce。"

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "フィールドの読み込みエラー。"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>エラー</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "ウィジェット"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "ユーザー権限グループ"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "コメント"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "投稿フォーマット"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "メニュー項目"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "投稿ステータス"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "メニュー"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "メニューの位置"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "メニュー"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "投稿タクソノミー"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "子ページ (親ページあり)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "親ページ (子ページあり)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "最上位レベルのページ (親ページなし)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "投稿ページ"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "フロントページ"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "ページタイプ"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "バックエンドで表示"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "フロントエンドで表示"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "ログイン済み"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "現在のユーザー"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "固定ページテンプレート"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "登録"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "追加 / 編集"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "ユーザーフォーム"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "親ページ"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "特権管理者"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "現在のユーザー権限グループ"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "デフォルトテンプレート"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "投稿テンプレート"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "投稿カテゴリー"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "すべての%sフォーマット"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "添付ファイル"

#: includes/validation.php:313
msgid "%s value is required"
msgstr "%s の値は必須です"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "このフィールドグループの表示条件"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "条件判定"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "と"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "ローカル JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "フィールドを複製"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"また、すべてのプレミアムアドオン ( %s) が最新版に更新されていることを確認して"
"ください。"

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"このバージョンにはデータベースの改善が含まれており、アップグレードが必要で"
"す。"

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "%1$s v%2$sへの更新をありがとうございます。"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "データベースのアップグレードが必要"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "オプションページ"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "ギャラリー"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "柔軟なコンテンツ"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "繰り返し"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "すべてのツールに戻る"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"複数のフィールドグループが編集画面に表示される場合、最初のフィールドグループ "
"(最小の番号を持つもの) のオプションが使用されます"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "編集画面で<b>非表示</b>にする項目を<b>選択して</b>ください。"

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "画面上で非表示"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "トラックバック送信"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "タグ"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "カテゴリー"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "ページ属性"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "フォーマット"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "投稿者"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "スラッグ"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "リビジョン"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "コメント"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "ディスカッション"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "抜粋"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "コンテンツエディター"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "パーマリンク"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "フィールドグループリストに表示"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "下位のフィールドグループを最初に表示"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "注文番号"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "フィールドの下"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "ラベルの下"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "手順の配置"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "ラベルの配置"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "サイド"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "通常 (コンテンツの後)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "高 (タイトルの後)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "位置"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "シームレス (メタボックスなし)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "標準 (WP メタボックス)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "スタイル"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "タイプ"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "キー"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "順序"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "フィールドを閉じる"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "ID"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "クラス"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "横幅"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "ラッパー属性"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "必須項目"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "手順"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "フィールドタイプ"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "スペースは不可、アンダースコアとダッシュは使用可能"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "フィールド名"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "これは、編集ページに表示される名前です"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "フィールドラベル"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "削除"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "フィールドを削除"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "移動"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "フィールドを別のグループへ移動"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "フィールドを複製"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "フィールドを編集"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "ドラッグして順序を変更"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "このフィールドグループを表示する条件"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "利用可能な更新はありません。"

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"データベースのアップグレードが完了しました。<a href=\"%s\">変更点を表示</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "アップグレードタスクを読み込んでいます..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "アップグレードに失敗しました。"

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "アップグレードが完了しました。"

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "データをバージョン%sへアップグレード中"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"続行する前にデータベースをバックアップすることを強くおすすめします。本当に更"
"新ツールを今すぐ実行してもよいですか ?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "アップグレードするサイトを1つ以上選択してください。"

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"データベースのアップグレードが完了しました。<a href=\"%s\">ネットワークダッ"
"シュボードに戻る</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "サイトは最新状態です"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "%1$s から %2$s へのデータベースのアップグレードが必要です"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "サイト"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "サイトをアップグレード"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"以下のサイトはデータベースのアップグレードが必要です。更新したいものにチェッ"
"クを入れて、%s をクリックしてください。"

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "ルールグループを追加"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"どの編集画面でカスタムフィールドを表示するかを決定するルールを作成します"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "ルール"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "コピーしました"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "クリップボードにコピー"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"エクスポートしたい項目とエクスポート方法を選んでください。「JSON としてエクス"
"ポート」では別の ACF をインストールした環境でインポートできる JSON ファイルが"
"エクスポートされます。「PHP の生成」ではテーマ内で利用できる PHP コードが生成"
"されます。"

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "フィールドグループを選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "フィールド未選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "PHP を生成"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "フィールドグループをエクスポート"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "空ファイルのインポート"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "不正なファイルの種類"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "ファイルアップロードエラー。もう一度お試しください"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"インポートしたい ACF の JSON ファイルを選択してください。下のインポートボタン"
"をクリックすると、ACF はファイルに項目をインポートします。"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "フィールドグループをインポート"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "同期"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "%sを選択"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "複製"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "この項目を複製"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "サポート"

#: includes/admin/admin.php:346
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "ドキュメンテーション"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "説明"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "同期が利用できます"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "%s件のフィールドグループを同期しました。"

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s件のフィールドグループを複製しました。"

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "使用中 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "サイトをレビューしてアップグレード"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "データベースをアップグレード"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "カスタムフィールド"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "フィールドを移動"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "このフィールドの移動先を選択してください"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s フィールドは現在 %2$s フィールドグループにあります"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "移動が完了しました。"

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "有効"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "フィールドキー"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "設定"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "位置"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "コピー"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(このフィールド)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "チェック済み"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "カスタムフィールドを移動"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "利用可能な切り替えフィールドがありません"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "フィールドグループのタイトルは必須です"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "変更を保存するまでこのフィールドは移動できません"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" という文字列はフィールド名の先頭に使うことはできません"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "フィールドグループ下書きを更新しました。"

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "フィールドグループを公開予約しました。"

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "フィールドグループを送信しました。"

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "フィールドグループを保存しました。"

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "フィールドグループを公開しました。"

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "フィールドグループを削除しました。"

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "フィールドグループを更新しました。"

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "ツール"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "等しくない"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "等しい"

#: includes/locations.php:104
msgid "Forms"
msgstr "フォーム"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "固定ページ"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "投稿"

#: includes/fields.php:328
msgid "Relational"
msgstr "関連"

#: includes/fields.php:327
msgid "Choice"
msgstr "選択"

#: includes/fields.php:325
msgid "Basic"
msgstr "基本"

#: includes/fields.php:276
msgid "Unknown"
msgstr "不明"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "フィールドタイプが存在しません"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "スパムを検出しました"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "投稿を更新しました"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "更新"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "メールを確認"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "コンテンツ"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "タイトル"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "フィールドグループを編集"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "選択範囲が以下より小さい場合"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "選択範囲が以下より大きい場合"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "値が以下より小さい場合"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "値が以下より大きい場合"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "以下の値が含まれる場合"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "値が以下のパターンに一致する場合"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "値が以下に等しくない場合"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "値が以下に等しい場合"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "値がない場合"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "任意の値あり"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "キャンセル"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "本当に実行しますか ?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d個のフィールドで確認が必要です"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1つのフィールドで確認が必要です"

#: includes/assets.php:368 includes/validation.php:247
#: includes/validation.php:255
msgid "Validation failed"
msgstr "検証失敗"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "検証成功"

#: includes/media.php:54
msgid "Restricted"
msgstr "制限"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "詳細を折りたたむ"

#: includes/media.php:52
msgid "Expand Details"
msgstr "詳細を展開"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "この投稿へのアップロード"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "更新"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "編集"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "このページから移動した場合、変更は失われます"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "ファイル形式は %s である必要があります。"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "または"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "ファイルサイズは %s 以下である必要があります。"

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "ファイルサイズは %s 以上である必要があります。"

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "画像の高さは %dpx 以下である必要があります。"

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "画像の高さは %dpx 以上である必要があります。"

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "画像の幅は %dpx 以下である必要があります。"

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "画像の幅は %dpx 以上である必要があります。"

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(タイトルなし)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "フルサイズ"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "大"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "中"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "サムネイル"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(ラベルなし)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "テキストエリアの高さを設定"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "行"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "テキストエリア"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "追加のチェックボックスを先頭に追加して、すべての選択肢を切り替えます"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "フィールドの選択肢として「カスタム」を保存する"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "「カスタム」値の追加を許可する"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "新規選択肢を追加"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "すべて切り替え"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "アーカイブ URL を許可"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "アーカイブ"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "ページリンク"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "追加"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "名前"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s を追加しました"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s はすでに存在しています"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "ユーザーが新規 %s を追加できません"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ターム ID"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "タームオブジェクト"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "投稿タームから値を読み込む"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "タームを読み込む"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "選択したタームを投稿に関連付ける"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "タームを保存"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "編集中に新しいタームを作成できるようにする"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "タームを追加"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "ラジオボタン"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "単一値"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "複数選択"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "チェックボックス"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "複数値"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "このフィールドの外観を選択"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "外観"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "表示するタクソノミーを選択"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "%s なし"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "値は%d文字以下である必要があります"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "値は%d文字以上である必要があります"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "値は数字である必要があります"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "番号"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "フィールドの選択肢として「その他」を保存する"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "「その他」の選択肢を追加してカスタム値を許可"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "その他"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "ラジオボタン"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"前のアコーディオンを停止するエンドポイントを定義します。このアコーディオンは"
"表示されません。"

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr ""
"他のアコーディオンを閉じずにこのアコーディオンを開くことができるようにする。"

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "マルチ展開"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "このアコーディオンをページの読み込み時に開いた状態で表示します。"

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "受付中"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "アコーディオン"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "アップロード可能なファイルを制限"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ファイル ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "ファイルの URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "ファイル配列"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "ファイルを追加"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "ファイルが選択されていません"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "ファイル名"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "ファイルを更新"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "ファイルを編集"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "ファイルを選択"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "ファイル"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "パスワード"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "戻り値を指定します"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "AJAX を使用して選択肢を遅延読み込みしますか ?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "新しい行に各デフォルト値を入力してください"

#: includes/fields/class-acf-field-select.php:227 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "読み込み失敗"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "検索中&hellip;"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "結果をさらに読み込み中&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "%d項目のみ選択可能です"

#: includes/fields/class-acf-field-select.php:96
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "1項目のみ選択可能です"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "%d文字を削除してください"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "1文字削除してください"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "%d文字以上を入力してください"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "1つ以上の文字を入力してください"

#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "一致する項目がありません"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:88
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d件の結果が見つかりました。上下矢印キーを使って移動してください。"

#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "1件の結果が利用可能です。Enter を押して選択してください。"

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ユーザー ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "ユーザーオブジェクト"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "ユーザー配列"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "すべてのユーザー権限グループ"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "権限グループで絞り込む"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "ユーザー"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "区切り"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "色を選択"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "デフォルト"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "クリア"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "カラーピッカー"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "完了"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "現在"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "タイムゾーン"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "マイクロ秒"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "ミリ秒"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "秒"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "分"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "時"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "時間"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "時間を選択"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "日時選択ツール"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "エンドポイント"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "左揃え"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "上揃え"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "配置"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "タブ"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "値は有効な URL である必要があります"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "リンク URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "リンク配列"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "新しいウィンドウまたはタブで開く"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "リンクを選択"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "リンク"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "メール"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "ステップサイズ"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "最大値"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "最小値"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "範囲"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "両方 (配列)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "ラベル"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "値"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "垂直"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "水平"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr "下記のように記述すると、値とラベルの両方を制御することができます:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "選択肢を改行で区切って入力してください。"

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "選択肢"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "ボタングループ"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "空の値を許可"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "親"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "フィールドがクリックされるまで TinyMCE は初期化されません"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "初期化を遅延させる"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "メディアアップロードボタンを表示"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "ツールバー"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "テキストのみ"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "ビジュアルのみ"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "ビジュアルとテキスト"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "タブ"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "クリックして TinyMCE を初期化"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "テキスト"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "ビジュアル"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "値は%d文字以内である必要があります"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "制限しない場合は空白にする"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "文字数制限"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "入力内容の後に表示"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "追加"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "入力内容の前に表示"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "先頭に追加"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "入力内容の中に表示"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "プレースホルダーテキスト"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "新規投稿作成時に表示"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "テキスト"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$sは%2$s個以上選択する必要があります"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "投稿 ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "投稿オブジェクト"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "最大投稿数"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "最小投稿数"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "アイキャッチ画像"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "選択した要素がそれぞれの結果に表示されます"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "要素"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "タクソノミー"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "投稿タイプ"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "フィルター"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "すべてのタクソノミー"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "タクソノミーで絞り込み"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "すべての投稿タイプ"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "投稿タイプでフィルター"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "検索…"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "タクソノミーを選択"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "投稿タイプを選択"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "一致する項目がありません"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "読み込み中"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "最大値 ({max}) に達しました"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "関係"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "カンマ区切りのリスト。すべてのタイプを許可する場合は空白のままにします"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "許可されるファイルの種類"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "最大"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "ファイルサイズ"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "アップロード可能な画像を制限"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "最小"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "投稿にアップロード"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "すべて"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "メディアライブラリの選択肢を制限"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "ライブラリ"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "プレビューサイズ"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "画像 ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "画像 URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "画像配列"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "フロントエンドへの返り値を指定してください"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "返り値"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "画像を追加"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "画像が選択されていません"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "削除"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "編集"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "すべての画像"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "画像を更新"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "画像を編集"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "画像を選択"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "画像"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "HTML マークアップのコードとして表示を許可"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "HTML をエスケープ"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "書式設定なし"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "自動的に &lt;br&gt; を追加"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "自動的に段落追加する"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "改行をどのように表示するか制御"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "改行"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "週の始まり"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "値を保存するときに使用される形式"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "書式を保存"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "前へ"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "次へ"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "今日"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "完了"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "日付選択ツール"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "幅"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "埋め込みサイズ"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "URL を入力"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "無効化時に表示されるテキスト"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "無効化時のテキスト"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "有効時に表示するテキスト"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "アクティブ時のテキスト"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "スタイリッシュな UI"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "初期値"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "チェックボックスの横にテキストを表示"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "メッセージ"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
msgid "No"
msgstr "いいえ"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
msgid "Yes"
msgstr "はい"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "真/偽"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "行"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "テーブル"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "ブロック"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "選択したフィールドのレンダリングに使用されるスタイルを指定します"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "レイアウト"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "サブフィールド"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "グループ"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "地図の高さをカスタマイズ"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "高さ"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "地図のデフォルトズームレベルを設定"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "ズーム"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "地図のデフォルト中心位置を設定"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "中央"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "住所を検索…"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "現在の場所を検索"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "位置情報をクリア"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "検索"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "お使いのブラウザーは位置情報機能に対応していません"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google マップ"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "テンプレート関数で返されるフォーマット"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "戻り値の形式"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "カスタム:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "投稿編集時に表示される書式"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "表示形式"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "時間選択ツール"

#. translators: counts for inactive field groups
#: acf.php:507
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "停止中 <span class=\"count\">(%s)</span>"

#: acf.php:468
msgid "No Fields found in Trash"
msgstr "ゴミ箱にフィールドが見つかりません"

#: acf.php:467
msgid "No Fields found"
msgstr "フィールドが見つかりません"

#: acf.php:466
msgid "Search Fields"
msgstr "フィールドを検索"

#: acf.php:465
msgid "View Field"
msgstr "フィールドを表示"

#: acf.php:464 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "新規フィールド"

#: acf.php:463
msgid "Edit Field"
msgstr "フィールドを編集"

#: acf.php:462
msgid "Add New Field"
msgstr "新規フィールドを追加"

#: acf.php:460
msgid "Field"
msgstr "フィールド"

#: acf.php:459 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "フィールド"

#: acf.php:434
msgid "No Field Groups found in Trash"
msgstr "ゴミ箱にフィールドグループが見つかりません"

#: acf.php:433
msgid "No Field Groups found"
msgstr "フィールドグループが見つかりません"

#: acf.php:432
msgid "Search Field Groups"
msgstr "フィールドグループを検索"

#: acf.php:431
msgid "View Field Group"
msgstr "フィールドグループを表示"

#: acf.php:430
msgid "New Field Group"
msgstr "新規フィールドグループ"

#: acf.php:429
msgid "Edit Field Group"
msgstr "フィールドグループを編集"

#: acf.php:428
msgid "Add New Field Group"
msgstr "新規フィールドグループを追加"

#: acf.php:427 acf.php:461
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "新規追加"

#: acf.php:426
msgid "Field Group"
msgstr "フィールドグループ"

#: acf.php:425 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "フィールドグループ"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"パワフル、プロフェッショナル、直感的なフィールドで WordPress をカスタマイズ。"

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "オプションを更新しました"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "再確認"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "公開"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"このオプションページにカスタムフィールドグループがありません. <a href=\"%s\">"
"カスタムフィールドグループを作成</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>エラー</b> 更新サーバーに接続できません"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "表示"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "行を追加"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "レイアウト"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "レイアウト"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "{identifier}に{label}は最低{min}個必要です"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""
"あと{available}個 {identifier}には {label} を利用できます（最大 {max}個）"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""
"あと{required}個 {identifier}には {label} を利用する必要があります（最小 "
"{max}個）"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "柔軟コンテンツは少なくとも1個のレイアウトが必要です"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "下の \"%s\" ボタンをクリックしてレイアウトの作成を始めてください"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "レイアウトを追加"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "レイアウトを削除"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "レイアウトを削除"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "レイアウトを複製"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "新しいレイアウトを追加"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "レイアウトを追加"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "最小数"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "最大数"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "レイアウトの最小数"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "レイアウトの最大数"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "ボタンのラベル"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "ギャラリーに画像を追加"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "選択の最大数に到達しました"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "長さ"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "ギャラリーを追加"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "一括操作"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "アップロード日で並べ替え"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "変更日で並び替え"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "タイトルで並び替え"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "並び順を逆にする"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "閉じる"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "最小選択数"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "最大選択数"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "許可するファイルタイプ"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "最小行数に達しました（{min} 行）"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "最大行数に達しました（{max} 行）"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "最大行数"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "ドラッグして並び替え"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "行を追加"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "行を削除"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "フロントページ"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "投稿ページ"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "フロントページ"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "投稿ページ"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "オプションページはありません"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "ライセンスのアクティベートを解除"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "ライセンスをアクティベート"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "ライセンスキー"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "アップデート情報"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "現在のバージョン"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "最新のバージョン"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "利用可能なアップデート"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "アップグレード通知"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "アップデートのロックを解除するためにライセンスキーを入力してください"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "プラグインをアップデート"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
