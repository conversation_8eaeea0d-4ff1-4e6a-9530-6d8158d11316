(()=>{var e={734:()=>{!function(e){var t=acf.FieldSetting.extend({type:"clone",name:"display",render:function(){var e=this.field.val();this.$fieldObject.attr("data-display",e)}});acf.registerFieldSetting(t);var a=acf.FieldSetting.extend({type:"clone",name:"prefix_label",render:function(){var e="";this.field.val()&&(e=this.fieldObject.prop("label")+" "),this.$("code").html(e+"%field_label%")}});acf.registerFieldSetting(a);var l=acf.FieldSetting.extend({type:"clone",name:"prefix_name",render:function(){var e="";this.field.val()&&(e=this.fieldObject.prop("name")+"_"),this.$("code").html(e+"%field_name%")}});acf.registerFieldSetting(l),new acf.Model({filters:{select2_args:"select2Args"},select2Args:function(e,t,a,l,i){return"acf/fields/clone/query"==a.ajaxAction&&(e.closeOnSelect=!1,i.data.ajaxData=this.ajaxData),e},ajaxData:function(t){return t.fields={},acf.getFieldObjects().map((function(e){t.fields[e.prop("key")]={key:e.prop("key"),type:e.prop("type"),label:e.prop("label"),ancestors:e.getParents().length}})),t.title=e("#title").val(),t}})}(jQuery)},368:()=>{var e,t;e=jQuery,t=acf.FieldSetting.extend({type:"flexible_content",name:"fc_layout",events:{"blur .layout-label":"onChangeLabel","blur .layout-name":"onChangeName","click .add-layout":"onClickAdd","click .acf-field-settings-fc_head":"onClickEdit","click .acf-field-setting-fc-duplicate":"onClickDuplicate","click .acf-field-setting-fc-delete":"onClickDelete","changed:layoutLabel":"updateLayoutTitles","changed:layoutName":"updateLayoutTitles"},$input:function(t){return e("#"+this.getInputId()+"-"+t)},$list:function(){return this.$(".acf-field-list:first")},getInputId:function(){return this.fieldObject.getInputId()+"-layouts-"+this.field.get("id")},getFields:function(){return acf.getFieldObjects({parent:this.$el})},getChildren:function(){return acf.getFieldObjects({list:this.$list()})},initialize:function(){var e=this.$el.parent();e.css("position","relative"),e.hasClass("ui-sortable")||e.sortable({items:"> .acf-field-setting-fc_layout",handle:".acf-fc_draggable",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:this.proxy((function(e,t){this.fieldObject.save()}))}),this.updateFieldLayouts(),this.updateLayoutTitles()},updateFieldLayouts:function(){this.getChildren().map(this.updateFieldLayout,this)},updateFieldLayout:function(e){e.prop("parent_layout",this.get("id"))},updateLayoutTitles:function(){const e=this.get("layoutLabel"),t=this.get("layoutName"),a=this.$el.find("> .acf-label .acf-fc-layout-label");e&&a.text(acf.decode(e));const l=this.$el.find("> .acf-label .acf-fc-layout-name span");t?(l.text(t),l.parent().css("display","")):l.parent().css("display","none")},onClickEdit:function(t){const a=e(t.target);a.hasClass("acf-btn")||a.hasClass("copyable")||a.parent().hasClass("acf-btn")||a.parent().hasClass("copyable")||(this.isOpen()?this.close():this.open())},isOpen:function(e){return this.$el.children(".acf-field-layout-settings").hasClass("open")},open:function(e,t){const a=e?e.children(".acf-field-settings-fc_head"):this.$el.children(".acf-field-settings-fc_head"),l=e?e.children(".acf-field-layout-settings"):this.$el.children(".acf-field-layout-settings"),i=e?e.find(".toggle-indicator").first():this.$el.find(".toggle-indicator").first();acf.doAction("show",l),t?l.slideDown({complete:function(){l.find(".layout-label").trigger("focus")}}):l.slideDown(),i.addClass("open"),i.hasClass("closed")&&i.removeClass("closed"),l.addClass("open"),a.addClass("open")},close:function(){const e=this.$el.children(".acf-field-settings-fc_head"),t=this.$el.children(".acf-field-layout-settings"),a=this.$el.find(".toggle-indicator").first();t.slideUp(),t.removeClass("open"),a.removeClass("open"),e.removeClass("open"),a.hasClass("closed")||a.addClass("closed"),acf.doAction("hide",t)},onChangeLabel:function(e,t){let a=t.val();const l=acf.encode(a);this.set("layoutLabel",l),this.$el.attr("data-layout-label",l);let i=this.$input("name");""==i.val()&&(acf.val(i,acf.strSanitize(a)),this.$el.find(".layout-name").trigger("blur"))},onChangeName:function(e,t){const a=acf.strSanitize(t.val(),!1);t.val(a),this.set("layoutName",a),this.$el.attr("data-layout-name",a)},onClickAdd:function(e,t){e.preventDefault();var a=this.get("id"),l=acf.uniqid("layout_");$layout=acf.duplicate({$el:this.$el,search:a,replace:l,after:function(e,t){var a=t.find(".acf-field-list:first");a.children(".acf-field-object").remove(),a.addClass("-empty"),t.attr("data-layout-label",""),t.attr("data-layout-name",""),t.find(".acf-fc-meta input").val(""),t.find("label.acf-fc-layout-label").html(acf.__("Layout"))}});var i=acf.getFieldSetting($layout);i.$input("key").val(l),this.isOpen()?i.$el.find(".layout-label").trigger("focus"):this.open(i.$el,!0),this.fieldObject.save()},onClickDuplicate:function(e,t){e.preventDefault();var a=this.get("id"),l=acf.uniqid("layout_");$layout=acf.duplicate({$el:this.$el,search:a,replace:l});var i=acf.getFieldObjects({parent:$layout});i.length&&(i.map((function(e){e.wipe(),e.isOpen()&&e.open(),e.updateParent()})),acf.doAction("duplicate_field_objects",i,this.fieldObject,this.fieldObject));var n=acf.getFieldSetting($layout),s=n.get("layoutLabel"),c=n.get("layoutName"),o=c.split("_").pop(),r=acf.__("copy");if(acf.isNumeric(o)){var f=1*o+1;s=s.replace(o,f),c=c.replace(o,f)}else 0===o.indexOf(r)?(f=(f=1*o.replace(r,""))?f+1:2,s=s.replace(o,r+f),c=c.replace(o,r+f)):(s+=" ("+r+")",c+="_"+r);n.$input("label").val(s),n.set("layoutLabel",s),n.$el.attr("data-layout-label",s),n.$input("name").val(c),n.set("layoutName",c),n.$el.attr("data-layout-name",c),n.$input("key").val(l),this.isOpen()?n.$el.find(".layout-label").trigger("focus"):this.open(n.$el,!0),this.fieldObject.save()},onClickDelete:function(e,t){if(e.preventDefault(),e.shiftKey)return this.delete();this.$el.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:t,context:this,confirm:function(){this.delete()},cancel:function(){this.$el.removeClass("-hover")}})},delete:function(){if(!this.$el.siblings(".acf-field-setting-fc_layout").length)return alert(acf.__("Flexible Content requires at least 1 layout")),!1;this.getFields().map((function(e){e.delete({animate:!1})})),acf.remove(this.$el),this.fieldObject.save()}}),acf.registerFieldSetting(t),new acf.Model({actions:{sortstop_field_object:"updateParentLayout",change_field_object_parent:"updateParentLayout"},updateParentLayout:function(e){var t=e.getParent();if(t&&"flexible_content"===t.prop("type")){var a=e.$el.closest(".acf-field-setting-fc_layout"),l=acf.getFieldSetting(a);e.has("parent_layout")||e.prop("parent_layout",0),e.prop("parent_layout",l.get("id"))}else e.prop("parent_layout",null)}})},195:()=>{var e;jQuery,e=acf.FieldSetting.extend({type:"repeater",name:"collapsed",events:{"focus select":"onFocus"},onFocus:function(e,t){var a=t,l=[];l.push({label:a.find('option[value=""]').text(),value:""});var i=this.fieldObject.$(".acf-field-list:first");acf.getFieldObjects({list:i}).map((function(e){l.push({label:e.prop("label"),value:e.prop("key")})})),acf.renderSelect(a,l)}}),acf.registerFieldSetting(e)}},t={};function a(l){var i=t[l];if(void 0!==i)return i.exports;var n=t[l]={exports:{}};return e[l](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var l in t)a.o(t,l)&&!a.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";a(195),a(368),a(734)})()})();