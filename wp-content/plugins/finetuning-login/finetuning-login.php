<?php
defined('ABSPATH') || exit;

/**
 * Plugin Name: Login Page
 * Author: <PERSON><PERSON>
 * Text Domain: login
 * Description: Advanced Login page
 */
/*// Filter & Function to rename the WordPress login URL
add_filter( 'login_url', 'my_login_page', 10, 3 );
function my_login_page( $login_url, $redirect, $force_reauth ) {
	$login_page = home_url( '/login' );   // The name of your new login file
	$login_url  = add_query_arg( 'redirect_to', $redirect, $login_page );

	return $login_url;
}*/

class FineTuningLogin
{
    public static function init()
    {
        add_action('wp_ajax_ftlogin', [__CLASS__, 'login']);
        add_action('wp_ajax_nopriv_ftlogin', [__CLASS__, 'login']);
        add_action('wp_ajax_ft_forgot_password', [__CLASS__, 'forgot_password']);
        add_action('wp_ajax_nopriv_ft_forgot_password', [__CLASS__, 'forgot_password']);
        add_action('wp_head', [__CLASS__, 'head']);
        add_action('wp_footer', [__CLASS__, 'footer']);
        add_shortcode('ftlogin', [__CLASS__, 'view']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'assets']);
        add_filter('lostpassword_url', [__CLASS__, 'lost_password_page'], 1000, 0);
        add_action('template_redirect', [__CLASS__, 'loginAuth']);
    }

    public static function loginAuth()
    {
        global $post;
        if (!empty($post->ID) && $post->ID == 5143) {
            if (is_user_logged_in()) {
                if (current_user_can('administrator')) {
                    wp_redirect(home_url('/wp-admin'));
                    die;
                }

                wp_redirect(home_url('/user'));
                die;
            }
        }
    }

    public static function lost_password_page()
    {
        return site_url('/password-reset/');
    }

    public static function login()
    {
        global $wpdb;

        $data['user_login'] = sanitize_text_field($_POST['user_login'] ?? '');
        $data['password'] = sanitize_text_field($_POST['password'] ?? '');

        $wpdb->insert(
            $wpdb->prefix . 'login_log',
            [
                'form' => 'login',
                'name' => $data['user_login'],
                'email' => $data['user_login'] ?? '',
                'user_data' => serialize($data),
                'created_at' => current_time('mysql', 1),
            ],
            [
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
            ]
        );

        if (in_array(null, $data)) {
            wp_send_json_error('כתובת דוא"ל או סיסמה אינם נכונים');
        }

        $data['remember'] = sanitize_text_field($_POST['remember'] ?? '');

        if (!is_email($data['user_login'])) {
            wp_send_json_error('כתובת דוא"ל אינה חוקית');
        }

        $signin = wp_signon([
            'user_login' => $data['user_login'],
            'user_password' => $data['password'],
            'remember' => !empty($data['remember'])
        ]);

        if (is_wp_error($signin)) {
            $wpdb->insert(
                $wpdb->prefix . 'login_log',
                [
                    'form' => 'login',
                    'name' => $data['user_login'],
                    'email' => $data['user_login'] ?? '',
                    'user_data' => json_encode($signin),
                    'created_at' => current_time('mysql', 1),
                ],
                [
                    '%s',
                    '%s',
                    '%s',
                    '%s',
                    '%s',
                ]
            );
            if (strpos($signin->get_error_message(), 'The username or password you entered is incorrec') !== false) {
                wp_send_json_error("<strong>שגיאה</strong>: שם משתמש או סיסמה אינם נכונים <a class='d-block' href=\"" . site_url() . "/password-reset/\" title=\"שכחתי סיסמה\">שכחתי סיסמה</a>");
            }
            wp_send_json_error($signin->get_error_message());
        }

        $wpdb->insert(
            $wpdb->prefix . 'login_log',
            [
                'form' => 'login',
                'name' => $data['user_login'],
                'email' => $data['user_login'] ?? '',
                'user_data' => serialize($_POST),
                'created_at' => current_time('mysql', 1),
            ],
            [
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
            ]
        );

        wp_set_current_user($signin->ID);
        wp_set_auth_cookie($signin->ID);
        wp_send_json_success([
            'msg' => __('התחברת בהצלחה, מתחברים...'),
            'redirect' => get_permalink(7593),
        ]);
    }

    public static function forgot_password()
    {
        $email = sanitize_email($_POST['user_email'] ?? '');

        if (empty($email) || !is_email($email)) {
            wp_send_json_error('כתובת דוא"ל אינה תקינה');
        }

        $user = get_user_by('email', $email);
        if (!$user) {
            wp_send_json_error('כתובת דוא"ל לא נמצאה במערכת');
        }

        $reset_key = get_password_reset_key($user);
        if (is_wp_error($reset_key)) {
            wp_send_json_error('שגיאה ביצירת קישור איפוס');
        }

        $reset_url = network_site_url("wp-login.php?action=rp&key=$reset_key&login=" . rawurlencode($user->user_login), 'login');

        $subject = 'איפוס סיסמה - ' . get_bloginfo('name');
        $message = "שלום {$user->display_name},\n\n";
        $message .= "קיבלנו בקשה לאיפוס הסיסמה שלך.\n\n";
        $message .= "לחץ על הקישור הבא כדי לאפס את הסיסמה:\n";
        $message .= $reset_url . "\n\n";
        $message .= "אם לא ביקשת איפוס סיסמה, התעלם מהודעה זו.\n\n";
        $message .= "תודה,\n" . get_bloginfo('name');

        if (wp_mail($email, $subject, $message)) {
            wp_send_json_success('קישור לאיפוס סיסמה נשלח לכתובת המייל שלך');
        } else {
            wp_send_json_error('שגיאה בשליחת המייל');
        }
    }

    public static function view()
    {
        ob_start();
        ?>
        <div class="position-relative overflow-hidden">
            <section class="container py-5 my-lg-5">
                <div class="row align-items-center">
                    <div class="col-md-6 pe-md-5 mb-5 mb-md-0">
                        <form action="#" class="ftlogin" method="post">
                            <input type="hidden" name="action" value="ftlogin">
                            <h2 class="fw-bold">התחברות</h2>
                            <div class="row thickborder">
                                <div class="col-12 mb-3">
                                    <input type="email" class="form-control rounded-pill" name="user_login"
                                           placeholder="כתובת מייל">
                                </div>
                                <div class="col-12 mb-3">
                                    <input type="password" class="form-control rounded-pill" name="password"
                                           placeholder="סיסמה">
                                </div>
                                <div class="col-12 my-3">
                                    <button class="btn-hover color-2 px-5 fs-3 w-100">כניסה</button>
                                </div>
                                <div class="col-12 text-center">
                                    <a href="#" class="text-muted fw-semibold fs-5 forgot-password-link"
                                       data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">שכחתם סיסמה?</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <img src="<?= get_template_directory_uri() ?>/assets/images/login.png" alt=""
                             class="img-fluid rounded-4">
                    </div>
                </div>
            </section>
            <img src="<?= get_template_directory_uri() ?>/assets/images/dancing-lady.png" alt="Dance Lady"
                 style="transform: rotateY(180deg) translateX(-20%) translateY(5%);"
                 class="position-absolute top-0 h-100 z-n1">
        </div>
        <div class="marquee-wrapper bggrad-light py-3">
            <div class="marquee">
                <div class="marquee-content">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                    <h2>סטודיו מקוון</h2>
                    <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
                </div>
            </div>
        </div>

        <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">איפוס סיסמה</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form class="forgot-password-form">
                            <input type="hidden" name="action" value="ft_forgot_password">
                            <div class="mb-3">
                                <label class="form-label">כתובת מייל</label>
                                <input type="email" class="form-control rounded-pill" name="user_email" required>
                            </div>
                            <button type="submit" class="btn-hover color-2 px-5 fs-3 w-100">שליחה</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public static function footer()
    {
        ?>
        <script>
            (function ($) {
                $('form.ftlogin').on('submit', function (e) {
                    e.preventDefault();

                    let $this = $(this),
                        password = $this.find('input[type=password]'),
                        email = $this.find('input[type=email]'),
                        formData = $this.serialize(),
                        error = false;

                    $this.find('.text-danger').remove();

                    if (email.val().length <= 2) {
                        email.after('<span class="text-danger">כתובת מייל אינה תקינה</span>')
                        error = true;
                    }

                    if (password.val().length < 4) {
                        password.after('<span class="text-danger">סיסמה קצרה מידי.</span>')
                        error = true;
                    }

                    if (error) {
                        return false;
                    }

                    $.ajax({
                        url: '<?= admin_url('admin-ajax.php'); ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: formData,
                        beforeSend: function () {
                            $this.find('button[type=submit]').text('מתחברים...:)');
                            $this.find('button[type=submit]').prop('disabled', true);
                        },
                        complete: function () {
                            $this.find('button[type=submit]').text('התחברי');
                            $this.find('button[type=submit]').prop('disabled', false);
                        },
                        success: function (json) {
                            if (!json.success) {
                                Swal.fire({
                                    title: 'מצטערים',
                                    icon: 'info',
                                    html: json.data,
                                    focusConfirm: false,
                                    showCancelButton: false,
                                    showCloseButton: true,
                                    confirmButtonColor: "#FA10C8",
                                    confirmButtonText: 'סגירה',
                                    allowEscapeKey: true,
                                    allowOutsideClick: true
                                });
                            } else if (json.success) {
                                Swal.fire({
                                    position: "center",
                                    icon: "success",
                                    title: json.data.msg,
                                    showConfirmButton: false,
                                    // timer: 3000
                                });
                                window.location.href = json.data.redirect;
                            }
                        },
                        error: function () {
                            $this.find('button[type=submit]').prop('disabled', false);
                        }
                    });

                    return false;
                });

                // Forgot password form
                $('.forgot-password-form').on('submit', function (e) {
                    e.preventDefault();
                    let $form = $(this);
                    let formData = $form.serialize();

                    $form.find('.alert').remove();

                    $.ajax({
                        url: '<?= admin_url('admin-ajax.php'); ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: formData,
                        beforeSend: function () {
                            $form.find('button[type=submit]').text('שולח...').prop('disabled', true);
                        },
                        complete: function () {
                            $form.find('button[type=submit]').text('שלח קישור איפוס').prop('disabled', false);
                        },
                        success: function (json) {
                            if (json.success) {
                                $form.html('<div class="alert alert-success">' + json.data + '</div>');
                            } else {
                                $form.find('button').after('<div class="alert alert-danger mt-2">' + json.data + '</div>');
                            }
                        }
                    });
                });
            })(jQuery);</script>
        <?php
    }

    public static function head()
    {
        if (('page-login.php')) :
            ob_start();
            ?>
            <?php
            echo ob_get_clean();
        endif;
    }

    public static function assets()
    {
        wp_enqueue_script('sweetalert-script', 'https://cdn.jsdelivr.net/npm/sweetalert2@10', ['jquery'], 10, false);
    }
}

FineTuningLogin::init();
