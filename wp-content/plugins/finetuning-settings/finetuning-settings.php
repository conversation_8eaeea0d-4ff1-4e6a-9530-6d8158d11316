<?php
/**
 * Plugin Name: Finetuning settings
 * Author: <PERSON><PERSON>
 * Text Domain: finetuning-settings
 * Description: Finetuning settings - Do not remove this plugin!
 */

class FineTuningSettings {
	public static function init() {
		add_action( 'wp_enqueue_scripts', [ __CLASS__, 'assets' ] );
		add_action( 'wp_head', [ __CLASS__, 'site_header' ] );
	}

	public static function site_header() {
		ob_start();
		?>
        <meta name="google-site-verification" content="a56yTuw8YTCDbVEdSk32fuIql0sK9FwcHxN1oHbSios"/>
		<?php
		echo ob_get_clean();
	}

	public static function assets() {
		wp_enqueue_style( 'ft-settings-style', plugin_dir_url( __FILE__ ) . 'public/css/style.css', null, '1.0.' . time() );
	}
}

FineTuningSettings::init();
