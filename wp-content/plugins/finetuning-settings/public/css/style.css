body, html {
    overflow-x: hidden;
}

/* um login form subbmit button */
#um-submit-btn {
    background-color: #FE0DC8;
}

/* .woocommerce {
    padding: 5%;
} */

.um input[type=submit].um-button, .um input[type=submit].um-button:focus, .um a.um-button, .um a.um-button.um-disabled:hover, .um a.um-button.um-disabled:focus, .um a.um-button.um-disabled:active {
    background: #FA10C8;
}

.um.um-account.um-editing.um-um_account_id {
    padding-right: 20%;
    padding-left: 20%;
    padding-top: 50px;
    padding-bottom: 80px;
}

.um .um-tip:hover, .um .um-field-radio.active:not(.um-field-radio-state-disabled) i, .um .um-field-checkbox.active:not(.um-field-radio-state-disabled) i, .um .um-member-name a:hover, .um .um-member-more a:hover, .um .um-member-less a:hover, .um .um-members-pagi a:hover, .um .um-cover-add:hover, .um .um-profile-subnav a.active, .um .um-item-meta a, .um-account-name a:hover, .um-account-nav a.current, .um-account-side li a.current span.um-account-icon, .um-account-side li a.current:hover span.um-account-icon, .um-dropdown li a:hover, i.um-active-color, span.um-active-color {
    color: #FA10C8;
}


body.rtl .um-account .um-account-main {
    padding-right: 30px !important;
}

a.um-account-link {
    padding: 10px !important;
}

i.um-faicon-angle-left {
    margin-left: 15px;
}

.um-account .um-woo-form.woocommerce .button {
    background-color: #FA10C8;
}

.um .um-field-group-head, .picker__box, .picker__nav--prev:hover, .picker__nav--next:hover, .um .um-members-pagi span.current, .um .um-members-pagi span.current:hover, .um .um-profile-nav-item.active a, .um .um-profile-nav-item.active a:hover, .upload, .um-modal-header, .um-modal-btn, .um-modal-btn.disabled, .um-modal-btn.disabled:hover, div.uimob800 .um-account-side li a.current, div.uimob800 .um-account-side li a.current:hover {
    background: #FA10C8 !important;
}

.woocommerce div.product {
    padding-top: unset;
}

i.um-faicon-cog {
    color: #FE0DC8;
}


.um-modal-btn.alt {
    color: #fff;
    margin-bottom: 30px;
}

label {
    direction: ltr;
}

i.um-faicon-angle-down.icon {
    margin-right: 10px;
    margin-left: 10px;
}

span {
    direction: rtl;
}

body > div.woocommerce {
    padding: 2% 10% 10% 10%;
}

label {
    direction: rtl;
}

/* hide return to shop button */

a.button.continue_shopping.pull-left.wc-backward {
    display: none;
}

/* hide save to account checkbox on checkout */
p.form-row.woocommerce-SavedPaymentMethods-saveNew.woocommerce-validated {
    display: none !important;
}

.sg-popup-builder-content {
    height: auto !important;
}

.user-login {
    display: block;
}

.logged-in .user-login,
.logged-in .user-register,
.logged-in .logged-in-hide {
    display: none;
}

.elementor-editor-active .user-login,
.elementor-editor-active .user-register,
.elementor-editor-active .logged-in-hide,
.logged-in .user-account {
    display: block !important;
}

.user-account {
    display: none;
}

.um-profile-nav,
.um-header,
.search-vod-form {
    max-width: 1200px;
    margin: 0 auto;
}

.logged-in .login-wrap {
    width: 100% !important;
}
