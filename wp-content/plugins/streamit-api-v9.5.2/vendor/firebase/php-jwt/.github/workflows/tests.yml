name: Test Suite
on:
  push:
    branches:
      - main
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: [ "8.0", "8.1", "8.2", "8.3", "8.4" ]
    name: PHP ${{matrix.php }} Unit Test
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
      - name: Install Dependencies
        uses: nick-invision/retry@v1
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: composer install
      - name: Run Script
        run: vendor/bin/phpunit

  style:
    runs-on: ubuntu-latest
    name: PHP Style Check
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.3"
      - name: Run Script
        run: |
          composer global require friendsofphp/php-cs-fixer
          ~/.composer/vendor/bin/php-cs-fixer fix --diff --dry-run --allow-risky=yes .

  staticanalysis:
    runs-on: ubuntu-latest
    name: PHPStan Static Analysis
    steps:
    - uses: actions/checkout@v2
    - name: Install PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
    - name: Run Script
      run: |
        composer install
        composer global require phpstan/phpstan:~1.10.0
        ~/.composer/vendor/bin/phpstan analyse
