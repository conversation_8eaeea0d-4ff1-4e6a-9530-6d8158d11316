# Copyright (C) 2025 Iqonic Design
# This file is distributed under the GPL-2.0+.
msgid ""
msgstr ""
"Project-Id-Version: Streamit Api 9.5.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/streamit_plugin\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-26T06:58:48+02:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: streamit-api\n"

#. Plugin Name of the plugin
#: streamit-api.php
msgid "Streamit Api"
msgstr ""

#. Plugin URI of the plugin
#: streamit-api.php
msgid "https://iqonic.design"
msgstr ""

#. Description of the plugin
#: streamit-api.php
msgid "Streamit api mobile plugin"
msgstr ""

#. Author of the plugin
#: streamit-api.php
msgid "Iqonic Design"
msgstr ""

#. Author URI of the plugin
#: streamit-api.php
msgid "https://iqonic.design/"
msgstr ""

#: admin/class-streamit-api-admin.php:90
#: admin/class-streamit-api-admin.php:91
#: admin/view/html-admin-options.php:8
msgid "App Options"
msgstr ""

#: admin/class-streamit-api-admin.php:101
#: admin/class-streamit-api-admin.php:102
msgid "App Content"
msgstr ""

#: admin/class-streamit-api-admin.php:111
msgid "App Settings"
msgstr ""

#: admin/class-streamit-api-admin.php:112
msgid "Settings"
msgstr ""

#: admin/class-streamit-api-admin.php:122
msgid "Migration Settings"
msgstr ""

#: admin/class-streamit-api-admin.php:123
msgid "Migration"
msgstr ""

#: admin/class-streamit-api-admin.php:142
msgid "Home"
msgstr ""

#: admin/class-streamit-api-admin.php:147
#: admin/view/html-admin-comment-option.php:18
msgid "Movies"
msgstr ""

#: admin/class-streamit-api-admin.php:152
msgid "TV Shows"
msgstr ""

#: admin/class-streamit-api-admin.php:157
#: admin/view/html-admin-home.php:23
msgid "Video"
msgstr ""

#: admin/class-streamit-api-admin.php:231
msgid "General"
msgstr ""

#: admin/class-streamit-api-admin.php:236
msgid "Rate and Review"
msgstr ""

#: admin/class-streamit-api-admin.php:241
msgid "Limits Logins"
msgstr ""

#: admin/class-streamit-api-admin.php:246
msgid "Membership"
msgstr ""

#: admin/class-streamit-api-admin.php:310
#: admin/view/html-admin-migration-template.php:18
msgid "Download & Install Plugin"
msgstr ""

#: admin/class-streamit-api-admin.php:342
msgid "Learn More"
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:74
msgid "Option parameter is missing or empty."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:78
msgid "Form data is missing."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:87
#: admin/controllers/class.streamit-api-admin-pannel-controller.php:140
msgid "You have not made any changes."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:95
msgid "Options updated successfully."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:97
#: admin/view/banners/html-admin-banner.php:21
#: admin/view/banners/html-admin-banner.php:24
#: admin/view/banners/html-admin-banner.php:27
#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:138
#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:150
msgid "Something went wrong."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:102
#: admin/controllers/class.streamit-api-admin-pannel-controller.php:167
msgid "An unexpected error occurred."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:123
msgid "Setting key and data are required."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:153
msgid "Failed to update settings in the database."
msgstr ""

#: admin/controllers/class.streamit-api-admin-pannel-controller.php:160
msgid "Data update successfully."
msgstr ""

#: admin/functions/streamit-api-general-functions.php:48
msgid "No Data found"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:64
msgid "None"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:65
#: admin/view/sliders/html-admin-slider.php:87
#: admin/view/sliders/html-admin-slider.php:152
#: admin/view/sliders/html-admin-slider.php:216
msgid "Latest"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:66
#: admin/view/sliders/html-admin-slider.php:89
#: admin/view/sliders/html-admin-slider.php:155
#: admin/view/sliders/html-admin-slider.php:218
msgid "Upcoming"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:67
msgid "Most Liked"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:68
msgid "Most Viewed"
msgstr ""

#: admin/functions/streamit-api-general-functions.php:69
#: admin/view/sliders/html-admin-slider.php:91
#: admin/view/sliders/html-admin-slider.php:157
#: admin/view/sliders/html-admin-slider.php:220
msgid "Top 10"
msgstr ""

#: admin/view/banners/html-admin-banner.php:22
#: admin/view/banners/html-admin-banner.php:25
#: admin/view/banners/html-admin-banner.php:28
#: admin/view/banners/html-admin-banner.php:36
msgid "No Data Found."
msgstr ""

#: admin/view/banners/html-admin-banner.php:63
msgid "Select Banner Item"
msgstr ""

#: admin/view/banners/html-admin-banner.php:73
msgid "Banner Image"
msgstr ""

#: admin/view/banners/html-admin-banner.php:79
msgid "Upload the banner image."
msgstr ""

#: admin/view/banners/html-admin-banner.php:84
msgid "Banner image is required."
msgstr ""

#: admin/view/fields/html-button-field.php:15
#: admin/view/html-admin-comment-option.php:56
#: admin/view/html-admin-firebase-option.php:93
#: admin/view/html-admin-home.php:81
#: admin/view/html-admin-limitlogins-option.php:70
#: admin/view/html-admin-membership-option.php:111
#: admin/view/html-admin-movie.php:72
#: admin/view/html-admin-tvshow.php:70
#: admin/view/html-admin-video.php:70
msgid "Submit"
msgstr ""

#: admin/view/fields/html-upload-media.php:22
msgid "Choose Media to Upload"
msgstr ""

#: admin/view/fields/html-upload-media.php:52
msgid "X"
msgstr ""

#: admin/view/html-admin-comment-option.php:28
msgid "Tv Show"
msgstr ""

#: admin/view/html-admin-comment-option.php:38
msgid "Episodes"
msgstr ""

#: admin/view/html-admin-comment-option.php:48
msgid "Videos"
msgstr ""

#: admin/view/html-admin-firebase-option.php:17
msgid "Display titles"
msgstr ""

#: admin/view/html-admin-firebase-option.php:26
msgid "Client Email"
msgstr ""

#: admin/view/html-admin-firebase-option.php:29
msgid "Add your FireBase Client email"
msgstr ""

#: admin/view/html-admin-firebase-option.php:37
msgid "Private Key"
msgstr ""

#: admin/view/html-admin-firebase-option.php:40
msgid "Add your FireBase Private key"
msgstr ""

#: admin/view/html-admin-firebase-option.php:48
msgid "Project Id"
msgstr ""

#: admin/view/html-admin-firebase-option.php:51
msgid "Add your FireBase Project id"
msgstr ""

#: admin/view/html-admin-firebase-option.php:59
msgid "Application Name"
msgstr ""

#: admin/view/html-admin-firebase-option.php:62
msgid "Add your Application Name"
msgstr ""

#: admin/view/html-admin-firebase-option.php:69
msgid "Default Image"
msgstr ""

#: admin/view/html-admin-firebase-option.php:75
msgid "Upload the default image."
msgstr ""

#: admin/view/html-admin-firebase-option.php:81
msgid "App Logo"
msgstr ""

#: admin/view/html-admin-firebase-option.php:87
msgid "Upload the App Logo image New."
msgstr ""

#: admin/view/html-admin-home.php:11
msgid "Home Banner"
msgstr ""

#: admin/view/html-admin-home.php:22
msgid "Movie"
msgstr ""

#: admin/view/html-admin-home.php:24
msgid "TV Show"
msgstr ""

#: admin/view/html-admin-home.php:30
#: admin/view/html-admin-movie.php:20
#: admin/view/html-admin-tvshow.php:18
#: admin/view/html-admin-video.php:18
msgid "Add Banner"
msgstr ""

#: admin/view/html-admin-home.php:31
#: admin/view/html-admin-home.php:56
#: admin/view/html-admin-movie.php:21
#: admin/view/html-admin-movie.php:46
#: admin/view/html-admin-tvshow.php:19
#: admin/view/html-admin-tvshow.php:44
#: admin/view/html-admin-video.php:19
#: admin/view/html-admin-video.php:44
msgid "Add New"
msgstr ""

#: admin/view/html-admin-home.php:55
#: admin/view/html-admin-movie.php:45
#: admin/view/html-admin-tvshow.php:43
#: admin/view/html-admin-video.php:43
msgid "Add Slider"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:13
msgid "Device Limits"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:14
msgid "Set login limits per account"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:24
msgid "Enable"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:35
msgid "Default Limit"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:44
msgid "Membership Plans"
msgstr ""

#: admin/view/html-admin-limitlogins-option.php:45
msgid "Add device limits by membership plans"
msgstr ""

#: admin/view/html-admin-membership-option.php:19
msgid "Select Section"
msgstr ""

#: admin/view/html-admin-membership-option.php:23
msgid "Disable"
msgstr ""

#: admin/view/html-admin-membership-option.php:24
msgid "Default"
msgstr ""

#: admin/view/html-admin-membership-option.php:25
msgid "In-App Payment"
msgstr ""

#: admin/view/html-admin-membership-option.php:42
msgid "Instructions !"
msgstr ""

#: admin/view/html-admin-membership-option.php:44
msgid "In order for the in-app purchase feature to work,"
msgstr ""

#: admin/view/html-admin-membership-option.php:45
msgid "- You must enter the `Entitlement ID` and one of the API keys according to your app"
msgstr ""

#: admin/view/html-admin-membership-option.php:46
msgid "- Enter both API keys if your app supports iOS and Android."
msgstr ""

#: admin/view/html-admin-membership-option.php:74
msgid "Entitlement ID"
msgstr ""

#: admin/view/html-admin-membership-option.php:84
msgid "Google API key"
msgstr ""

#: admin/view/html-admin-membership-option.php:94
msgid "Apple API key"
msgstr ""

#: admin/view/html-admin-migration-template.php:10
msgid "Hello"
msgstr ""

#: admin/view/html-admin-migration-template.php:11
msgid "To ensure your data is compatible with the latest version of Streamit, we recommend migrating your data."
msgstr ""

#: admin/view/html-admin-migration-template.php:12
msgid "To proceed with the migration, please install the <strong>Streamit Import</strong> plugin."
msgstr ""

#: admin/view/html-admin-migration-template.php:14
msgid "Click the button below to download and install the plugin."
msgstr ""

#: admin/view/html-admin-movie.php:12
msgid "Movie Banner"
msgstr ""

#: admin/view/html-admin-tvshow.php:11
msgid "TV Show Banner"
msgstr ""

#: admin/view/html-admin-video.php:11
msgid "Video Banner"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:27
msgid "Title"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:35
msgid "Title is required"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:42
msgid "View All"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:53
msgid "Select Post Type"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:81
#: admin/view/sliders/html-admin-slider.php:146
#: admin/view/sliders/html-admin-slider.php:210
msgid "Select Filter"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:85
#: admin/view/sliders/html-admin-slider.php:103
#: admin/view/sliders/html-admin-slider.php:150
#: admin/view/sliders/html-admin-slider.php:214
#: admin/view/sliders/html-admin-slider.php:231
msgid "Genres"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:86
#: admin/view/sliders/html-admin-slider.php:116
#: admin/view/sliders/html-admin-slider.php:151
#: admin/view/sliders/html-admin-slider.php:180
#: admin/view/sliders/html-admin-slider.php:215
#: admin/view/sliders/html-admin-slider.php:243
msgid "Tags"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:88
#: admin/view/sliders/html-admin-slider.php:154
#: admin/view/sliders/html-admin-slider.php:217
msgid "Selected"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:90
#: admin/view/sliders/html-admin-slider.php:156
#: admin/view/sliders/html-admin-slider.php:219
msgid "Most Views"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:128
msgid "Select Movies"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:168
msgid "Category"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:192
msgid "Select Videos"
msgstr ""

#: admin/view/sliders/html-admin-slider.php:255
msgid "Select Tv Shows"
msgstr ""

#: includes/classes/controllers/cast-routes/class-streamit-api-cast-callback.php:28
msgid "Details not found."
msgstr ""

#: includes/classes/controllers/cast-routes/class-streamit-api-cast-callback.php:111
msgid "Cast details."
msgstr ""

#: includes/classes/controllers/cast-routes/class-streamit-api-cast-callback.php:134
#: includes/classes/controllers/cast-routes/class-streamit-api-cast-callback.php:193
#: includes/classes/controllers/episode-routes/class-streamit-api-episode-callback.php:33
#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:71
#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:35
#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:73
#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:150
#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:431
#: includes/classes/controllers/tvshow-routes/class-streamit-api-tvshow-callback.php:89
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:512
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:586
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:708
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:763
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:844
msgid "No data found."
msgstr ""

#: includes/classes/controllers/cast-routes/class-streamit-api-cast-callback.php:187
msgid "Details."
msgstr ""

#: includes/classes/controllers/episode-routes/class-streamit-api-episode-callback.php:44
msgid "Episode Details"
msgstr ""

#: includes/classes/controllers/movies-routes/class-streamit-api-movie-callback.php:33
#: includes/classes/controllers/videos-routes/class-streamit-api-video-callback.php:77
msgid "No details found."
msgstr ""

#: includes/classes/controllers/movies-routes/class-streamit-api-movie-callback.php:67
msgid "Movie Details."
msgstr ""

#: includes/classes/controllers/movies-routes/class-streamit-api-movie-callback.php:104
msgid "No movies found."
msgstr ""

#: includes/classes/controllers/movies-routes/class-streamit-api-movie-callback.php:110
msgid "Recommended movies."
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:31
msgid "Notification not found."
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:50
msgctxt "time difference"
msgid "%s ago"
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:74
msgid "Notification List."
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:101
msgid "Read notification."
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:101
msgid "Unread notification"
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:107
msgid "somthing went wrong."
msgstr ""

#: includes/classes/controllers/notification-routes/class-streamit-api-notification-callback.php:134
msgid "Notification Count"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:31
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:47
msgid "Playlists not found."
msgstr ""

#. Translators: %s represents the number of items in the playlist
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:74
msgid "%s item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:96
msgid "Playlists retrieved successfully."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:121
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:186
msgid "Playlist type not found."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:128
msgid "Please Add Playlist Name."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:149
msgid "Playlist created successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:150
msgid "Playlist changes saved successfully."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:160
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:219
msgid "Something went wrong. Try again."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:195
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:248
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:309
#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:379
msgid "Playlist not available"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:203
msgid "Your are not allow to delete playlist."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:214
msgid "Playlist deleted successfully."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:256
msgid "Your are not allow to see playlist data."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:264
msgid "Playlist is empty."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:285
msgid "Playlist."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:317
msgid "Your are not allow to add item."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:330
msgid "Playlist media Not added. Try Again."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:337
msgid "Movie added to your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:340
msgid "Tv Show added to your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:343
msgid "Video added to your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:346
msgid "Post added successfully."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:387
msgid "Your are not allow to delete item."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:395
msgid "Playlist media not removed. Try again."
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:401
msgid "Movie removed from your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:404
msgid "Tv Show removed from your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:407
msgid "Video removed from your playlist successfully!"
msgstr ""

#: includes/classes/controllers/playlist-routes/class-streamit-api-playlist-callback.php:410
msgid "Post deleted successfully."
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:27
msgid "No membership plans found."
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:33
msgid "Membership plans."
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:79
msgid "User orders."
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:220
msgid "Something Wrong ! Try Again."
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:239
msgid "Membership order not found"
msgstr ""

#: includes/classes/controllers/pmp-routes/class-streamit-api-pmp-callback.php:246
msgid "Membership orders"
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:29
msgid "No rate available."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:49
msgid "Rating List."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:77
msgid "You have already added a review."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:97
msgid "Invalid rate ID provided."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:103
msgid "Review Updated successfully."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:103
msgid "Failed to update the review."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:107
msgid "Review Added successfully."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:107
msgid "Failed to add the review."
msgstr ""

#: includes/classes/controllers/ratting-routes/class-streamit-api-ratting-callback.php:145
msgid "Review deleted successfully."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:47
msgid "Search Reasult."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:157
msgid "Dashboard result."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:350
msgid "No genres found."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:367
msgid "Genre result."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:390
msgid "Missing required parameters."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:421
msgid "Result."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:533
msgid "App comman settings."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:644
msgid "Content Not available."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:656
msgid "View all results."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:680
#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:778
msgid "No recent searches found."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:687
msgid "Searches."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:743
msgid "Search Result."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:768
msgid "Valid search ID is required."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:790
msgid "Search ID not found."
msgstr ""

#: includes/classes/controllers/streamit-routes/class-streamit-api-streamit-callback.php:805
msgid "Search removed successfully."
msgstr ""

#: includes/classes/controllers/tvshow-routes/class-streamit-api-tvshow-callback.php:32
#: includes/classes/controllers/tvshow-routes/class-streamit-api-tvshow-callback.php:46
msgid "No Details found."
msgstr ""

#: includes/classes/controllers/tvshow-routes/class-streamit-api-tvshow-callback.php:52
msgid "TV shows details."
msgstr ""

#: includes/classes/controllers/tvshow-routes/class-streamit-api-tvshow-callback.php:95
msgid "Season data."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:31
msgid "Username already exists."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:33
msgid "Username can only contain letters, numbers, \"_\", and \"-\"."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:38
msgid "Email already exists."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:43
msgid "Password must be at least 6 characters long."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:50
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:71
msgid "Registration failed."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:88
msgid "User registered successfully."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:110
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:126
msgid "Valid token."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:120
msgid "Token has been expired."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:160
msgid "User profile."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:213
msgid "Profile has been updated successfully"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:238
msgid "User not found."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:244
msgid "Old password is invalid."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:250
msgid "Password has been changed successfully."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:272
msgid "User not found with this email address."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:278
msgid "New Password"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:280
msgid "Hello,"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:281
msgid "You recently requested to reset your password. Here is the new password for your App:"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:282
msgid "New Password:"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:283
msgid "Thanks,"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:293
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:298
msgid "Password has been sent successfully to your email address."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:304
msgid "Currently, the email service is disabled by the admin."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:338
msgid "User Deleted Successfully."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:343
msgid "User not Deleted."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:365
msgid "Store api nonce."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:389
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:432
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:481
msgid "List of logged-in devices."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:395
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:474
msgid "No devices found."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:457
msgid "All devices are removed."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:558
msgid "Watchlist result."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:591
msgid "Somthing Went Wrong."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:598
msgid "Movie added to watchlist successfully!"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:601
msgid "Tv Show added to watchlist successfully!"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:604
msgid "Video added to watchlist successfully!"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:607
msgid "Added to watchlist."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:616
msgid "Movie removed from your watchlist."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:619
msgid "Tv Show removed from your watchlist."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:622
msgid "Video removed from your watchlist."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:625
msgid "Removed from watchlist."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:721
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:816
#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:896
msgid "Continue watch Result."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:922
msgid "Post ID and Post Type are required."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:945
msgid "You liked this."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:948
msgid "You unliked this."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:963
msgid "Something went wrong. Please try again."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:999
msgid "Player ID`s."
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:1032
msgid "Player ID`s"
msgstr ""

#: includes/classes/controllers/user-routes/class-streamit-api-user-callback.php:1040
msgid "Player Id not present | records not available."
msgstr ""

#: includes/classes/controllers/videos-routes/class-streamit-api-video-callback.php:39
msgid "No videos found."
msgstr ""

#: includes/classes/controllers/videos-routes/class-streamit-api-video-callback.php:51
msgid "Video list."
msgstr ""

#: includes/classes/controllers/videos-routes/class-streamit-api-video-callback.php:108
msgid "Video Details."
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:33
msgid "Request ID not found"
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:42
msgid "Comment ID not found"
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:48
msgid "Comment author matches the current user"
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:66
msgid "You cannot leave the comment empty."
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:75
msgid "Comment ID not found."
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:82
#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:111
msgid "Something went wrong! Try again."
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:92
msgid "Comment edited successfully."
msgstr ""

#: includes/classes/controllers/wpposts-routes/class-streamit-api-wppost-callback.php:117
msgid "Comment has been deleted."
msgstr ""

#: includes/functions/st-helper-function.php:57
msgid "Authorization failed"
msgstr ""

#: includes/functions/st-pmp-helper-function.php:423
msgid "Account Limit Exceeded."
msgstr ""
