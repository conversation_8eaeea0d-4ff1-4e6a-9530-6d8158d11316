<?php

/**
 * Streamit API
 *
 * @link              https://iqonic.design
 * @since             9.5.0
 * @package           Streamit_Api
 *
 * @wordpress-plugin
 * Plugin Name:       Streamit Api
 * Plugin URI:        https://iqonic.design
 * Description:       Streamit api mobile plugin
 * Version:           9.5.2
 * Author:            Iqonic Design
 * Author URI:        https://iqonic.design/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       streamit-api
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

if (file_exists(dirname(__FILE__) . '/vendor/autoload.php')) {
	require_once dirname(__FILE__) . '/vendor/autoload.php';
} else {
	die('Something went wrong');
}


/**
 * Currently plugin version.
 * Start at version 9.5.0 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define( 'STREAMIT_API_VERSION', '9.5.2' );

// Plugin Root File
if (!defined('STREAMIT_API_PLUGIN_FILE')) {
    define('STREAMIT_API_PLUGIN_FILE', __FILE__);
}

if (!defined('STREAMIT_API_DIR')) {
	define('STREAMIT_API_DIR', plugin_dir_path(__FILE__));
}

if (!defined('STREAMIT_API_DIR_URI')) {
	define('STREAMIT_API_DIR_URI', plugin_dir_url(__FILE__));
}


if (!defined('STREAMIT_API_NAMESPACE')) {
	define('STREAMIT_API_NAMESPACE', 'streamit');
}

if (!defined('STREAMIT_API_PREFIX')) {
	define('STREAMIT_API_PREFIX', 'iq_');
}

if (!defined('JWT_AUTH_SECRET_KEY')) {
	define('JWT_AUTH_SECRET_KEY', 'your-top-secrect-key');
}

if (!defined('JWT_AUTH_CORS_ENABLE')) {
	define('JWT_AUTH_CORS_ENABLE', true);
}

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-streamit-api-activator.php
 */
function activate_streamit_api() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-streamit-api-activator.php';
	Streamit_Api_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-streamit-api-deactivator.php
 */
function deactivate_streamit_api() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-streamit-api-deactivator.php';
	Streamit_Api_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_streamit_api' );
register_deactivation_hook( __FILE__, 'deactivate_streamit_api' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-streamit-api.php';


new Streamit_Api;
