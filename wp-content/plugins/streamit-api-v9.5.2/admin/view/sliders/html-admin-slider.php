<?php
if (!defined('ABSPATH')) {
    exit;
}
$field_controller = new streamit_api_field_controller();
?>

<div class="streamit_api_single_slider" rel="<?php echo esc_attr($i); ?>">
    <div class="remove_slider">
        <button class="remove_selected_slider">X</button>
    </div>
    <?php
    $field_controller->get_text_field(
        array(
            'key' => 'type',
            'id' => '',
            'label' => '',
            'type' => 'hidden',
            'value' => $type,
            'class' => 'hidden-text'
        )
    );
    $field_controller->get_text_field(
        array(
            'key' => 'slider_title',
            'id' => '',
            'label' => esc_html__('Title', 'streamit-api'),
            'type' => 'text',
            'value' => isset($value['title']) ? $value['title'] : '',
            'class' => 'slider_title'
        )
    );
    ?>
    <p class="title_error" style="color: red; display: none; margin: -16px 6px 19px 0px;">
        <?php echo esc_html__('Title is required', 'streamit-api'); ?>
    </p>
    <?php
    $field_controller->get_checkbox_field(
        array(
            'key' => 'view_all',
            'id' => '',
            'label' => esc_html__('View All', 'streamit-api'),
            'value' => isset($value['view_all']) ? $value['view_all'] : 'false',
        )
    );
    if ($type == 'home') {
        $field_controller->get_select_field(
            array(
                'key'    => 'slider_post_type_' . $type . '_' . esc_attr($i),
                'name'   => 'slider_post_type_' . esc_attr($i),
                'id'     => '',
                'value'  => isset($value['post_type']) ? $value['post_type'] : '',
                'label'  => esc_html__('Select Post Type', 'streamit-api'),
                'options' => [
                    'movie'  => 'Movies',
                    'video'  => 'Videos',
                    'tvshow' => 'TV Shows'
                ]
            )
        );
    } else {
        $field_controller->get_text_field(
            array(
                'key' => 'slider_post_type_' . $type . '_' . esc_attr($i),
                'id' => '',
                'label' => '',
                'type' => 'hidden',
                'value' => $type,
            )
        );
    }
    ?>
    <?php if ($type == ('movie' || 'home')) : ?>
        <div class="movie_slider_options">
            <?php
            $filterBy = $value['moviefilter'] ?? 'latest';
            $field_controller->get_select_field(
                array(
                    'key' => 'movie_select_fillters_' . $type . '_' . esc_attr($i),
                    'id'   => 'select_type_' . esc_attr($i),
                    'label' => esc_html__('Select Filter', 'streamit-api'),
                    'name' => 'select_movie_fillters_' . esc_attr($i),
                    'value' => $filterBy,
                    'options' => [
                        'genre'     => esc_html__('Genres', 'streamit-api'),
                        'tag'       => esc_html__('Tags', 'streamit-api'),
                        'latest'    => esc_html__('Latest', 'streamit-api'),
                        'selected'  => esc_html__('Selected', 'streamit-api'),
                        'upcoming'  => esc_html__('Upcoming', 'streamit-api'),
                        'view'      => esc_html__('Most Views', 'streamit-api'),
                        'top_ten'    => esc_html__('Top 10', 'streamit-api'),
                    ],
                )
            );
            ?>
            <div class="genre">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'movie_slider_genre_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'name'    => 'movie_slider_genre_' . $type . '_' . esc_attr($i),
                    'value'  => isset($value['movieGenres']) ? $value['movieGenres'] : '',
                    'label'  => esc_html__('Genres', 'streamit-api'),
                    'options' => streamit_get_all_genres('movie'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="tag">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'movie_slider_tag_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'name'    => 'movie_slider_tag_' . $type . '_' . esc_attr($i),
                    'value'  => isset($value['movieTags']) ? $value['movieTags'] : '',
                    'label'  => esc_html__('Tags', 'streamit-api'),
                    'options' => streamit_get_all_tags('movie'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="selected">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'movie_slider_selected_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['movieSelect']) ? $value['movieSelect'] : '',
                    'label'  => esc_html__('Select Movies', 'streamit-api'),
                    'options' => streamit_get_recommended_movies(['per_page' => -1]),
                    'multiple' => true
                ));
                ?>
            </div>
        </div>
    <?php
    endif;
    if ($type == ('video' || 'home')) : ?>

        <div class="video_slider_options">
            <?php
            $filterBy = $value['videofilter'] ?? 'latest';
            $field_controller->get_select_field(
                array(
                    'key' => 'video_select_fillters_' . $type . '_' . esc_attr($i),
                    'id'   => 'select_type_' . esc_attr($i),
                    'label' => esc_html__('Select Filter', 'streamit-api'),
                    'name' => 'select_video_fillters_' . esc_attr($i),
                    'value' => $filterBy,
                    'options' => [
                        'genre'     => esc_html__('Genres', 'streamit-api'),
                        'tag'       => esc_html__('Tags', 'streamit-api'),
                        'latest'    => esc_html__('Latest', 'streamit-api'),
                        'most_like' => esc_html__('Most Like' ,' streamit-api'),
                        'selected'  => esc_html__('Selected', 'streamit-api'),
                        'upcoming'  => esc_html__('Upcoming', 'streamit-api'),
                        'view'      => esc_html__('Most Views', 'streamit-api'),
                        'top_ten'    => esc_html__('Top 10', 'streamit-api'),
                    ],
                )
            );
            ?>
            <div class="genre">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'video_slider_genre_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['videoGenres']) ? $value['videoGenres'] : '',
                    'label'  => esc_html__('Category', 'streamit-api'),
                    'options' => streamit_get_all_genres('video'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="tag">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'video_slider_tag_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['videoTags']) ? $value['videoTags'] : '',
                    'label'  => esc_html__('Tags', 'streamit-api'),
                    'options' => streamit_get_all_tags('video'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="selected">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'video_slider_selected_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['videoSelect']) ? $value['videoSelect'] : '',
                    'label'  => esc_html__('Select Videos', 'streamit-api'),
                    'options' => streamit_get_recommended_videos(['per_page' => -1]),
                    'multiple' => true
                ));
                ?>
            </div>

        </div>
    <?php
    endif;
    if ($type == ('tvshow' || 'home')) : ?>
        <div class="tvshow_slider_options">
            <?php
            $filterBy = $value['tvshowfilter'] ?? 'latest';
            $field_controller->get_select_field(
                array(
                    'key' => 'tvshow_select_fillters_' . $type . '_' . esc_attr($i),
                    'id'   => 'select_type_' . esc_attr($i),
                    'label' => esc_html__('Select Filter', 'streamit-api'),
                    'name' => 'select_tvshows_fillters_' . esc_attr($i),
                    'value' => $filterBy,
                    'options' => [
                        'genre'     => esc_html__('Genres', 'streamit-api'),
                        'tag'       => esc_html__('Tags', 'streamit-api'),
                        'latest'    => esc_html__('Latest', 'streamit-api'),
                        'selected'  => esc_html__('Selected', 'streamit-api'),
                        'upcoming'  => esc_html__('Upcoming', 'streamit-api'),
                        'view'      => esc_html__('Most Views', 'streamit-api'),
                        'top_ten'    => esc_html__('Top 10', 'streamit-api'),
                    ],
                )
            );
            ?>
            <div class="genre">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'tvshow_slider_genre_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['tvshowGenres']) ? $value['tvshowGenres'] : '',
                    'label'  => esc_html__('Genres', 'streamit-api'),
                    'options' => streamit_get_all_genres('tvshow'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="tag">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'tvshow_slider_tag_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['tvshowTags']) ? $value['tvshowTags'] : '',
                    'label'  => esc_html__('Tags', 'streamit-api'),
                    'options' => streamit_get_all_tags('tvshow'),
                    'multiple' => true
                ));
                ?>
            </div>
            <div class="selected">
                <?php
                $field_controller->get_select_field(array(
                    'key'    => 'tvshow_slider_selected_' . $type . '_' . esc_attr($i),
                    'id'     => '',
                    'value'  => isset($value['tvshowSelect']) ? $value['tvshowSelect'] : '',
                    'label'  => esc_html__('Select Tv Shows', 'streamit-api'),
                    'options' => streamit_get_recommended_tvshows(['per_page' => -1]),
                    'multiple' => true
                ));
                ?>
            </div>
        </div>
    <?php
    endif; ?>
</div>