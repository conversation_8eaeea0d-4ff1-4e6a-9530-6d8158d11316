<?php
if (!defined('ABSPATH')) {
    exit;
}
$field_controller = new streamit_api_field_controller();
$value = !empty($api_options) && isset($api_options['st_device_lmit']) ? $api_options['st_device_lmit'] : '';

?>

<div id="admin_options_limitslogin" class="streamit_admin_setting_pannel">
    <form name="st-admin-settings-limitlogin" id="st-admin-settings-limitlogin" enctype="multipart/form-data">

        <h2><?php esc_html_e('Device Limits', 'streamit-api'); ?></h2>
        <h4><?php esc_html_e('Set login limits per account', 'streamit-api'); ?></h4>


        <?php
        $is_enabled = isset($value['limitlogin_is_enable']) && $value['limitlogin_is_enable'] == '1';
        $field_controller->get_switch_checkbox_field(
            array(
                'key' => 'limitlogin_is_enable',
                'id' => '',
                'value' => isset($value['limitlogin_is_enable']) ? $value['limitlogin_is_enable'] : '0',
                'label' => esc_html__('Enable', 'streamit-api'),
                'description' => '',
            )
        );
        ?>
        <div id='display_limit_login_options' style="display: <?php echo $is_enabled ? 'block' : 'none'; ?>">
        <?php
        $field_controller->get_text_field(
            array(
                'key'   => 'default_limit',
                'id'    => '',
                'label' => esc_html__('Default Limit', 'streamit-api'),
                'type'  => 'number',
                'value' => isset($value['default_limit']) ? $value['default_limit'] : '',
                'class' => 'limit-login-input', 
            )
        );

        ?>

        <h2><?php esc_html_e('Membership Plans', 'streamit-api'); ?></h2>
        <h4><?php esc_html_e('Add device limits by membership plans', 'streamit-api'); ?></h4>

        <?php
        $pmp_levels = function_exists('streamit_get_pricing_levels') ? streamit_get_pricing_levels() : '';
        if (!empty($pmp_levels)) {
            foreach ($pmp_levels as $level_id => $level_name) {
                $field_controller->get_text_field(
                    array(
                        'key'   => $level_id,
                        'id'    => '',
                        'label' => esc_html($level_name),
                        'type'  => 'number',
                        'value' => isset($value[$level_id]) ? $value[$level_id] : '',
                        'class' => 'limit-login-input', 
                    )
                );
            }
        }
        ?>
        </div>
        <?php

        $field_controller->get_button_field(array(
            'key'           => 'st-admin-settings-limitlogin',
            'label'         => '',
            'button_text'   => esc_html__('Submit', 'streamit-api'),
            'class'         => 'button',
            'type'          => 'submit',
            
        ));
        ?>
    </form>
</div>