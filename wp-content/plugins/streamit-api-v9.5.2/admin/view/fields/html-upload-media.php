<?php
if (!defined('ABSPATH')) {
    exit;
}

// Ensure required field values are set
$field['name'] = isset($field['name']) ? $field['name'] : $field['id'];
$field['value'] = isset($field['value']) ? $field['value'] : '';
$field['wrapper_class'] = isset($field['wrapper_class']) ? $field['wrapper_class'] : '';
$field['placeholder'] = isset($field['placeholder']) ? $field['placeholder'] : false;

?>
<div id="<?php echo esc_attr($field['key']); ?>_field" class="form-field media-attachment-video media-option <?php echo esc_attr($field['key']); ?>_field <?php echo esc_attr($field['wrapper_class']); ?>">
    <label for="<?php echo esc_attr($field['id']); ?>"><?php echo wp_kses_post($field['label']); ?></label>
    <input type="hidden" id="<?php echo esc_attr($field['key']); ?>" name="<?php echo esc_attr($field['name']); ?>" class="upload_video_id" value="<?php echo esc_attr($field['value']); ?>" />
    <a href="#" id="<?php echo esc_attr($field['upload_video_id']); ?>" class="button streamit_api_upload_video_button tips">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-image">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
            <?php echo esc_html__('Choose Media to Upload', 'streamit-api'); ?>
        </a>
    <div class="streamit_api_media_preview-wrap">
        <div id="<?php echo esc_attr($field['wrapper_class']); ?>" class="streamit_media_preview">
            <?php
            // Check if the field value is not empty
            if (!empty($field['value'])) {
                if (isset($field['type'])) {
                    if ($field['type'] == 'image') {
                        $image_src = wp_get_attachment_url($field['value']);
                        if ($image_src) {
                            echo '<img style="max-width: 150px;" src="' . esc_url($image_src) . '" alt="Image" />';
                        }
                    } elseif ($field['type'] == 'video') {
                        $video_src = wp_get_attachment_url($field['value']);
                        if ($video_src) {
                            echo '<video width="320" height="240" controls>';
                            echo '<source src="' . esc_url($video_src) . '" type="video/mp4">';
                            echo 'Your browser does not support the video tag.';
                            echo '</video>';
                        }
                    }
                }
            }
            ?>

        </div>

        <button href="#" id="<?php echo esc_attr($field['remove_video_id']); ?>" class="button streamit_api_remove_video_button tips">
            <span class="remove-media-icon">
                <?php esc_html_e('X', 'streamit-api'); ?>
            </span>
        </button>
    </div>
</div>