<?php
if (!defined('ABSPATH')) {
    exit;
}

// Ensure required field values are set
$field['name'] = isset($field['name']) ? $field['name'] : $field['id'];
$field['value'] = isset($field['value']) ? $field['value'] : '';
$field['wrapper_class'] = isset($field['wrapper_class']) ? $field['wrapper_class'] : '';
$field['checked'] = !empty($field['value']) && ($field['value'] == '1') ? 'checked' : '';
$field['label'] = isset($field['label']) ? $field['label'] : '';
?>
<div id="<?php echo esc_attr($field['key']); ?>_field" class="form-field <?php echo esc_attr($field['key']); ?>_field <?php echo esc_attr($field['wrapper_class']); ?>">
    <div class="switch">
        <input 
        type="checkbox"
        name="<?php echo esc_attr($field['name']); ?>"
        id="<?php echo esc_attr($field['key']); ?>"
        class="switch-input"
        <?php echo esc_attr($field['checked']); ?> />
        <span class="switch-slider"></span>
        <label for="<?php echo esc_attr($field['id']); ?>"><?php echo wp_kses_post($field['label']); ?></label>
    </div>
</div>
