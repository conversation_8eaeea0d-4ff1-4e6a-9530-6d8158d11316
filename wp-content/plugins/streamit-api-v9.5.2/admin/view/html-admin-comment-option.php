<?php
if (!defined('ABSPATH')) {
    exit;
}
$field_controller = new streamit_api_field_controller();
$value = !empty($api_options) && isset($api_options['st_comment']) ? $api_options['st_comment'] : '';

?>

<div id="admin_options_comment" class="streamit_admin_setting_pannel">
<form name="st-admin-settings-comment" id="st-admin-settings-comment" enctype="multipart/form-data">
<?php
    $field_controller->get_switch_checkbox_field(
        array(
            'key' => 'movie_comments',
            'id' => '',
            'value' => isset($value['movie_comments']) ? $value['movie_comments'] : '',
            'label' => esc_html__('Movies', 'streamit-api'),
            'description' => '',
        )
    );

    $field_controller->get_switch_checkbox_field(
        array(
            'key' => 'tvshow_comments',
            'id' => '',
            'value' => isset($value['tvshow_comments']) ? $value['tvshow_comments'] : '',
            'label' => esc_html__('Tv Show', 'streamit-api'),
            'description' => '',
        )
    ); 

    $field_controller->get_switch_checkbox_field(
        array(
            'key' => 'episode_comments',
            'id' => '',
            'value' => isset($value['episode_comments']) ? $value['episode_comments'] : '',
            'label' => esc_html__('Episodes', 'streamit-api'),
            'description' => '',
        )
    );
    
    $field_controller->get_switch_checkbox_field(
        array(
            'key' => 'video_comments',
            'id' => '',
            'value' => isset($value['video_comments']) ? $value['video_comments'] : '',
            'label' => esc_html__('Videos', 'streamit-api'),
            'description' => '',
        )
    );

    $field_controller->get_button_field(array(
        'key'           => 'st-admin-settings-comment',
        'label'         => '',
        'button_text'   => esc_html__('Submit', 'streamit-api'),
        'class'         => 'button',
        'type'          => 'submit',
    ));
    ?>
</form>
</div>