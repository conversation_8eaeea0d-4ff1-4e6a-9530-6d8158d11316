<?php
if (!defined('ABSPATH')) {
    exit;
}
$field_controller = new streamit_api_field_controller();
$value = !empty($api_options) && isset($api_options['st_firebase']) ? $api_options['st_firebase'] : '';
?>

<div id="admin_options_general" class="streamit_admin_setting_pannel">
<form name="st-admin-settings-general" id="st-admin-settings-general" enctype="multipart/form-data">
<?php
    $field_controller->get_switch_checkbox_field(
        array(
            'key'           => 'show_titles',
            'id'            => '',
            'value'         => isset($value['show_titles']) ? $value['show_titles'] : '',
            'label'         => esc_html__('Display titles', 'streamit-api'),
            'description'   => '',
        )
    );

    $field_controller->get_text_field(
        array(
            'key'           => 'client_email',
            'id'            => '',
            'label'         => esc_html__('Client Email', 'streamit-api'),
            'type'          => 'text',
            'value'         => isset($value['client_email']) ? $value['client_email'] : '',
            'description'   => esc_html__('Add your FireBase Client email' , 'streamit-api')
        )
    );

    $field_controller->get_text_field(
        array(
            'key'           => 'private_key',
            'id'            => '',
            'label'         => esc_html__('Private Key', 'streamit-api'),
            'type'          => 'text',
            'value'         => isset($value['private_key']) ? $value['private_key'] : '',
            'description'   => esc_html__('Add your FireBase Private key' , 'streamit-api')
        )
    );

    $field_controller->get_text_field(
        array(
            'key'           => 'project_id',
            'id'            => '',
            'label'         => esc_html__('Project Id', 'streamit-api'),
            'type'          => 'text',
            'value'         => isset($value['project_id']) ? $value['project_id'] : '',
            'description'   => esc_html__('Add your FireBase Project id' , 'streamit-api')
        )
    );

    $field_controller->get_text_field(
        array(
            'key'           => 'app_name',
            'id'            => '',
            'label'         => esc_html__('Application Name', 'streamit-api'),
            'type'          => 'text',
            'value'         => isset($value['app_name']) ? $value['app_name'] : '',
            'description'   => esc_html__('Add your Application Name' , 'streamit-api')
        )
    );

    $field_controller->get_upload_media_field(array(
        'key'             => 'default_image',
        'id'              => '',
        'label'           => esc_html__('Default Image', 'streamit-api'),
        'value'           => isset($value['default_image']) ? esc_attr($value['default_image']) : '',
        'upload_video_id' => 'streamit_api_upload_default_image',
        'type'            => 'image',
        'remove_video_id' => 'streamit_api_remove_default_image',
        'wrapper_class'   => 'streamit_api_single_default_media',
        'description'     => esc_html__('Upload the default image.', 'streamit-api'),
    ));

    $field_controller->get_upload_media_field(array(
        'key'             => 'app_logo',
        'id'              => '',
        'label'           => esc_html__('App Logo', 'streamit-api'),
        'value'           => isset($value['app_logo']) ? esc_attr($value['app_logo']) : '',
        'upload_video_id' => 'streamit_api_upload_app_logo',
        'type'            => 'image',
        'remove_video_id' => 'streamit_api_remove_app_logo',
        'wrapper_class'   => 'streamit_api_single_app_logo',
        'description'     => esc_html__('Upload the App Logo image New.', 'streamit-api'),
    ));
    
    $field_controller->get_button_field(array(
        'key'           => 'st-admin-settings-general',
        'label'         => '',
        'button_text'   => esc_html__('Submit', 'streamit-api'),
        'class'         => 'button',
        'type'          => 'submit',
    ));

    ?>
</form>
</div>