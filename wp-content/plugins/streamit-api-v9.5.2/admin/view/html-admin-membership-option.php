<?php
if (!defined('ABSPATH')) {
    exit;
}
$field_controller = new streamit_api_field_controller();
$value = !empty($api_options) && isset($api_options['st_pmp_options']) ? $api_options['st_pmp_options'] : '';

?>

<div id="admin_options_membership" class="streamit_admin_setting_pannel">
<form name="st-admin-settings-membership" id="st-admin-settings-membership" enctype="multipart/form-data">

    <div class="streamit-api-slider-radio-section">
        <?php
        $field_controller->get_radio_field(
            array(
                'key'       => 'payment_type',
                'id'        => 'payment_mode',
                'label'     => esc_html__('Select Section', 'streamit-api'),
                'name'      => 'payment_type',
                'value'     => isset($value['payment_type']) ? $value['payment_type'] : '1',
                'options'   => [
                    '0'         => esc_html__('Disable', 'streamit-api'),
                    '1'         => esc_html__('Default', 'streamit-api'),
                    '2'         => esc_html__('In-App Payment', 'streamit-api'),
                ],
                'required' => true
            )
        );
        ?>

        <div class="streamit-api-default-payment mt-1 mb-3">
            <?php
            echo sprintf(
                'You can manage this settings from Memberships > Settings > <a href="%s" target="_blank">Payment Gateway & SSL</a>',
                esc_url(admin_url('?page=pmpro-paymentsettings'))
            );
            ?>
        </div>

        <div class="streamit-api-inapp-payment">
            <h2><?php esc_html_e('Instructions !', 'streamit-api') ?></h2>
            <div class="instructions-box">
                <p><?php esc_html_e('In order for the in-app purchase feature to work,', 'streamit-api') ?></p>
                <p><?php esc_html_e('- You must enter the `Entitlement ID` and one of the API keys according to your app', 'streamit-api') ?></p>
                <p><?php esc_html_e('- Enter both API keys if your app supports iOS and Android.', 'streamit-api') ?></p>
            </div>

            <div class="instructions-info">
                <p class="instruction-link">
            <?php
            echo sprintf(
                '<a class="instruction-url" href="%s" target="_blank">Click Here</a> To know how to get `Entitlement ID`?',
                esc_url('https://www.revenuecat.com/docs/getting-started/entitlements#creating-an-entitlement')
            );
            ?>
            </p>
            <p class="instruction-link">

            <?php
            echo sprintf(
                '<a class="instruction-url" href="%s" target="_blank">Click Here</a> To know how to get `Android & Apple` API keys?',
                esc_url('https://www.revenuecat.com/docs/welcome/authentication#obtaining-api-keys')
            ); ?>
            </p>
            
            </div> 

            <?php 
            $field_controller->get_text_field(
                array(
                    'key'   => 'entitlement_id',
                    'id'    => '',
                    'label' => esc_html__('Entitlement ID', 'streamit-api'),
                    'type'  => 'text',
                    'value' => isset($value['entitlement_id']) ? $value['entitlement_id'] : '',
                )
            );

            $field_controller->get_text_field(
                array(
                    'key'   => 'google_api_key',
                    'id'    => '',
                    'label' => esc_html__('Google API key', 'streamit-api'),
                    'type'  => 'text',
                    'value' => isset($value['google_api_key']) ? $value['google_api_key'] : '',
                )
            );

            $field_controller->get_text_field(
                array(
                    'key'   => 'apple_api_key',
                    'id'    => '',
                    'label' => esc_html__('Apple API key', 'streamit-api'),
                    'type'  => 'text',
                    'value' => isset($value['apple_api_key']) ? $value['apple_api_key'] : '',
                )
            );

           ?>

           


        </div>
        <?php 
        
        $field_controller->get_button_field(array(
            'key'           => 'st-admin-option-memebrship',
            'label'         => '',
            'button_text'   => esc_html__('Submit', 'streamit-api'),
            'class'         => 'button',
            'type'          => 'submit',
        ));
        ?>
        

    </div>
</form>
</div>