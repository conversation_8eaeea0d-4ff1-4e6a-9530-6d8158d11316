<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://iqonic.design
 * @since      1.0.0
 *
 * @package    Streamit_Api
 * @subpackage Streamit_Api/admin
 */

class Streamit_Api_Admin
{

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param      string    $plugin_name       The name of this plugin.
	 * @param      string    $version    The version of this plugin.
	 */
	public function __construct($plugin_name, $version)
	{

		$this->plugin_name = $plugin_name;
		$this->version = $version;
		$this->include();
		$this->event_handler();
		add_action('in_admin_header', 		[$this, 'in_admin_header']);
		add_action('admin_enqueue_scripts', [$this, 'admin_enqueue_scripts']);

		add_action('admin_menu', 			[$this, 'admin_menu_options']);

		$parent_theme = get_template(); // Returns 'streamit' if the parent theme is active
		$child_theme  = get_stylesheet(); // Returns the active theme (child or parent
		//MIgration NOtices
		if ($parent_theme !== 'streamit' && $child_theme !== 'streamit') {
			if (class_exists('MasVideos')) {
				add_action('admin_notices', 		[$this, 'st_app_migration_notice']);
			}
			add_action('admin_notices', [$this, 'st_app_plugin_upgrade_notice']);
		}
	}

	public function include()
	{
		require_once STREAMIT_API_DIR . 'admin/functions/streamit-api-general-functions.php';
		require_once STREAMIT_API_DIR . 'admin/class-streamit-api-routes.php';
		require_once STREAMIT_API_DIR . 'admin/class-streamit-api-routes-handler.php';
		require_once STREAMIT_API_DIR . 'admin/class-streamit-api-field-controller.php';
	}

	public function event_handler()
	{
		add_action('streamit_api_admin_tabs_data', [$this, 'streamit_api_admin_tabs_data'], 10);
		add_action('streamit_api_admin_options_data', [$this, 'streamit_api_admin_options_data'], 10);
	}

	public function streamit_api_admin_tabs_data()
	{
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-home.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-movie.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-tvshow.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-video.php';
	}

	public function admin_menu_options()
	{

		add_menu_page(
			esc_html__('App Options', 'streamit-api'),
			esc_html__('App Options', 'streamit-api'),
			'manage_options',
			'streamit-app-configuration',
			[$this, 'adminDashboard'],
			STREAMIT_API_DIR_URI . 'admin/assets/images/sidebar-icon.png',
			4
		);

		add_submenu_page(
			"streamit-app-configuration",
			esc_html__('App Content', 'streamit-api'),
			esc_html__('App Content', 'streamit-api'),
			'manage_options',
			'streamit-app-configuration',
			[$this, 'adminDashboard'],
		);

		//  app settings
		add_submenu_page(
			"streamit-app-configuration",
			esc_html__('App Settings', 'streamit-api'),
			esc_html__('Settings', 'streamit-api'),
			'manage_options',
			'st-app-options',
			[$this, 'app_options_page']
		);

		//  migration settings
		if (class_exists('MasVideos')) :
			add_submenu_page(
				"streamit-app-configuration",
				esc_html__('Migration Settings', 'streamit-api'),
				esc_html__('Migration', 'streamit-api'),
				'manage_options',
				'st-app-migration',
				[$this, 'app_migration_page']
			);
		endif;
	}
	public function adminDashboard()
	{
		$options_tabs = $this->streamit_api_admin_tabs();
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-pannel.php';
	}

	public function streamit_api_admin_tabs()
	{
		$tabs = apply_filters(
			'streamit_api_admin_tabs',
			array(
				'home' => array(
					'label' => esc_html__('Home', 'streamit-api'),
					'target' => 'admin_home_tab',
					'class' => array(),
				),
				'movie' => array(
					'label' => esc_html__('Movies', 'streamit-api'),
					'target' => 'admin_movie_tab',
					'class' => array(),
				),
				'tvshow' => array(
					'label' => esc_html__('TV Shows', 'streamit-api'),
					'target' => 'admin_tvshow_tab',
					'class' => array(),
				),
				'video' => array(
					'label' => esc_html__('Video', 'streamit-api'),
					'target' => 'admin_video_tab',
					'class' => array(),
				),
			)
		);

		return $tabs;
	}
	public function admin_enqueue_scripts()
	{
		wp_enqueue_script('jquery');

		// Enqueue Select2 script and styles
		wp_enqueue_script(
			'streamit-api-select2',
			STREAMIT_API_DIR_URI . 'admin/js/select2.js',
			array('jquery'),
			'1.0',
			true
		);

		wp_enqueue_style(
			'streamit-api-select2',
			STREAMIT_API_DIR_URI . 'admin/css/select2.css',
			array(),
			'1.0'
		);

		wp_enqueue_script(
			'streamit-api-admin',
			STREAMIT_API_DIR_URI . 'admin/js/streamit-api-admin.js',
			array('jquery'),
			'1.0',
			true
		);

		wp_enqueue_style(
			'streamit-api-style',
			STREAMIT_API_DIR_URI . 'admin/css/streamit-api-admin.css',
			array(),
			'1.0'
		);

		add_filter('script_loader_tag', function ($tag, $handle, $src) {
			if ($handle === 'streamit-api-admin') {
				$tag = '<script type="module" src="' . esc_url($src) . '"></script>';
			}
			return $tag;
		}, 10, 3);

		wp_localize_script(
			'streamit-api-admin',
			'streamitapiAjax',
			[
				'ajaxurl' => admin_url('admin-ajax.php'),
				'_ajax_nonce' => wp_create_nonce('streamit_api_ajax_nonce'),
				'st_migrationUrl' => admin_url('admin.php?page=streamit-migration'),
			]
		);
	}

	public function app_options_page()
	{
		$tabs = $this->app_options_tabs();
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-options.php';
	}

	public function app_options_tabs()
	{
		$tabs = apply_filters(
			'streamit_api_admin_options',
			array(
				'general' => array(
					'label' => esc_html__('General', 'streamit-api'),
					'target' => 'admin_options_general',
					'class' => array(),
				),
				'comment' => array(
					'label' => esc_html__('Rate and Review', 'streamit-api'),
					'target' => 'admin_options_comment',
					'class' => array(),
				),
				'limitslogin' => array(
					'label' => esc_html__('Limits Logins', 'streamit-api'),
					'target' => 'admin_options_limitslogin',
					'class' => array(),
				),
				'membership' => array(
					'label' => esc_html__('Membership', 'streamit-api'),
					'target' => 'admin_options_membership',
					'class' => array(),
				),
			)
		);

		return $tabs;
	}

	public function streamit_api_admin_options_data()
	{
		$api_options = get_option('st_app_options');
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-firebase-option.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-comment-option.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-limitlogins-option.php';
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-membership-option.php';
	}

	public function app_migration_page()
	{
		require_once STREAMIT_API_DIR . 'admin/view/html-admin-migration-template.php';
	}

	public function in_admin_header()
	{

		$current_screen = get_current_screen();
		$streamit_page_list = array(
			'toplevel_page_streamit-app-configuration',
			'app-options_page_st-app-options',
			'app-options_page_st-app-migration'
		);
		if (in_array($current_screen->id, $streamit_page_list)) {
			remove_all_actions('admin_notices');
			remove_all_actions('all_admin_notices');
		}
	}

	public function st_app_migration_notice()
	{
?>
		<div class="notice streamit-migration-notice is-dismissible">
			<div class="streamit-migration-notice-inner">
				<div class="notice-left-img">
					<img src="<?php echo esc_url(plugin_dir_url(__FILE__) . 'assets/images/streamit-small-logo.png'); ?>" alt="">
				</div>
				<div>
					<p><strong>
							<?php esc_html_e(
								'🚀 Upgrade to Streamit 4.0 effortlessly! Migrate Movies, TV Shows, and Videos data from older versions with a single click.',
								'streamit'
							); ?>
						</strong></p>
					<p>
						<a target="_blank" href="<?php echo esc_url('https://documentation.iqonic.design/streamit/new-streamit/whats-new'); ?>">
							<?php esc_html_e('📌 What’s New?', 'streamit'); ?>
						</a>
					</p>
				</div>
			</div>
			<div>
				<form action="" id="sa_download_plugin">
					<button type="submit" class="streamit-migration-button">
						<?php echo esc_html__('Download & Install Plugin', 'streamit-api'); ?>
					</button>
				</form>
			</div>
		</div>

		<?php
	}

	/**
	 * Displays an admin notice if the Streamit plugin version is below 1.0.1.
	 */
	public function st_app_plugin_upgrade_notice()
	{
		// Check if the plugin version is defined and less than 1.0.1.
		$streamit_plugin_version = '1.1.0';
		if (defined('STREAMIT_VERSION') && version_compare(STREAMIT_VERSION, $streamit_plugin_version, '<')) {
		?>
			<div class="notice streamit-migration-notice is-dismissible">
				<div class="streamit-migration-notice-inner">
					<div class="notice-left-img">
						<img src="<?php echo esc_url(plugin_dir_url(__FILE__) . 'assets/images/streamit-small-logo.png'); ?>" alt="">
					</div>
					<div>
						<p>
							<?php
							/* translators: %1$s: new version, %2$s: URL to release notes or update page */
							echo wp_kses_post(
								sprintf(
									__('A new update for Streamit plugin is available! Please update to version %1$s for improved features and security. <a href="%2$s" target="_blank">%3$s</a>', 'streamit'),
									$streamit_plugin_version,
									esc_url('https://documentation.iqonic.design/streamit/getting-started/update-plugin'), // Replace with your actual URL.
									esc_html__('Learn More', 'streamit-api')
								)
							);
							?>
						</p>
					</div>
				</div>
			</div>
<?php
		}
	}
}
