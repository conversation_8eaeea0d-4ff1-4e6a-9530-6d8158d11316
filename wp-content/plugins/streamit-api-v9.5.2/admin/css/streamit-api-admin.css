:root {

    --admin-input-border-color: #e50914;
    --admin-input-btn-back: #1c2236;
    --admin-input-back-color: #121623;
    --admin-border-radius: 4px;
    --admin-border-radius-box: 5px;
    --admin-border-color: #262525;
    --admin-background-color: #000;
    --admin-lable-text-color: #ffffff;
    --admin-button-bg: #e50914;
    --admin-button-hover-bg: #B70710;
    --light-bg-color: #141314;

}

body.toplevel_page_streamit-app-configuration,
body.app-options_page_st-app-migration,
body.app-options_page_st-app-options {
    background-color: var(--light-bg-color);  
}

.body.toplevel_page_streamit-app-configuration h1,
.body.toplevel_page_streamit-app-configuration h2,
.body.toplevel_page_streamit-app-configuration h3,
.body.toplevel_page_streamit-app-configuration h4,
.body.toplevel_page_streamit-app-configuration h5,
.body.toplevel_page_streamit-app-configuration h6 {
        color: var(--admin-lable-text-color); 
}

#streamit_api_admin_options{
    background-color: var(--admin-background-color);
    padding: 30px;
    border-radius: var(--admin-border-radius);
}
#streamit_api_admin_options .streamit-api-tabs{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 6px;
    transition: all .3s ease-in-out;
}
#streamit_api_admin_options .streamit-api-tabs li{
    display: flex;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
    padding: 8px 16px;
}
#streamit_api_admin_options .streamit-api-tabs a{
    text-decoration: none;
    color: var(--admin-lable-text-color);
    font-size: 1rem;
    text-align: center;
    
}
#streamit_api_admin_options .streamit-api-tabs .active{
    background: var(--admin-button-bg);
    border-radius: var(--admin-border-radius);
}
.streamit_admin_option_pannel{
    background-color: #141314;
    margin: auto;
    margin-top: 30px;
    padding: 30px;
    border-radius: var(--admin-border-radius);
}

.streamit_admin_option_pannel .streamit_api_slider_section .streamit_api_add_slider_field,
.streamit_admin_option_pannel .streamit_api_slider_section .streamit_api_add_slider_movie_field,
.streamit_admin_option_pannel .streamit_api_slider_section .streamit_api_add_slider_tvshow_field,
.streamit_admin_option_pannel .streamit_api_slider_section .streamit_api_add_slider_video_field{
    display: flex;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 16px;
}

#st-admin-option-dashboard .streamit_api_slider_section .streamit_api_add_slider_field label,
#st-admin-option-dashboard-movie .streamit_api_slider_section .streamit_api_add_slider_movie_field label,
#st-admin-option-dashboard-tvshow .streamit_api_slider_section .streamit_api_add_slider_tvshow_field label,
#st-admin-option-dashboard-video .streamit_api_slider_section .streamit_api_add_slider_video_field label{
    margin-bottom: 0;    
} 
#st-admin-option-dashboard h5,#st-admin-option-dashboard-movie h5,#st-admin-option-dashboard-tvshow h5, #st-admin-option-dashboard-video h5{
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 30px;
    margin-top: 0;
    color: var(--admin-lable-text-color);

}
#streamit_api_banner_select,#streamit_api_add_slider, #streamit_api_banner_select_movie ,#streamit_api_add_slider_movie ,#streamit_api_banner_select_tvshow ,#streamit_api_add_slider_tvshow,#streamit_api_banner_select_video,#streamit_api_add_slider_video ,#streamit_api_dashboard_submit ,#streamit_api_dashbord_submit{
    background: var(--admin-button-bg);
    border: none;
    color: var(--admin-lable-text-color);
    font-size: 14px;
    line-height: 20px;
    padding: 8px 16px;
    border-radius: var(--admin-border-radius);
  }

 #st-admin-option-dashboard .select2-container--default .select2-selection--single ,#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single,
 #st-admin-option-dashboard-movie .select2-container--default .select2-selection--single ,#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single,
 #st-admin-option-dashboard-tvshow .select2-container--default .select2-selection--single ,#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single,
 #st-admin-option-dashboard-video .select2-container--default .select2-selection--single ,#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single{
    background-color: var(--light-bg-color);
    height: 40px;
    vertical-align: middle;
    border-color: var(--admin-border-color);
}

#st-admin-option-dashboard .select2-container--default .select2-selection--single .select2-selection__rendered,
#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single .select2-selection__rendered,
#st-admin-option-dashboard-tvshow .select2-container--default .select2-selection--single .select2-selection__rendered,
#st-admin-option-dashboard-video .select2-container--default .select2-selection--single .select2-selection__rendered{
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 28px;
    color: var(--admin-lable-text-color);
    height: 40px;
    line-height: 38px;  
    vertical-align: middle;
}
#st-admin-option-dashboard .select2-container--default .select2-selection--single .select2-selection__rendered .select2-selection__arrow,
#st-admin-option-dashboard-movie .select2-container--default .select2-selection--single .select2-selection__rendered .select2-selection__arrow,
#st-admin-option-dashboard-tvshow .select2-container--default .select2-selection--single .select2-selection__rendered .select2-selection__arrow,
#st-admin-option-dashboard-video .select2-container--default .select2-selection--single .select2-selection__rendered .select2-selection__arrow{
    width: 20px;
    color: var(--admin-lable-text-color);
    height: 40px;
    line-height: 38px;
    vertical-align: middle;
}
#st-admin-option-dashboard .form-field label ,#st-admin-option-dashboard-movie .form-field label, #st-admin-option-dashboard-tvshow .form-field label ,#st-admin-option-dashboard-video .form-field label {
    display: block;
    margin-bottom: 8px;
    color: var(--admin-lable-text-color);
    font-size: 1rem;
}
#st-admin-option-dashboard :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field),#st-admin-option-dashboard-movie :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field),#st-admin-option-dashboard-tvshow :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field),#st-admin-option-dashboard-video :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field){
    background-color: var(--light-bg-color);
    color: var(--admin-lable-text-color);
    width: 100%;
    min-height: 40px;
    height: auto;
    max-width: 100%;
    border-color: var(--admin-border-color);
}
:is(.streamit-wraper-table, #st-admin-option-dashboard ) input:is([type="checkbox"], [type="radio"]), :is(.streamit-wraper-table, #st-admin-option-dashboard) .table-view-list input[type="checkbox"] {
    background: var(--admin-background-color);
   
}
#st-admin-option-dashboard input:is( [type="radio"]):checked::before {
    background-color: var(--admin-button-hover-bg);
    border:  var(--admin-button-hover-bg);
    
}
#st-admin-option-dashboard .streamit_api_banner_select_field, .streamit_api_banner_select_movie_field, .streamit_api_banner_select_tvshow_field, .streamit_api_banner_select_video_field{
    display: flex;
    gap: 16px;
    margin: 16px 0;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}
#st-admin-option-dashboard .streamit_api_banner_select_field label,
#st-admin-option-dashboard-tvshow .streamit_api_banner_select_tvshow_field label,
#st-admin-option-dashboard-movie .streamit_api_banner_select_movie_field label,
#st-admin-option-dashboard-video .streamit_api_banner_select_video_field label{
    margin-bottom: 0;
}

#st-admin-option-dashboard .streamit_api_slider_section .streamit_api_single_slider,
#st-admin-option-dashboard-movie .streamit_api_slider_section .streamit_api_single_slider,
#st-admin-option-dashboard-tvshow .streamit_api_slider_section .streamit_api_single_slider,
#st-admin-option-dashboard-video .streamit_api_slider_section .streamit_api_single_slider,
#st-admin-option-dashboard .streamit_api_banner_sections .banner_display_list,
#st-admin-option-dashboard-movie .streamit_api_banner_sections .banner_display_list,
#st-admin-option-dashboard-tvshow .streamit_api_banner_sections .banner_display_list,
#st-admin-option-dashboard-video .streamit_api_banner_sections .banner_display_list {
    background: var(--admin-background-color);
    border-radius: var(--admin-border-radius);
    padding: 15px 20px;
    margin-bottom: 32px;
}
#st-admin-option-dashboard .streamit_api_single_slider #type_field,
#st-admin-option-dashboard-movie .streamit_api_single_slider #type_field,
#st-admin-option-dashboard-tvshow .streamit_api_single_slider #type_field,
#st-admin-option-dashboard-video .streamit_api_single_slider #type_field{
    margin-bottom: 0;
}

#st-admin-option-dashboard .remove_slider,
#st-admin-option-dashboard-movie .remove_slider,
#st-admin-option-dashboard-tvshow .remove_slider,
#st-admin-option-dashboard-video .remove_slider,
#st-admin-option-dashboard .remove_banner,
#st-admin-option-dashboard-movie .remove_banner,
#st-admin-option-dashboard-tvshow .remove_banner,
#st-admin-option-dashboard-video .remove_banner{
    display: flex;
    justify-content: end;
}
.streamit_api_single_slider .remove_slider .remove_selected_slider,
.streamit_api_banner_section .remove_banner .remove_selected_banner{
    background: var(--admin-button-bg);
    border: none;
    color: var(--admin-lable-text-color);
    font-size: 14px;
    line-height: 20px;
    padding: 8px 16px;
    border-radius: var(--admin-border-radius);
}
#st-admin-option-dashboard .view_all_field,
#st-admin-option-dashboard-movie .view_all_field,
#st-admin-option-dashboard-tvshow .view_all_field,
#st-admin-option-dashboard-video .view_all_field{
    display: flex;
    gap: 16px;
    align-items: center;
}
#st-admin-option-dashboard .view_all_field label,
#st-admin-option-dashboard-movie .view_all_field label,
#st-admin-option-dashboard-tvshow .view_all_field label,
#st-admin-option-dashboard-video .view_all_field label{
    margin-bottom: 0;
}
#st-admin-option-dashboard .view_all_field input[type=checkbox],
#st-admin-option-dashboard-movie .view_all_field input[type=checkbox],
#st-admin-option-dashboard-tvshow .view_all_field input[type=checkbox],
#st-admin-option-dashboard-video .view_all_field input[type=checkbox] {
    background: transparent;
}
#st-admin-option-dashboard .view_all_field  input[type=checkbox]:focus,
#st-admin-option-dashboard-movie .view_all_field input[type=checkbox]:focus,
#st-admin-option-dashboard-tvshow .view_all_field input[type=checkbox]:focus,
#st-admin-option-dashboard-video .view_all_field input[type=checkbox]:focus{
    box-shadow: none;
    border-color: var(--admin-button-bg); 
}
#st-admin-option-dashboard .view_all_field input[type=checkbox]:checked::before,
#st-admin-option-dashboard-movie .view_all_field input[type=checkbox]:checked::before,
#st-admin-option-dashboard-tvshow .view_all_field input[type=checkbox]:checked::before,
#st-admin-option-dashboard-video .view_all_field input[type=checkbox]:checked::before{
    content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M14.83 4.89l1.34.94-5.81 8.38H9.02L5.78 9.67l1.34-1.25 2.57 2.4z" fill="%23b70710"/></svg>') !important;
}
#st-admin-option-dashboard .form-field,
#st-admin-option-dashboard-movie .form-field,
#st-admin-option-dashboard-tvshow .form-field,
#st-admin-option-dashboard-video .form-field{
    margin-bottom: 24px;
}
#st-admin-option-dashboard :not(.options_group) .select2-selection :is(.select2-selection__rendered, .select2-selection__arrow),
#st-admin-option-dashboard-movie :not(.options_group) .select2-selection :is(.select2-selection__rendered, .select2-selection__arrow),
#st-admin-option-dashboard-tvshow :not(.options_group) .select2-selection :is(.select2-selection__rendered, .select2-selection__arrow),
#st-admin-option-dashboard-video :not(.options_group) .select2-selection :is(.select2-selection__rendered, .select2-selection__arrow) {
    line-height: 35px;
    vertical-align: middle;
    display: inline-flex;
    flex-wrap: wrap;
    min-height: 40px;
    margin: 0;
}
#st-admin-option-dashboard .select2-selection--multiple .select2-selection__choice,
#st-admin-option-dashboard-movie .select2-selection--multiple .select2-selection__choice,
#st-admin-option-dashboard-tvshow .select2-selection--multiple .select2-selection__choice,
#st-admin-option-dashboard-video .select2-selection--multiple .select2-selection__choice {
    background: var(--admin-button-bg);
    line-height: 24px;
    padding-right: 5px;
    padding-left: 20px;
    border-color: var(--admin-input-border-color);
    font-size: 12px;
}
#st-admin-option-dashboard .select2-selection--multiple,
#st-admin-option-dashboard-movie .select2-selection--multiple,
#st-admin-option-dashboard-tvshow .select2-selection--multiple,
#st-admin-option-dashboard-video .select2-selection--multiple {
    background-color: var(--light-bg-color);
    color: var(--admin-lable-text-color);
    min-height: 40px;
    border-color: var(--admin-border-color);
    padding-bottom: 0 !important;
}
#st-admin-option-dashboard .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove,
#st-admin-option-dashboard-movie .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove,
#st-admin-option-dashboard-tvshow .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove,
#st-admin-option-dashboard-video .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    margin-right: 0px !important;
    border: none;
    color: var(--admin-lable-text-color);
}

/* Media */
.streamit_api_media_preview-wrap:has(.streamit_media_preview) {
    display: none;
}       

.streamit_api_media_preview-wrap:has(.streamit_media_preview>:is(img, video)) {
    display: block;
}
.streamit_api_media_preview-wrap .streamit_media_preview {
    height: 100%;
    display: flex;
}
.streamit_api_media_preview-wrap .streamit_media_preview :is(img, video) {
    max-height: 100%;
    max-width: 100% !important;
    display: flex;
    margin: 0 auto;
    vertical-align: middle;
    border-radius: var(--admin-border-radius);
}
.streamit_api_media_preview-wrap .streamit_api_remove_video_button {
    position: absolute;
    z-index: 9999;
    top: 5px;
    right: 5px;
    background-color: var(--admin-button-bg);
    color: var(--admin-lable-text-color);
    font-size: 12px;
    line-height: 20px;
    border: none;
    transition: all .5s ease-in-out;
}
.streamit_api_media_preview-wrap {
    position: absolute;
    top: calc(50% + 15px);
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: var(--light-bg-color);
    width: 90%;
    height: 70%;
}


/* app option setting */
#streamit_api_admin_settings{
    border-radius: var(--admin-border-radius);
    padding: 30px 20px;
    vertical-align: middle;
    color: var(--admin-lable-text-color);
    text-transform: capitalize;
    background: var(--admin-background-color);
    min-height: 100%;
}

.app-options_page_app-options #streamit_api_admin_settings h1,
.app-options_page_app-options #streamit_api_admin_settings h2,
.app-options_page_app-options #streamit_api_admin_settings h3,
.app-options_page_app-options #streamit_api_admin_settings h4,
.app-options_page_app-options #streamit_api_admin_settings h5,
.app-options_page_app-options #streamit_api_admin_settings h6 {
    color: var(--admin-lable-text-color);
}

#streamit_api_admin_settings h1,
#streamit_api_admin_settings h2,
#streamit_api_admin_settings h3,
#streamit_api_admin_settings h4,
#streamit_api_admin_settings h5,
#streamit_api_admin_settings h6 {
    color: var(--admin-lable-text-color);
}

.app-options_page_app-options #streamit_api_admin_settings h1, 
.app-options_page_app-options #streamit_api_admin_settings h2{
    margin-top: 0;
}
#streamit_api_admin_settings .streamit-api-options-tabs{
    margin-bottom: 2em;
    margin-top: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    background: transparent;
    border-bottom: 0.063em solid var(--admin-border-color);
}
#streamit_api_admin_settings .streamit-api-options-tabs::before {
    position: absolute;
    content: " ";
    width: 100%;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid var(--admin-border-color);
    z-index: 1;
}
#streamit_api_admin_settings .streamit-api-options-tabs li {
    background-color: transparent;
    border: none;
    padding: 0.7em 1em;
    margin: 0 1em;
    text-align: center;
    position: relative;
}
#streamit_api_admin_settings .streamit-api-options-tabs li a {
    font-size: 1.333em;
    line-height: 1.2;
    color: var(--admin-lable-text-color);
    padding: 0.7em 1em;
    letter-spacing: -.02em;
    text-decoration: none;
}
#streamit_api_admin_settings .streamit-api-options-tabs li.active a {
    color: var(--admin-button-bg);
}
#streamit_api_admin_settings .streamit-api-options-tabs li a::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 0.063em;
    background: var(--admin-button-bg);
    opacity: 0;
    transition: all 0.8s linear;
}
#streamit_api_admin_settings .streamit-api-options-tabs li.active a::after {
    opacity: 1;
    width: 100%;
}
#streamit_api_admin_settings .streamit-api-options-tabs li:hover a {
    color: var(--admin-button-bg);
}
#st-admin-settings-general .show_titles_field {
    display: flex;
    align-items: center;
    gap: .5em;
}
#streamit_api_admin_settings label {
    display: inline-block;
    margin-bottom: 12px;
}
#streamit_api_admin_settings .form-field{
    margin: 24px 0;
}
#streamit_api_admin_settings :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field), #st-admin-option-dashboard-movie :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field), #st-admin-option-dashboard-tvshow :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field), #st-admin-option-dashboard-video :is(input, select, .select2):not([type="radio"], [type="button"], [type="checkbox"], [type="submit"], .select2-search__field) {
    background-color: var(--light-bg-color);
    color: var(--admin-lable-text-color);
    width: 100%;
    min-height: 40px;
    height: auto;
    max-width: 100%;
    border-color: var(--admin-border-color);
}

#streamit_api_admin_settings input[type=checkbox]:focus, 
#streamit_api_admin_settings input[type=color]:focus, 
#streamit_api_admin_settings input[type=date]:focus, 
#streamit_api_admin_settings input[type=datetime-local]:focus, 
#streamit_api_admin_settings input[type=datetime]:focus, 
#streamit_api_admin_settings input[type=email]:focus, 
#streamit_api_admin_settings input[type=month]:focus, 
#streamit_api_admin_settings input[type=number]:focus, 
#streamit_api_admin_settings input[type=password]:focus, 
#streamit_api_admin_settings input[type=radio]:focus, 
#streamit_api_admin_settings input[type=search]:focus, 
#streamit_api_admin_settings input[type=tel]:focus, 
#streamit_api_admin_settings input[type=text]:focus, 
#streamit_api_admin_settings input[type=time]:focus, 
#streamit_api_admin_settings input[type=url]:focus, 
#streamit_api_admin_settings input[type=week]:focus, 
#streamit_api_admin_settings select:focus, 
#streamit_api_admin_settings textarea:focus {
    border-color: var(--admin-input-border-color);
    box-shadow: none;
}
#streamit_api_admin_settings .switch {
    display: flex;
    gap: 4px;
    align-items: center;
}
#streamit_api_admin_settings .switch label {
    margin-bottom: 0;
}
#streamit_api_admin_settings .switch input[type=checkbox]{
    background: transparent;
    margin: 0;
}
#streamit_api_admin_settings .switch input[type=checkbox]:checked::before{
    content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M14.83 4.89l1.34.94-5.81 8.38H9.02L5.78 9.67l1.34-1.25 2.57 2.4z" fill="%23b70710"/></svg>') !important;
}
#streamit_api_admin_settings input:is( [type="radio"]):checked::before {
        background-color: var(--admin-button-hover-bg);
}      
#streamit_api_admin_settings .switch input[type=checkbox]:focus{
    box-shadow: none;
    border-color: var(--admin-button-bg); 
}
input[type=number]:focus{
    box-shadow: none;
    border-color: var(--admin-button-bg); 
}
.streamit_admin_setting_pannel .movie_comments_field,
.streamit_admin_setting_pannel .tvshow_comments_field,
.streamit_admin_setting_pannel .episode_comments_field,
.streamit_admin_setting_pannel .video_comments_field{
    display: flex;
    gap: 8px;
    align-items: center;
}
#st-admin-settings-general.button,
#st-admin-settings-comment.button,
#st-admin-settings-limitlogin.button,
#st-admin-option-memebrship.button{
    background: var(--admin-button-bg);
    border: none;
    color: var(--admin-lable-text-color);
    font-size: 14px;
    line-height: 20px;
    padding: 8px 16px;
    border-radius: var(--admin-border-radius);
}
#st-admin-settings-membership .streamit-api-slider-radio-section .payment_type_field{
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    margin-bottom: 0;
}
#st-admin-settings-membership .streamit-api-slider-radio-section .payment_type_field .radio-group{
    display: flex;
    align-items: center;
    gap: 8px;
}
#st-admin-settings-membership .streamit-api-slider-radio-section .payment_type_field .radio-group input[type=radio]{
    background: transparent;
}
#st-admin-settings-membership .streamit-api-slider-radio-section .payment_type_field .radio-group input[type=radio]:focus{
    border-color: var(--admin-input-border-color);
    box-shadow: none;
}
#st-admin-settings-membership .instructions-box{
    color: var(--admin-button-bg);
    font-weight: 700;
}
#st-admin-settings-membership .instructions-box p{
    margin-top: 0;
}
#st-admin-settings-membership .instructions-info{
    padding-left:10px;
    border: 4px solid var(--light-bg-color);
    border-left: 12px solid var(--light-bg-color);
    line-height:30px;
    margin-bottom: 16px;
}
#st-admin-settings-membership .instructions-info .instruction-link{
    margin-bottom: 8px;
}
#st-admin-settings-membership .instructions-info .instruction-link .instruction-url{
    text-decoration: none;
    font-weight: 700;
    color: var(--admin-button-bg);
}

.toplevel_page_streamit-app-configuration .streamit-api-logo {
    padding: 15px 0;
}

.toplevel_page_streamit-app-configuration .streamit-api-logo img {
    height: 40px;
}

.app-options_page_st-app-migration.wp-core-ui .button-primary {
    color: var(--admin-lable-text-color);
    background-color: var(--admin-button-bg);
    border-color: var(--admin-button-bg);
}

.app-options_page_st-app-migration.wp-core-ui .button-primary:hover {
background-color: var(--admin-button-hover-bg); 
border-color: var(--admin-button-hover-bg);   
}

.app-options_page_st-app-migration.wp-core-ui .button-primary:focus {
    background-color: var(--admin-button-hover-bg); 
    border-color: var(--admin-button-hover-bg);  
    box-shadow: none; 
}

.streamit_admin_setting_pannel {
    color: var(--admin-lable-text-color);
    background-color: var(--admin-background-color);
    padding: 24px;
    border-radius: var(--admin-border-radius);
    margin-top: 30px;     
}

.streamit_admin_setting_pannel  h3 {
    color: var(--admin-lable-text-color); 
    margin-top: 0;
}
.media-option {
    position: relative;
}
.media-option a.button:is(:focus, :hover),
.media-option a.button {
        min-height: 200px;
        padding: 20px;
        background: var(--light-bg-color);
        width: 100%;
        border: 2px dashed var(--admin-border-color);
        border-radius: var(--admin-border-radius);
        text-align: center;
        color: var(--admin-lable-text-color);
        box-shadow: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 15px;
}

.streamit_api_media_preview-wrap .streamit_media_preview :is(img, video) {
    max-height: 100%;
    max-width: 100% !important;
    display: flex;
    margin: 0 auto;
    vertical-align: middle;
    border-radius: var(--admin-border-radius);
}

.wp-person a:focus .gravatar, a:focus, a:focus .media-icon img, a:focus .plugin-icon {
    box-shadow: unset;
}

#streamit_api_admin_settings .streamit-api-options-tabs li a {
    font-size: 1.333em;
    line-height: 1.2;
    color: var(--admin-lable-text-color);
    padding: 0.7em 1em;
    letter-spacing: -.02em;
    text-decoration: none;
}

.streamit-migration-button {
    background: var(--admin-button-bg);
    color: var(--admin-lable-text-color);
    border: none;
    padding: 1em 2em;
    font-size: 1em;
    cursor: pointer;
    border-radius: .25em;
    display: flex;
    align-items: center;
}
/* Notic */
.streamit-migration-notice {
    background: #fff9e7; /* Eye-catching yellow */
    border-left: 5px solid #dba617; /* Orange accent */
    padding: 1em;
    font-size: .875em;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}
.streamit-migration-notice-inner {
    display: flex;
    align-items: center;
    gap: 1em;
}
.streamit-migration-notice-inner .notice-left-img img {
    height: 55px;
}

.streamit-migration-notice-inner p a {
    display: block;
}
.streamit-migration-notice strong {
    color: #000;
}
.streamit-migration-notice a {
    color: #e50914;
    text-decoration: none;
    font-weight: bold;
}
.streamit-migration-notice a:hover {
    color: #e50914;
    text-decoration: underline;
}
.streamit-migration-button {
    background: #e50914;
    color: #fff;
    border: none;
    padding: 1em 2em;
    font-size: 1em;
    cursor: pointer;
    border-radius: .25em;
    display: flex;
    align-items: center;
}
.streamit-migration-button:hover {
    background: rgb(183.2, 7.2, 16);
}
