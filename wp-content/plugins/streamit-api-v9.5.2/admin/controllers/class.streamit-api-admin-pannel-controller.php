<?php
final class streamit_api_admin_pannel_controler
{

    /**
     * add banner section in admin options.
     *
     * @param WP_REST_Request $request The REST request object.
     * @return WP_REST_Response|WP_Error Response object on success, error object on failure.
     */
    public function add_banner(WP_REST_Request $request)
    {
        try {
            $data = $request->get_param('data');

            $value['type'] = $data['selected_type'];


            ob_start();
            $i = $data['i'];
            require STREAMIT_API_DIR . 'admin/view/banners/html-admin-banner.php';
            $banner_html = ob_get_clean();

            return wp_send_json($banner_html);
        } catch (Exception $e) {
            // Handle exceptions and provide meaningful error messages.
            $code = $e->getCode();
            $message = $e->getMessage();

            return new WP_Error($code, $message, array('status' => $code));
        }
    }


    /**
     * add slider section in admin options.
     *
     * @param WP_REST_Request $request The REST request object.
     * @return WP_REST_Response|WP_Error Response object on success, error object on failure.
     */
    public function add_slider(WP_REST_Request $request)
    {
        try {
            $data = $request->get_param('data');
            ob_start();
            $type = $data['type'];
            $i = $data['i'];
            require STREAMIT_API_DIR . 'admin/view/sliders/html-admin-slider.php';
            $slider_html = ob_get_clean();

            return wp_send_json($slider_html);
        } catch (Exception $e) {
            // Handle exceptions and provide meaningful error messages.
            $code = $e->getCode();
            $message = $e->getMessage();

            return new WP_Error($code, $message, array('status' => $code));
        }
    }

    /**
     * Add dashboard data from admin options.
     *
     * @param WP_REST_Request $request The REST request object.
     * @return WP_REST_Response|WP_Error Response object on success, error object on failure.
     */
    public function submit_dashboard_data(WP_REST_Request $request)
    {
        try {
            // Retrieve parameters from the request
            $data = $request->get_params();
            // Check if the option and formData are set
            if (!isset($data['option']) || empty($data['option'])) {
                return new WP_Error('missing_option', esc_html__('Option parameter is missing or empty.', 'streamit-api'), array('status' => 400));
            }

            if (!isset($data['formData'])) {
                return new WP_Error('missing_form_data', esc_html__('Form data is missing.', 'streamit-api'), array('status' => 400));
            }

            $current_data = get_option($data['option']);
            $new_data = $data['formData'];

            if ($current_data === $new_data) {
                return wp_send_json_success(array(
                    'success' => false,
                    'message' => esc_html__('You have not made any changes.', 'streamit-api'),
                    'updated_option' => $data['option']
                ));
            }

            // Update the option in the database
            $result = update_option($data['option'], $data['formData']);
            if($result){
                return wp_send_json_success(array('success' => true, 'message' => esc_html__('Options updated successfully.', 'streamit-api') , 'updated_option' => $data['option']));
            }
            return wp_send_json_success(array('success' => true, 'message' => esc_html__('Something went wrong.', 'streamit-api') , 'updated_option' => $data['option']));
            // Return a success response
        } catch (Exception $e) {
            // Handle exceptions and provide meaningful error messages.
            $code = $e->getCode() ? $e->getCode() : 500; // Default to 500 if no code is set
            $message = $e->getMessage() ?: esc_html__('An unexpected error occurred.', 'streamit-api');

            return new WP_Error($code, $message, array('status' => $code));
        }
    }

    /**
     * Add setting data from admin settings.
     *
     * @param WP_REST_Request $request The REST request object.
     * @return WP_REST_Response|WP_Error Response object on success, error object on failure.
     */
    public function submit_settings(WP_REST_Request $request)
    {
        try {
            // Retrieve parameters from the request
            $data = $request->get_params();
            // Check if the necessary keys exist in the data
            if (empty($data['setting_key'])) {
                return wp_send_json(array(
                    'success' => false,
                    'message' => esc_html__('Setting key and data are required.', 'streamit-api'),
                ));
            }

            // Sanitize the inputs to prevent security issues
            $setting_key = sanitize_text_field($data['setting_key']);

            $new_value = $data['datavalue'];

            // Retrieve the existing settings
            $settings = get_option('st_app_options', array());
            $current_data = isset($settings[$setting_key]) ? $settings[$setting_key] : null;

            // Check if the value has changed
            if ($current_data === $new_value) {
                return wp_send_json(array(
                    'success' => false,
                    'message' => esc_html__('You have not made any changes.', 'streamit-api'),
                    'updated_option' => $setting_key
                ));
            }

            // Update the settings with the new value
            $settings[$setting_key] = $new_value;
            // Update the option in the database
            $result = update_option('st_app_options', $settings);
            // Check if the update was successful
            if ($result === false) {
                return wp_send_json(array(
                    'success' => false,
                    'message' => esc_html__('Failed to update settings in the database.', 'streamit-api'),
                ));
            }

            // Return a success response with the updated option
            return wp_send_json(array(
                'success' => true,
                'message' => esc_html__('Data update successfully.', 'streamit-api'),
                'updated_option' => $setting_key,
                'new_value' => $new_value // Optionally return the new value for confirmation
            ));
        } catch (Exception $e) {
            // Handle exceptions and provide meaningful error messages
            $code = $e->getCode() ? $e->getCode() : 500; // Default to 500 if no code is set
            $message = $e->getMessage() ?: esc_html__('An unexpected error occurred.', 'streamit-api');

            return new WP_Error($code, $message, array('status' => $code));
        }
    }

    public function install_import_plugin(WP_REST_Request $request)
    {
        // Define the plugin source URL
        $plugin_source = esc_url_raw('https://assets.iqonic.design/wp/plugins/streamit_4/streamit-import.zip');

        // Plugin slug (path relative to the plugins directory)
        $plugin_slug = 'streamit-import/streamit-import.php'; // Adjust to the correct plugin file path

        // Check if the plugin is already active
        if (is_plugin_active($plugin_slug)) {
            return wp_send_json(
                array('success' => true, 'message' => 'The plugin is already active'),
                200
            );
        }

        // Check if the plugin is already installed
        if ($this->is_plugin_installed($plugin_slug)) {
            // Activate the plugin if installed but not active
            $activation_result = activate_plugin($plugin_slug);
            if (is_wp_error($activation_result)) {
                return wp_send_json(
                    array('success' => false, 'message' => $activation_result->get_error_message()),
                    500
                );
            }

            return wp_send_json(
                array('success' => true, 'message' => 'The plugin was already installed and is now activated'),
                200
            );
        }

        // Check if the source URL is valid
        if (!filter_var($plugin_source, FILTER_VALIDATE_URL)) {
            return wp_send_json(
                array('success' => false, 'message' => 'Invalid plugin source URL'),
                400
            );
        }

        // Download the plugin ZIP file
        $plugin_path = download_url($plugin_source);

        if (is_wp_error($plugin_path)) {
            return wp_send_json(
                array('success' => false, 'message' => $plugin_path->get_error_message()),
                500
            );
        }

        // Include necessary WordPress files for installing plugins
        include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
        include_once ABSPATH . 'wp-admin/includes/plugin.php'; // Required for activation

        // Instantiate the plugin upgrader
        $upgrader = new Plugin_Upgrader();

        // Install the plugin from the ZIP file
        $result = $upgrader->install($plugin_path);

        // Clean up the downloaded ZIP file
        unlink($plugin_path);

        // Return the response based on the installation result
        if (is_wp_error($result)) {
            return wp_send_json(
                array('success' => false, 'message' => $result->get_error_message()),
                500
            );
        }

        // Activate the plugin after installation
        $activation_result = activate_plugin($plugin_slug);

        if (is_wp_error($activation_result)) {
            return wp_send_json(
                array('success' => false, 'message' => $activation_result->get_error_message()),
                500
            );
        }

        // Return success response
        return wp_send_json(
            array('success' => true, 'message' => 'Plugin installed and activated successfully'),
            200
        );
    }

    /**
     * Check if the plugin is installed
     *
     * @param string $plugin_slug The plugin file path (e.g. 'plugin-name/plugin-file.php')
     * @return bool
     */
    private function is_plugin_installed($plugin_slug)
    {
        // Get the list of installed plugins
        $installed_plugins = get_plugins();

        // Check if the plugin exists in the list of installed plugins
        return isset($installed_plugins[$plugin_slug]);
    }
}
