import { post, get } from "./ajax.js";
import notificationToast from "../js/notification-toast.js";
export default class Streamit_Api_Admin {
    constructor() {
        this.mediaUploader = null;
        this.defaultimageuploader = null;


        this.setupEventHandlers();
        this.AdminTabHandler();
        this.AdminOptionsHandler();
        this.SliderSelectionHandler();
        this.checkDefaultValues();
        this.MemberShipOptions();
        this.sliderOptionHandler();

        jQuery('.streamit_api_select2').each(function () {
            if (!jQuery(this).hasClass('select2-initialized')) {
                jQuery(this).select2({
                    width: '100%'
                }).addClass('select2-initialized');
            }
        });
    }


    setupEventHandlers() {
        //Add Banner
        jQuery(document.body).on("click", "#streamit_api_banner_select", this.AddBannerSection.bind(this));
        jQuery(document.body).on("click", "#streamit_api_banner_select_movie", this.AddMovieBannerSection.bind(this));
        jQuery(document.body).on("click", "#streamit_api_banner_select_video", this.AddVideoBannerSection.bind(this));
        jQuery(document.body).on("click", "#streamit_api_banner_select_tvshow", this.AddTvshowBannerSection.bind(this));

        // Add Banner Images
        jQuery(document.body).on("click", "#streamit_api_upload_banner_image", this.setupMediaUploader.bind(this));

        // Remove thumbnail button click 
        jQuery(document.body).on("click", "#streamit_api_remove_banner_image", this.removeBannerImage.bind(this));

        // Remove Banner
        jQuery(document.body).on("click", ".remove_selected_banner", this.removeSelectedBanner.bind(this));

        //Remove Sider
        jQuery(document.body).on("click", ".remove_selected_slider", this.removeSelectedSlider.bind(this));

        //add default image
        jQuery(document.body).on("click", "#streamit_api_upload_default_image", this.setupDefaultUploader.bind(this));
        jQuery(document.body).on("click", "#streamit_api_remove_default_image", this.removeDefaultUploader.bind(this));

       // add app logo
       jQuery(document.body).on("click", "#streamit_api_upload_app_logo", this.setupDefaultUploaderApplogo.bind(this));
       jQuery(document.body).on("click", "#streamit_api_remove_app_logo", this.removeDefaultUploaderApplogo.bind(this));  
       
        // add new slider
        jQuery(document.body).on("click", "#streamit_api_add_slider", this.AddSlider.bind(this));
        jQuery(document.body).on("click", "#streamit_api_add_slider_movie", this.AddMovieSlider.bind(this));
        jQuery(document.body).on("click", "#streamit_api_add_slider_video", this.AddVideoSlider.bind(this));
        jQuery(document.body).on("click", "#streamit_api_add_slider_tvshow", this.AddTvshowSlider.bind(this));


        // Submit form handler
        jQuery(document.body).on("submit", "#st-admin-option-dashboard", this.homeSubmit.bind(this));
        jQuery(document.body).on("submit", "#st-admin-option-dashboard-movie", this.movieSubmit.bind(this));
        jQuery(document.body).on("submit", "#st-admin-option-dashboard-video", this.videoSubmit.bind(this));
        jQuery(document.body).on("submit", "#st-admin-option-dashboard-tvshow", this.tvshowSubmit.bind(this));

        jQuery(document.body).on("submit", "#st-admin-settings-general", this.GeneralSettings.bind(this));
        jQuery(document.body).on("submit", "#st-admin-settings-comment", this.CommentSettings.bind(this));
        jQuery(document.body).on("submit", "#st-admin-settings-limitlogin", this.LimitLoginSettings.bind(this));
        jQuery(document.body).on("submit", "#st-admin-settings-membership", this.MembershipSettings.bind(this));


        //limit logins
        jQuery(document.body).on("click", "#limitlogin_is_enable", this.LimitLogin.bind(this));

        //install plugin for migration
        jQuery(document.body).on("submit", "#sa_download_plugin", this.inatllImportPlugin.bind(this));

    }

    setupDefaultUploaderApplogo(event) {
        event.preventDefault();

        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('#app_logo_field');
        const inputId = '#app_logo';

        const mediaUploader = wp.media({
            title: 'Select or Upload Image',
            library: {
                type: 'image'
            },
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        mediaUploader.on('select', () => {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            this.handleDefaultImageSelectionApplogo(attachment, parentSection, inputId);
        });

        mediaUploader.open();
    }

    handleDefaultImageSelectionApplogo(attachment, parentSection, inputId) {
        parentSection.find('.streamit_media_preview').html(`
            <img src="${attachment.url}" alt="Banner Thumbnail" style="max-width: 150px;">
        `);
        parentSection.find(inputId).val(attachment.id);
    }

    removeDefaultUploaderApplogo(event) {
        event.preventDefault();

        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('#app_logo_field');
        const inputId = '#app_logo';

        parentSection.find('.streamit_media_preview').html('');
        parentSection.find(inputId).val('');
    }

    setupDefaultUploader(event) {
        event.preventDefault();

        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('#default_image_field');

        const mediaUploader = wp.media({
            title: 'Select or Upload Image',
            library: {
                type: 'image'
            },
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        mediaUploader.on('select', () => {
            let attachment = mediaUploader.state().get('selection').first().toJSON();
            this.handleDefaultImageSelection(attachment, parentSection);
        });

        mediaUploader.open();
    }

    handleDefaultImageSelection(attachment, parentSection) {

        parentSection.find('.streamit_media_preview').html(`
            <img src="${attachment.url}" alt="Banner Thumbnail" style="max-width: 150px;">
        `);

        parentSection.find('#default_image').val(attachment.id);
    }

    removeDefaultUploader(event) {
        event.preventDefault();

        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('#default_image_field');

        parentSection.find('.streamit_media_preview').html('');

        parentSection.find('#default_image').val('');

    }

    // Function to handle each slider post type value
    manageSliderOptions(value, element, post_type, rel_value) {
        // Find the nearest .streamit_api_single_slider container from the passed element
        const sliderContainer = element.closest('.streamit_api_single_slider');

        // Hide all sections by default
        const allOptions = sliderContainer.querySelectorAll('.movie_slider_options, .video_slider_options, .tvshow_slider_options');
        allOptions.forEach(option => option.style.display = 'none');  // Hide all options

        // Show the selected section based on post type
        let innsersecton;
        if (value === 'movie') {
            innsersecton = sliderContainer.querySelector('.movie_slider_options');
        } else if (value === 'video') {
            innsersecton = sliderContainer.querySelector('.video_slider_options');
        } else if (value === 'tvshow') {
            innsersecton = sliderContainer.querySelector('.tvshow_slider_options');
        }

        if (innsersecton) {
            innsersecton.style.display = 'block';
        }

        // After showing the appropriate section, manage the 'select_fillters' logic
        this.manageFilterOptions(innsersecton, value, post_type, rel_value);
    }

    // Function to manage filter options (genre, tag, selected) based on the selected filter value
    manageFilterOptions(innsersecton, value, post_type, rel_value) {
        const FiltterId = '#' + value + '_select_fillters_' + post_type + '_' + rel_value;
        const filterSelect = innsersecton.querySelector(FiltterId);

        // Check if filterSelect exists
        if (filterSelect) {
            const selectedFilter = filterSelect.value;
            this.OperateSliderrInnerSection(innsersecton, selectedFilter);

            // Attach Select2 event handler using select2:select event
            jQuery(filterSelect).on('select2:select', (event) => {
                const value = event.target.value;
                this.OperateSliderrInnerSection(innsersecton, value);
            });
        } else {
            console.log('Filter select element not found for:', FiltterId); // Debugging: No filter select element found
        }
    }

    // Function to handle the visibility of genre, tag, and selected sections
    OperateSliderrInnerSection(innsersecton, value) {
        const genreSection = innsersecton.querySelector('.genre');
        const tagSection = innsersecton.querySelector('.tag');
        const selectedSection = innsersecton.querySelector('.selected');

        // By default, hide all
        genreSection.style.display = 'none';
        tagSection.style.display = 'none';
        selectedSection.style.display = 'none';

        // Show sections based on filter type
        if (value === 'genre') {
            genreSection.style.display = 'block';
        } else if (value === 'tag') {
            tagSection.style.display = 'block';
        } else if (value === 'selected') {
            selectedSection.style.display = 'block';
        }
    }

    // Slider option handler to manage dynamic post type selections
    sliderOptionHandler() {
        // Collect all the slider post type select elements
        const singleslider = document.querySelectorAll('.streamit_api_single_slider');

        // Loop through each slider container
        singleslider.forEach((slider) => {
            // Get the 'rel' attribute and #type element
            const rel_value = slider.getAttribute('rel');
            const post_type_element = slider.querySelector('#type');

            // Ensure the #type element exists and get its value
            if (post_type_element) {
                const post_type = post_type_element.value; // Get the post type value from #type element

                // Construct the dynamic selector for the post type element
                const SelectPostTypeElement = '#slider_post_type_' + post_type + '_' + rel_value;
                // Query the DOM for elements matching the dynamic selector
                const sliderPostTypes = slider.querySelector(SelectPostTypeElement);

                // Ensure sliderPostTypes exists before proceeding
                if (sliderPostTypes) {

                    // Pass the element directly to manageSliderOptions
                    this.manageSliderOptions(sliderPostTypes.value, sliderPostTypes, post_type, rel_value);

                    // Add an event listener for change events on the select element
                    jQuery(sliderPostTypes).on('select2:select', (event) => {
                        const value = event.target.value;
                        this.manageSliderOptions(value, event.target, post_type, rel_value); // Pass the target element directly
                    });
                } else {
                    console.log('Slider post type element not found:', SelectPostTypeElement); // Debugging: Element not found
                }
            } else {
                console.log('#type element not found in slider:', slider); // Debugging: #type element missing
            }
        });
    }


    AdminTabHandler() {
        if (jQuery('#streamit_api_admin_options').length) {
            jQuery('.streamit_admin_option_pannel').hide();
            jQuery('#admin_home_tab').show();
            jQuery('.home_options').addClass('active');

            jQuery('.streamit-api-tabs li').on('click', function (e) {
                e.preventDefault();

                jQuery('.streamit-api-tabs li').removeClass('active');
                jQuery('.streamit_admin_option_pannel').removeClass('active').hide();

                jQuery(this).addClass('active');

                var panelToShow = jQuery(this).find('a').attr('href');
                jQuery(panelToShow).addClass('active').show();
            });
        }
    }

    AdminOptionsHandler() {
        if (jQuery('#streamit_api_admin_settings').length) {
            jQuery('.streamit_admin_setting_pannel').hide();
            jQuery('#admin_options_general').show();
            jQuery('.general_options').addClass('active');

            jQuery('.streamit-api-options-tabs li').on('click', function (e) {
                e.preventDefault();

                jQuery('.streamit-api-options-tabs li').removeClass('active');
                jQuery('.streamit_admin_setting_pannel').removeClass('active').hide();

                jQuery(this).addClass('active');

                var panelToShow = jQuery(this).find('a').attr('href');
                jQuery(panelToShow).addClass('active').show();
            });
        }
    }

    AddBannerSection(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the banner sections
        const bannerSection = button.closest('.streamit_api_banner_section').find('.streamit_api_banner_sections');

        var lastSource = bannerSection.find('.banner_display_list').last();
        var relValue = lastSource.attr('rel');
        if (relValue == undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }

        let selectData = {
            selected_type: jQuery('#banner_select').val(),
            i: relValue
        }
        this.addbannertab(selectData, button, bannerSection);
    }

    AddMovieBannerSection(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the banner sections
        const bannerSection = button.closest('.streamit_api_banner_section').find('.streamit_api_banner_sections');

        var lastSource = bannerSection.find('.banner_display_list').last();
        var relValue = lastSource.attr('rel');
        if (relValue == undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }

        let selectData = {
            selected_type: 'movie',
            i: relValue
        }

        this.addbannertab(selectData, button, bannerSection);
    }

    AddVideoBannerSection(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the banner sections
        const bannerSection = button.closest('.streamit_api_banner_section').find('.streamit_api_banner_sections');

        var lastSource = bannerSection.find('.banner_display_list').last();
        var relValue = lastSource.attr('rel');
        if (relValue == undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }

        let selectData = {
            selected_type: 'video',
            i: relValue
        }

        this.addbannertab(selectData, button, bannerSection);
    }

    AddTvshowBannerSection(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the banner sections
        const bannerSection = button.closest('.streamit_api_banner_section').find('.streamit_api_banner_sections');

        var lastSource = bannerSection.find('.banner_display_list').last();
        var relValue = lastSource.attr('rel');
        if (relValue == undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }

        let selectData = {
            selected_type: 'tvshow',
            i: relValue
        }

        this.addbannertab(selectData, button, bannerSection);
    }

    addbannertab(data, button, bannerSection) {
        get('streamit_api_add_banner_tab', {
            data: data,
            _ajax_nonce: window.streamitAjax
        })
            .then(response => {
                if (bannerSection.length) {
                    // Append new content to the specific banner section
                    bannerSection.append(response);
                    // Initialize selectpicker if needed
                    jQuery('.streamit_api_select2').select2();
                    button.attr('disabled', false);
                }
            })
            .catch(error => {
                console.log(error);
                button.attr('disabled', false); // Re-enable button on error
            });
    }


    setupMediaUploader(event) {
        event.preventDefault();

        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('.banner_display_list');

        const mediaUploader = wp.media({
            title: 'Select or Upload Image',
            library: {
                type: 'image'
            },
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        mediaUploader.on('select', () => {
            let attachment = mediaUploader.state().get('selection').first().toJSON();
            this.handleBannerImageSelection(attachment, parentSection);
        });

        mediaUploader.open();
    }

    handleBannerImageSelection(attachment, parentSection) {

        parentSection.find('.streamit_media_preview').html(`
            <img src="${attachment.url}" alt="Banner Thumbnail" style="max-width: 150px;">
        `);

        parentSection.find('.upload_video_id').val(attachment.id);
        parentSection.find('.banner-error-msg').hide();
    }

    removeBannerImage(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        const parentSection = button.closest('.banner_display_list');

        parentSection.find('.streamit_media_preview').html('');

        parentSection.find('.upload_video_id').val('');
        parentSection.find('.banner-error-msg').show();
    }

    AddSlider(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the slider sections
        const bannerSection = button.closest('.streamit_api_slider_section');
        const sliderList = bannerSection.find('#streamit_api_slider_list');

        var lastSource = sliderList.find('.streamit_api_single_slider').last();
        var relValue = lastSource.attr('rel');


        if (relValue === undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }
        var data = {
            i: relValue,
            type: 'home'
        };
        this.addslidertab(data, button, sliderList);
    }

    AddMovieSlider(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the slider sections
        const bannerSection = button.closest('.streamit_api_slider_section');
        const sliderList = bannerSection.find('#streamit_api_slider_list');

        var lastSource = sliderList.find('.streamit_api_single_slider').last();
        var relValue = lastSource.attr('rel');


        if (relValue === undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }
        var data = {
            i: relValue,
            type: 'movie'
        };
        this.addslidertab(data, button, sliderList);
    }

    AddVideoSlider(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the slider sections
        const bannerSection = button.closest('.streamit_api_slider_section');
        const sliderList = bannerSection.find('#streamit_api_slider_list');

        var lastSource = sliderList.find('.streamit_api_single_slider').last();
        var relValue = lastSource.attr('rel');


        if (relValue === undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }
        var data = {
            i: relValue,
            type: 'video'
        };
        this.addslidertab(data, button, sliderList);
    }

    AddTvshowSlider(event) {
        event.preventDefault();
        const button = jQuery(event.currentTarget);
        button.attr('disabled', true);

        // Find the closest parent that contains the slider sections
        const bannerSection = button.closest('.streamit_api_slider_section');
        const sliderList = bannerSection.find('#streamit_api_slider_list');

        var lastSource = sliderList.find('.streamit_api_single_slider').last();
        var relValue = lastSource.attr('rel');


        if (relValue === undefined) {
            relValue = 0;
        } else {
            relValue = parseInt(relValue, 10) + 1;
        }
        var data = {
            i: relValue,
            type: 'tvshow'
        };
        this.addslidertab(data, button, sliderList);
    }

    addslidertab(data, button, sliderList) {

        get('streamit_api_add_slider_tab', {
            data: data,
            _ajax_nonce: window.streamitAjax
        })
            .then(response => {
                if (sliderList.length) {
                    // Append the new content to the specific slider list
                    sliderList.append(response);

                    this.sliderOptionHandler();
                    if (jQuery('.streamit_api_select2').length > 0) {
                        jQuery('.streamit_api_select2').each(function () {
                            jQuery(this).select2({
                                width: '100%'
                            });
                        });
                    }
                    button.attr('disabled', false);
                }
            })
            .catch(error => {
                console.error("Error while adding slider tab:", error);
                button.attr('disabled', false);
            });
    }


    homeSubmit(event) {
        event.preventDefault();
        let submitButton = jQuery('#st-admin-option-dashboard button[type="submit"]');
        submitButton.attr('type', 'button');

        // Validate slider titles and banner images
        if (!this.validateSliderTitlesAndBanners('st-admin-option-dashboard')) {
            submitButton.attr('type', 'submit');
            return;
        }

        const bannersData = this.getBannersData('st-admin-option-dashboard');
        const sliderData = this.getSliderData('st-admin-option-dashboard');

        if (bannersData.length === 0 && sliderData.length === 0) {
            notificationToast.error('Please add content for a better app experience.', 'Error');
            submitButton.attr('type', 'submit');
            return;
        }

        const formData = {
            banners: bannersData,
            sliders: sliderData
        }

        var data = {
            option: 'streamit_api_home',
            formData: formData
        };

        this.SubmitData(data, submitButton);
    }

    movieSubmit(event) {
        event.preventDefault();

        let submitButton = jQuery('#st-admin-option-dashboard-movie button[type="submit"]');
        submitButton.attr('type', 'button');

        // Validate slider titles and banner images
        if (!this.validateSliderTitlesAndBanners('st-admin-option-dashboard-movie')) {
            submitButton.attr('type', 'submit');
            return;
        }

        const bannersData = this.getBannersData('st-admin-option-dashboard-movie');
        const sliderData = this.getSliderData('st-admin-option-dashboard-movie');

        if (bannersData.length === 0 && sliderData.length === 0) {
            notificationToast.error('Please add content for a better app experience.', 'Error');
            submitButton.attr('type', 'submit');
            return;
        }

        const formData = {
            banners: bannersData,
            sliders: sliderData
        }

        var data = {
            option: 'streamit_api_movie',
            formData: formData
        };
        this.SubmitData(data, submitButton);
    }

    videoSubmit(event) {
        event.preventDefault();

        let submitButton = jQuery('#st-admin-option-dashboard-video button[type="submit"]');
        submitButton.attr('type', 'button');

        // Validate slider titles and banner images
        if (!this.validateSliderTitlesAndBanners('st-admin-option-dashboard-video')) {
            submitButton.attr('type', 'submit');
            return;
        }

        const bannersData = this.getBannersData('st-admin-option-dashboard-video');
        const sliderData = this.getSliderData('st-admin-option-dashboard-video');

        if (bannersData.length === 0 && sliderData.length === 0) {
            notificationToast.error('Please add content for a better app experience.', 'Error');
            submitButton.attr('type', 'submit');
            return;
        }

        const formData = {
            banners: bannersData,
            sliders: sliderData
        }

        var data = {
            option: 'streamit_api_video',
            formData: formData
        };
        this.SubmitData(data, submitButton);
    }

    tvshowSubmit(event) {
        event.preventDefault();

        let submitButton = jQuery('#st-admin-option-dashboard-tvshow button[type="submit"]');
        submitButton.attr('type', 'button');

        // Validate slider titles and banner images
        if (!this.validateSliderTitlesAndBanners('st-admin-option-dashboard-tvshow')) {
            submitButton.attr('type', 'submit');
            return;
        }

        const bannersData = this.getBannersData('st-admin-option-dashboard-tvshow');
        const sliderData = this.getSliderData('st-admin-option-dashboard-tvshow');

        if (bannersData.length === 0 && sliderData.length === 0) {
            notificationToast.error('Please add content for a better app experience.', 'Error');
            submitButton.attr('type', 'submit');
            return;
        }

        const formData = {
            banners: bannersData,
            sliders: sliderData
        }

        var data = {
            option: 'streamit_api_tv_show',
            formData: formData
        };
        this.SubmitData(data, submitButton);
    }

    validateSliderTitlesAndBanners(formId) {
        let isValid = true;

        // Validate slider titles
        jQuery(`#${formId} .streamit_api_single_slider`).each(function() {
            const titleInput = jQuery(this).find('[id="slider_title"]');
            const titleError = jQuery(this).find('.title_error');
            
            if (!titleInput.val().trim()) {
                titleError.show();
                isValid = false;
            } else {
                titleError.hide();
            }
        });

        // Validate banner images
        jQuery(`#${formId} .banner_display_list`).each(function() {
            const bannerImage = jQuery(this).find('.upload_video_id').val();
            const bannerError = jQuery(this).find('.banner-error-msg');
            
            if (!bannerImage) {
                bannerError.show();
                isValid = false;
            } else {
                bannerError.hide();
            }
        });

        if (!isValid) {
            notificationToast.error('Please fill in all required fields.', 'Error');
        }

        return isValid;
    }

    SubmitData(data, submitButton) {
        post('streamit_api_submit_dashbord_Data', data).then(response => {
            console.log(response);
            notificationToast.success(response.data.message, 'Success');
            submitButton.attr('type', 'submit');
        }).catch(error => {
            console.log(error);
        })
    }
    getBannersData(formId) {
        // Initialize bannersData as an array
        const bannersData = [];

        // Loop through each banner display list within the specified form
        jQuery(`#${formId} .banner_display_list`).each(function () {
            const bannerType = jQuery(this).find('#banner_type').val();
            const bannerSelectItem = jQuery(this).find('select[name^="banner_select_item"]').val();
            const bannerImage = jQuery(this).find('.upload_video_id').val();

            // Add the collected data to the bannersData array
            bannersData.push({
                type: bannerType,
                selectItem: bannerSelectItem,
                image: bannerImage
            });
        });

        return bannersData; // Return the collected banner data
    }


    getSliderData(formId) {
        const sliderData = [];

        // Loop through each slider item within the specified form
        jQuery(`#${formId} .streamit_api_single_slider`).each(function () {
            const rel_value = jQuery(this).attr('rel');  // Correct way to get 'rel' value
            const post_type_element = jQuery(this).find('#type').val();

            // General fields (common for all content types)
            const title = jQuery(this).find('[id="slider_title"]').val() || '';  // Get title by ID pattern
            const view_all = jQuery(this).find('[id="view_all"]').is(':checked') ? 'true' : 'false';  // Get view_all by ID pattern
            const select_type_id = '#slider_post_type_' + post_type_element + '_' + rel_value;
            const select_type = jQuery(this).find(select_type_id).val() || '';  // Get post type based on ID pattern

            // Filter By (movie, video, tv show filter)
            const moviefilterBy = jQuery(this).find(`[id="movie_select_fillters_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const videofilterBy = jQuery(this).find(`[id="video_select_fillters_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const tvshowfilterBy = jQuery(this).find(`[id="tvshow_select_fillters_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            // Collect genres, tags, and other common fields for all content types
            const movieGenres = jQuery(this).find(`[id="movie_slider_genre_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const movieTags = jQuery(this).find(`[id="movie_slider_tag_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const movieSelect = jQuery(this).find(`[id="movie_slider_selected_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const videoGenres = jQuery(this).find(`[id="video_slider_genre_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const videoTags = jQuery(this).find(`[id="video_slider_tag_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const videoSelect = jQuery(this).find(`[id="video_slider_selected_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const tvshowGenres = jQuery(this).find(`[id="tvshow_slider_genre_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const tvshowTags = jQuery(this).find(`[id="tvshow_slider_tag_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            const tvshowSelect = jQuery(this).find(`[id="tvshow_slider_selected_${post_type_element}_${rel_value}"] option:selected`).map(function () {
                return jQuery(this).val();
            }).get() || [];

            // Add the collected data to the sliderData array
            sliderData.push({
                rel_value: rel_value,
                title: title,
                view_all: view_all,
                post_type: select_type,
                moviefilter: moviefilterBy,
                movieGenres: movieGenres,
                movieTags: movieTags,
                movieSelect: movieSelect,
                videofilter: videofilterBy,
                videoGenres: videoGenres,
                videoTags: videoTags,
                videoSelect: videoSelect,
                tvshowfilter: tvshowfilterBy,
                tvshowGenres: tvshowGenres,
                tvshowTags: tvshowTags,
                tvshowSelect: tvshowSelect
            });
        });
        console.log(sliderData);

        return sliderData;
    }


    SliderSelectionHandler() {
        // Bind event listener for the change event on radio buttons
        jQuery(document.body).on('change', 'input[name^="select_type_"]', (e) => {
            const selectedRadio = jQuery(e.currentTarget); // Get the current target
            const selectedValue = selectedRadio.val(); // Get the selected value
            const parentSlider = selectedRadio.closest('.streamit_api_single_slider');

            // Call showslideroption with the parent slider and selected value
            this.showslideroption(parentSlider, selectedValue);
        });
    }

    checkDefaultValues() {
        // Check for all instances of the slider on page load
        const sliders = jQuery('.streamit_api_single_slider'); // Store all slider instances
        sliders.each((index, slider) => { // Use arrow function to maintain 'this' context
            const currentSlider = jQuery(slider); // Create a jQuery object for the current slider

            // Find the checked radio button in this slider
            const checkedRadio = currentSlider.find('input[name^="select_type_"]:checked');
            if (checkedRadio.length) {
                const defaultValue = checkedRadio.val(); // Get the default value
                // Call showslideroption with the current slider and default value
                this.showslideroption(currentSlider, defaultValue);
            }
        });
    }

    showslideroption(parentSlider, selectedValue) {
        // Hide all conditional fields first
        parentSlider.find('#conditional-fields > div').hide();
        // Show the relevant field based on the selected value
        switch (selectedValue) {
            case 'genre':
                parentSlider.find('select[name="slider_genres"]').closest('div').show();
                break;
            case 'tag':
                parentSlider.find('select[name="slider_tags"]').closest('div').show();
                break;
            case 'filter':
                parentSlider.find('select[name="slider_filter_by"]').closest('div').show();
                break;
            case 'selected':
                parentSlider.find('select[name="slider_datas"]').closest('div').show();
                break;
            default:
                break;
        }
    }

    LimitLogin(event) {
        // Get the checkbox element
        var isChecked = jQuery(event.target).is(":checked");

        var displayElement = jQuery("#display_limit_login_options");

        if (isChecked) {
            displayElement.css("display", "block");
        } else {
            displayElement.css("display", "none");
        }
    }

    // Call this function during the MembershipOptions setup
    MemberShipOptions() {
        const paymentTypeRadios = document.querySelectorAll('input[name="payment_type"]');
        if (!paymentTypeRadios.length) {
            console.warn('No payment type radio buttons found.');
            return;
        }
        // Attach event listeners to radio buttons
        paymentTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.ManageMembershipContainer(radio.value);
            });
        });

        const selectedValue = document.querySelector('input[name="payment_type"]:checked').value;
        this.ManageMembershipContainer(selectedValue);
    }


    ManageMembershipContainer(value) {
        const inAppPaymentSection = document.querySelector('.streamit-api-inapp-payment');
        const defaultPaymentSection = document.querySelector('.streamit-api-default-payment');
        // Manage visibility based on the passed value
        switch (value) {
            case '0':
                // Hide both sections
                inAppPaymentSection.style.display = 'none';
                defaultPaymentSection.style.display = 'none';
                break;
            case '1':
                // Show Default Payment section and hide In-App Payment section
                defaultPaymentSection.style.display = 'block';
                inAppPaymentSection.style.display = 'none';
                break;
            case '2':
                // Show In-App Payment section and hide Default Payment section
                inAppPaymentSection.style.display = 'block';
                defaultPaymentSection.style.display = 'none';
                break;
            default:
                // Default case to hide both
                inAppPaymentSection.style.display = 'none';
                defaultPaymentSection.style.display = 'none';
                break;
        }
    }

    GeneralSettings(event) {
        event.preventDefault();

        const data = {};

        data.show_titles = jQuery('#show_titles').is(':checked') ? '1' : '0';
        data.client_email = jQuery('#client_email').val();
        data.private_key = jQuery('#private_key').val().replace(/\\n/g, "\n");;
        data.project_id = jQuery('#project_id').val();
        data.app_name = jQuery('#app_name').val().toLowerCase().replace(/\s+/g, '_');
        data.default_image = jQuery('#default_image').val();
        data.app_logo = jQuery('#app_logo').val();
        const settings = {
            setting_key: 'st_firebase',
            datavalue: data
        }

        this.SubmitSettings(settings);
    }

    CommentSettings(event) {
        event.preventDefault();

        const data = {};
        data.setting_key = 'st_comment';
        data.movie_comments = jQuery('#movie_comments').is(':checked') ? '1' : '0';
        data.tvshow_comments = jQuery('#tvshow_comments').is(':checked') ? '1' : '0';
        data.episode_comments = jQuery('#episode_comments').is(':checked') ? '1' : '0';
        data.video_comments = jQuery('#video_comments').is(':checked') ? '1' : '0';

        const settings = {
            setting_key: 'st_comment',
            datavalue: data
        }

        this.SubmitSettings(settings);

    }

    LimitLoginSettings(event) {
        event.preventDefault();

        const data = {};

        data.limitlogin_is_enable = jQuery('#limitlogin_is_enable').is(':checked') ? '1' : '0';
        jQuery('#st-admin-settings-limitlogin .limit-login-input').each(function () {
            const key = jQuery(this).attr('id');
            const value = jQuery(this).val();
            if (key) {
                data[key] = value;
            }
        });

        const settings = {
            setting_key: 'st_device_lmit',
            datavalue: data
        }

        this.SubmitSettings(settings);
    }

    MembershipSettings(event) {
        event.preventDefault();


        // Get the selected radio button value
        const selectedPaymentType = jQuery('input[name="payment_type"]:checked').val();

        // Gather values from other input fields
        const entitlementId = jQuery('#entitlement_id').val();
        const googleApiKey = jQuery('#google_api_key').val();
        const appleApiKey = jQuery('#apple_api_key').val();

        // Create a data object with the gathered values
        const data = {
            payment_type: selectedPaymentType,
            entitlement_id: entitlementId,
            google_api_key: googleApiKey,
            apple_api_key: appleApiKey
        };

        const settings = {
            setting_key: 'st_pmp_options',
            datavalue: data
        }

        this.SubmitSettings(settings);
    }

    SubmitSettings(data) {
        post('streamit_api_submit_settings', data).then(response => {
            if(response.success){
                notificationToast.success(response.message, 'Success'); 
            }else{
                notificationToast.error(response.message, 'Error');
            }
            console.log(response);
        }).catch(error => {
            console.log(error);
        })
    }

    removeSelectedBanner(event) {
        event.preventDefault();
        jQuery(event.target).closest('.banner_display_list').remove();
    }

    removeSelectedSlider(event) {
        event.preventDefault();
        jQuery(event.target).closest('.streamit_api_single_slider').remove();
    }

    inatllImportPlugin(event) {
        event.preventDefault();
        var data = {};
        post('streamit_api_install_import_plugin', data).then(response => {
            window.location.href = streamitapiAjax.st_migrationUrl;
        }).catch(error => {
            console.log(error);
        })
    }
}


new Streamit_Api_Admin();
