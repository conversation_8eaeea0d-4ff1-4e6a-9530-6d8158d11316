<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_pmp_Route_Controller
{
    public $module = 'membership';
    public $name_space;

    public function __construct()
    {
        $this->name_space = STREAMIT_API_NAMESPACE;

        // Register REST routes
        add_action('rest_api_init', [$this, 'register_playlist_routes']);
    }

    public function register_playlist_routes():void
    {
        $callbacks = new St_PMP_Route_Callback();
        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/levels',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'get_membership_levels'],
                'permission_callback' => '__return_true',
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/orders',
            array(
                array(
                    'methods'             => WP_REST_Server::READABLE,
                    'callback'            => [$callbacks, 'rest_order_list'],
                    'permission_callback' => '__return_true',
                ),
                array(
                    'methods'             => WP_REST_Server::CREATABLE,
                    'callback'            => [$callbacks, 'generate_order'],
                    'permission_callback' => '__return_true',
                ),
            )
        );

    }
}
new St_pmp_Route_Controller();