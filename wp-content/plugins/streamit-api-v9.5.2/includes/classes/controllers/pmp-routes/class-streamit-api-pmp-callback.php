<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}


class St_PMP_Route_Callback
{

    /**
     * Get Memebrship Levels.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_membership_levels(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        $user_id    = $data['user_id'];

        $levels = st_pmpro_getAllLevels($user_id);

        if (empty($levels))
            return st_comman_custom_response([
                "status"     => true,
                "messate"     => esc_html__('No membership plans found.', 'streamit-api'),
                "data"         => []
            ]);

        return st_comman_custom_response([
            "status"     => true,
            "messate"     => esc_html__('Membership plans.', 'streamit-api'),
            "data"         => array_values($levels)
        ]);
    }

    /**
     * Get Memebrship Order List.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function rest_order_list(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status']) {
            return st_comman_custom_response($data, 401);
        }

        $user_id = $data['user_id'];

        global $wpdb;

        $parameters = $request->get_params();

        $page         = $parameters["page"] ?? 1;
        $per_page     = $parameters["per_page"] ?? 10;


        $pgstrt = absint(($page - 1) * $per_page) . ', ';

        $limits = 'LIMIT ' . $pgstrt . $per_page;

        $order_result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $wpdb->pmpro_membership_orders WHERE `user_id` = %d ORDER BY id DESC $limits", $user_id));

        if (!$order_result)
            return st_comman_custom_response([
                "status"     => true,
                "messate"     =>  esc_html__('No data found.', 'streamit-api'),
                "data"         => []
            ]);

        $response = st_get_pmp_orders($order_result);

        return st_comman_custom_response([
            "status"     => true,
            "messate"     =>  esc_html__('User orders.', 'streamit-api'),
            "data"         => $response
        ]);
    }

    /**
     * Genrerate Membership Order.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function generate_order(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!isset($data['user_id']))
            return st_comman_custom_response($data, $data['status_code']);

        global $wpdb;

        $current_user_id = $data['user_id'];
        $user = get_userdata($current_user_id);

        $parameters = $request->get_params();
        $parameters = st_sanitize_recursive_text_fields($parameters);

        $leve_id = $parameters["level_id"];


        session_start();

        $pmpro_level = pmpro_getLevel($leve_id);
        $discount_code = isset($parameters["discount_code"]) ? $parameters["discount_code"] : "";

        $morder                   = new MemberOrder();
        $morder->user_id          = $current_user_id;
        $morder->membership_id    = $pmpro_level->id;
        $morder->membership_name  = $pmpro_level->name;
        $morder->discount_code    = $discount_code;
        $morder->InitialPayment   = pmpro_round_price($pmpro_level->initial_payment);
        $morder->PaymentAmount    = pmpro_round_price($parameters['billing_amount']);
        $morder->couponamount    = pmpro_round_price($parameters['coupon_amount']);
        $morder->subtotal        = pmpro_round_price($parameters['billing_amount']);
        $morder->total    = pmpro_round_price($parameters['coupon_amount']);
        $morder->ProfileStartDate = date_i18n("Y-m-d\TH:i:s", current_time("timestamp"));
        $morder->BillingPeriod    = $pmpro_level->cycle_period;
        $morder->BillingFrequency = $pmpro_level->cycle_number;
        if ($pmpro_level->billing_limit) {
            $morder->TotalBillingCycles = $pmpro_level->billing_limit;
        }
        if (pmpro_isLevelTrial($pmpro_level)) {
            $morder->TrialBillingPeriod    = $pmpro_level->cycle_period;
            $morder->TrialBillingFrequency = $pmpro_level->cycle_number;
            $morder->TrialBillingCycles    = $pmpro_level->trial_limit;
            $morder->TrialAmount           = pmpro_round_price($pmpro_level->trial_amount);
        }

        $is_card_payment = ($parameters["payment_by"] === "card");
        if ($is_card_payment) {
            // Credit card values.
            $morder->cardtype              = $parameters["card_details"]["card_type"];
            $morder->accountnumber         = "XXXX XXXX XXXX " . $parameters["card_details"]["card_number"];
            $morder->expirationmonth       = $parameters["card_details"]["exp_month"];
            $morder->expirationyear        = $parameters["card_details"]["exp_year"];
            $morder->ExpirationDate        = "";
            $morder->ExpirationDate_YdashM = "";
            $morder->CVV2                  = "";
        }

        // Not saving email in order table, but the sites need it.
        $morder->Email = $user->user_email;

        // Save the user ID if logged in.
        if ($current_user_id) {
            $morder->user_id = $current_user_id;
        }

        $billing_details = $parameters["billing_details"];
        // Sometimes we need these split up.
        $morder->FirstName = $billing_details["first_name"];
        $morder->LastName  = $billing_details["last_name"];
        $morder->Address1  = $billing_details["user_address"];
        $morder->Address2  = "";

        // Set other values.
        $morder->billing                  = new stdClass();
        $morder->billing->name            = $morder->FirstName . " " . $morder->LastName;
        $morder->billing->street          = trim($morder->Address1 . " " . $morder->Address2);
        $morder->billing->city            = $billing_details["user_city"];
        $morder->billing->state           = $billing_details["user_state"];
        $morder->billing->country         = $billing_details["user_country"];
        $morder->billing->zip             = $billing_details["user_postal_code"];
        $morder->billing->phone           = $billing_details["user_phone"];
        $morder->gateway                 = $parameters["gateway"];
        $morder->gateway_environment     = $parameters["gateway_mode"];
        $morder->setGateway();


        $morder->payment_transaction_id         = $parameters["transation_id"];
        $morder->subscription_transaction_id     = $parameters["transation_id"];

        // Set up level var.
        $morder->getMembershipLevelAtCheckout();

        // Set tax.
        $initial_tax = $morder->getTaxForPrice($morder->InitialPayment);
        $recurring_tax = $morder->getTaxForPrice($morder->PaymentAmount);

        // Set amounts.
        $morder->initial_amount = pmpro_round_price((float)$morder->InitialPayment + (float)$initial_tax);
        $morder->subscription_amount = pmpro_round_price((float)$morder->PaymentAmount + (float)$recurring_tax);

        // die;
        // Filter for order, since v1.8
        $morder = apply_filters('pmpro_checkout_order', $morder);

        $order_id = (int) $morder->saveOrder();

        do_action("pmpro_after_checkout", $morder->user_id, $morder);

        // Check if we should send emails.
        if (apply_filters('pmpro_send_checkout_emails', true, $morder)) {
            // Set up some values for the emails.
            $user                   = get_userdata($morder->user_id);
            $user->membership_level = $pmpro_level;        // Make sure that they have the right level info.

            // Send email to member.
            $pmproemail = new PMProEmail();
            $pmproemail->sendCheckoutEmail($user, $morder);

            // Send email to admin.
            $pmproemail = new PMProEmail();
            $pmproemail->sendCheckoutAdminEmail($user, $morder);
        }


        session_destroy();

        if (!$order_id)
            return st_comman_custom_response([
                "status" => false,
                "message" =>  esc_html__("Something Wrong ! Try Again.", "streamit-api")
            ]);

        // add in-app purchase identifer if payment type is in-app
        if (isset($parameters['in_app_purchase_identifier']))
            add_pmpro_membership_order_meta($order_id, 'st_in_app_purchase_identifier', trim($parameters['in_app_purchase_identifier']));

        if (!$is_card_payment) {
            add_pmpro_membership_order_meta($order_id, $parameters["payment_by"], $parameters["meta_value"]);
        }
        if (isset($parameters["discount_code"]))
            add_pmpro_membership_order_meta($order_id, "discount_code", $parameters["discount_code"]);

        st_change_membership_level($leve_id, $current_user_id, ["discount_code" => $discount_code]);

        $order_result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $wpdb->pmpro_membership_orders WHERE `id` = %d", $order_id));

        if (!$order_result) return st_comman_custom_response([
            "status" => true,
            "message" =>  esc_html__("Membership order not found", "streamit-api")
        ]);

        $response = st_get_pmp_orders($order_result);

        return st_comman_custom_response([
            "status"     => true,
            "message"     =>  esc_html__("Membership orders", "streamit-api"),
            "data"         => reset($response)
        ]);
    }
}
