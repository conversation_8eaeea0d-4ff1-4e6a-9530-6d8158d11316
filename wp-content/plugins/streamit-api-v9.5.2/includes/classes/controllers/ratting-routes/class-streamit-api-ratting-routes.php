<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}


class St_Ratting_Route_Controller
{
    public $module = 'rate';
    public $name_space;

    public function __construct()
    {
        $this->name_space = STREAMIT_API_NAMESPACE;

        // Register REST routes
        add_action('rest_api_init', [$this, 'register_ratting_routes']);
    }

    /**
     * Register REST routes for the user module.
     *
     * @return void
     */
    public function register_ratting_routes(): void
    {
        $callbacks = new St_Rate_Route_Callback();

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '(?P<post_type>movie|tv_show|video)/(?P<post_id>\d+)',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'get_ratings'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '(?P<post_type>movie|tv_show|video)',
            [
                array(
                    'methods'             => WP_REST_Server::EDITABLE,
                    'callback'            => [$callbacks, 'add_rating'],
                    'permission_callback' => '__return_true'
                ),
                array(
                    'methods'             => WP_REST_Server::DELETABLE,
                    'callback'            => [$callbacks, 'delete_rating'],
                    'permission_callback' => '__return_true'
                )
            ]
        );
    }
}

new St_Ratting_Route_Controller();
