<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Rate_Route_Callback
{
    /**
     * Get Post Rattings
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_ratings(WP_REST_Request $request)
    {
        $parameters = $request->get_params();
        $post_type = $parameters['post_type'];
        $post_id = $parameters['post_id'];

        $args = [
            'comment_post_ID'   => $post_id,
            'post_type'         => ($post_type === 'tv_show' ? 'tvshow' : $post_type)
        ];
        $rate_list = streamit_get_comments($args);
        if (is_wp_error($rate_list) || empty($rate_list))
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('No rate available.', 'streamit-api'),
                "data"       => []
            ]);

        $ratings_output = [];
        foreach ($rate_list->results as $rate):
            $comment_data = $rate->get_data();
            $user_id = isset($comment_data['user_id']) ? (int) $comment_data['user_id'] : 0;
            
            $ratings_output[] = array_merge(
                st_rate_formate($rate),
                [
                    'rate_id' => $rate->get_id(),
                    'user_id' => $user_id
                ]
            );
        endforeach;

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('Rating List.', 'streamit-api'),
            "data"       => $ratings_output
        ]);
    }

    /**
     * Add Post Rattings
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function add_rating(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters = $request->get_params();
        $user_id    = $data['user_id'];
        $post_id    = isset($parameters['post_id']) ? (int)$parameters['post_id'] : '';
        $post_type  = $request->get_param('post_type');
        $ratting_id = isset($parameters['rate_id']) ? (int)$parameters['rate_id'] : '';

        if (empty($ratting_id)) {
            $existing_comment = streamit_user_current_post_comment($post_id, $post_type, $user_id);
            if ($existing_comment) {
                return st_comman_custom_response([
                    "status"     => false,
                    "message"    => esc_html__('You have already added a review.', 'streamit-api')
                ]);
            }
        }

        $args = [
            'post_type'             => ($post_type === 'tv_show' ? 'tvshow' : $post_type),
            'comment_post_ID'       => $post_id,
            'comment_author'        => isset($parameters['user_name']) ? $parameters['user_name'] : '',
            'comment_author_email'  => isset($parameters['user_email']) ? $parameters['user_email'] : '',
            'rating'                => isset($parameters['rating']) ? (int)$parameters['rating'] : '',
            'comment_content'       => isset($parameters['cm_details']) ? $parameters['cm_details'] : '',
            'user_id'               => $user_id,
        ];

        if (!empty($ratting_id)) {
            $existing_comment = streamit_get_comment($ratting_id);
            if (!$existing_comment) {
                return st_comman_custom_response([
                    "status"     => false,
                    "message"    => esc_html__('Invalid rate ID provided.', 'streamit-api')
                ]);
            }

            $result = streamit_update_comment($ratting_id, $args);
            $status = !is_wp_error($result);
            $message = $status ? esc_html__('Review Updated successfully.', 'streamit-api') : esc_html__('Failed to update the review.', 'streamit-api');
        } else {
            $result = streamit_add_comment($args);
            $status = !is_wp_error($result);
            $message = $status ? esc_html__('Review Added successfully.', 'streamit-api') : esc_html__('Failed to add the review.', 'streamit-api');
        }

        return st_comman_custom_response([
            "status"     => $status,
            "message"    => $message
        ]);
    }

    /**
     * Delete Post Rattings
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function delete_rating(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters = $request->get_params();
        $user_id    = $data['user_id'];
        $ratting_id = isset($parameters['rate_id']) ? $parameters['rate_id'] : '';
        $post_id = isset($parameters['post_id']) ? $parameters['post_id'] : '';
        // $post_type = isset($parameters['post_type']) ? $parameters['post_type'] : '';
        $post_type  = $request->get_param('post_type');

        if (empty($ratting_id) || empty($post_id) || empty($post_type))
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('Something went wrong.', 'streamit-api')
            ]);
        $delete_post_type = ($post_type === 'tv_show' ? 'tvshow' : $post_type);
        $result = streamit_delete_comment($ratting_id, $post_id, $delete_post_type);
        if ($result)
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('Review deleted successfully.', 'streamit-api')
            ]);

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('Something went wrong.', 'streamit-api')
        ]);
    }
}
