<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Streamit_Route_Callback
{
    /**
     * Create a new user.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function search_list(WP_REST_Request $request)
    {
        $parameters = $request->get_params();
        $user_id    = isset($parameters['user_id']) ? $parameters['user_id'] : 0;
        $paged      = isset($parameters['paged']) ? $parameters['paged'] : 1;
        $per_page   = isset($parameters['posts_per_page']) ? $parameters['posts_per_page'] : 10;
        $search     = isset($parameters['search']) ? $parameters['search'] : '';

        $post_type     = ['movie', 'tvshow', 'episode', 'video'];
        $args = array(
            'post_type' => $post_type,
            'paged'     =>  $paged,
            'per_page'  => $per_page,
            's'         => $search
        );
        $result = function_exists('streamit_get_data') ? streamit_get_data($args) : '';

        if (is_wp_error($result) || empty($result))
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('No data found.', 'streamit-api'),
                'data'      => []
            ]);

        $data = array();
        foreach ($result as $post) {
            $format_function = 'st_' . $post->get_post_type() . '_content_format';
            $data[] = $format_function($post);
        }

        return st_comman_custom_response([
            'status'    => true,
            'message'   => __('Search Reasult.', 'streamit-api'),
            'data'      => $data
        ]);
    }

    /**
     * Handles dashboard data retrieval for the given type.
     *
     * Retrieves banners, sliders, and continue watch data based on the dashboard configuration
     * and user-defined filters.
     *
     * @param WP_REST_Request $request The REST request.
     *
     * @return WP_REST_Response A REST response with dashboard data or an error message.
     */
    public function dashboard(WP_REST_Request $request): WP_REST_Response
    {
        $data = st_token_validation($request);
        $user_id = $data['user_id'] ?? null;

        $parameters = $request->get_params();
        $type = $parameters['type'] ?? '';

        if (!in_array($type, ['home', 'movie', 'tv_show', 'video'], true)) {
            return st_comman_custom_response([
                'status'  => true,
                'message' => esc_html__('No data found.', 'streamit-api'),
                'data'    => [],
            ]);
        }

        $finalResult = [
            'banner'         => [],
            'sliders'        => [],
            'continue_watch' => [],
        ];

        $continue_watch_post_type = in_array($type, ['movie', 'tv_show', 'video'], true)
            ? ($type === 'tv_show' ? 'tvshow' : $type)
            : '';

        $continue_watch = streamit_get_continue_watching($user_id, $continue_watch_post_type);

        if (! empty($continue_watch) && is_array($continue_watch)) {

            $post_types = ['movie', 'video', 'episode'];

            foreach ($post_types as $post_type) {

                $posts = $continue_watch[$post_type] ?? [];

                if (! is_array($posts) || empty($posts)) {
                    continue;
                }

                foreach ($posts as $post_id => $data) {

                    $post_id       = (int) $post_id;
                    $getter_func   = 'streamit_get_' . $post_type;
                    $format_func   = 'st_' . $post_type . '_content_format';

                    if (! function_exists($getter_func)) {
                        continue;
                    }

                    $post_data = call_user_func($getter_func, $post_id);

                    if (is_wp_error($post_data) || empty($post_data)) {
                        continue;
                    }

                    $formatted_data = function_exists($format_func) ? call_user_func($format_func, $post_data) : [];

                    if (empty($formatted_data)) {
                        continue;
                    }

                    $formatted_data['watched_duration'] = [
                        'watched_time'            => (int) ($data['watched_time'] ?? 0),
                        'watched_total_time'      => (int) ($data['watched_total_time'] ?? 0),
                        'watched_time_percentage' => (float) ($data['watched_time_percentage'] ?? 0),
                    ];

                    $finalResult['continue_watch'][] = $formatted_data;
                }
            }
        }


        $panelOption = get_option('streamit_api_' . $type);
        if (!empty($panelOption)) {
            $st_bn = !empty($panelOption['banners']) ? $panelOption['banners'] : [];
            $bannerData = $this->processBanners($st_bn);
            $finalResult['banner'] = $bannerData;

            $st_sl = !empty($panelOption['sliders']) ? $panelOption['sliders'] : [];
            $sliderData = $this->processSliders($st_sl);
            $finalResult['sliders'] = $sliderData;
        }

        if (empty($finalResult)) {
            return st_comman_custom_response([
                'status'  => true,
                'message' => esc_html__('No data found.', 'streamit-api'),
                'data'    => [],
            ]);
        }

        return st_comman_custom_response([
            'status'  => true,
            'message' => esc_html__('Dashboard result.', 'streamit-api'),
            'data'    => $finalResult,
        ]);
    }

    /**
     * Process banner data.
     *
     * @param array $banners Array of banners from the panel options.
     *
     * @return array Processed banner data.
     */
    private function processBanners(array $banners): array
    {
        $bannerData = [];

        foreach ($banners as $banner) {
            $functionName = 'streamit_get_' . ($banner['type'] ?? '');
            if (function_exists($functionName)) {
                $data = $functionName((int)($banner['selectItem'] ?? 0));
                if (!empty($data)) :
                    $formatFunction = 'st_' . ($banner['type'] ?? '') . '_content_format';
                    $bannerData[] = function_exists($formatFunction) ? $formatFunction($data) : [];
                endif;
            }
        }
        return $bannerData;
    }

    /**
     * Process slider data.
     *
     * @param array $sliders Array of sliders from the panel options.
     *
     * @return array Processed slider data.
     */
    private function processSliders(array $sliders): array
    {
        $sliderFinal = [];

        foreach ($sliders as $slider) {
            $out = $data = [];
            $postType = $slider['post_type'] ?? '';
            $filterBy = $slider[$postType . 'filter'][0] ?? 'latest';
            $functionName = 'streamit_get_' . $postType . 's';
            if (function_exists($functionName)) {
                $out = $this->getSliderArgs($filterBy, $slider, $postType);
                $result = $functionName($out['args'])->results;
                foreach ($result as $post) {
                    $format_function = 'st_' . $postType . '_content_format';
                    $data[] = $format_function($post);
                }
                $sliderFinal[] = [
                    'title'    => $slider['title'] ?? '',
                    'view_all' => !empty($slider['view_all']) && ($slider['view_all'] == 'true') ? true : false,
                    'data'     => $data,
                    'ids'      => $out['ids'],
                    'type'       => $filterBy
                ];
            }
        }

        return $sliderFinal;
    }

    /**
     * Get query arguments for slider data.
     *
     * @param string $filterBy  Filter type (e.g., selected, upcoming, latest).
     * @param array  $slider    Slider configuration.
     * @param string $postType  Post type (e.g., movie, tv_show).
     *
     * @return array Query arguments for fetching slider data.
     */
    private function getSliderArgs(string $filterBy, array $slider, string $postType): array
    {
        $args = $ids = [];

        switch ($filterBy) {
            case 'selected':
                $args['include'] = $slider[$postType . 'Select'] ?? [];
                $args['per_page'] = -1;
                $ids = $slider[$postType . 'Select'];
                break;

            case 'upcoming':
                $args['meta_query'] = [
                    [
                        'key'     => 'name_upcoming',
                        'value'   => '1',
                        'compare' => '=',
                    ],
                ];
                break;

            case 'latest':
                $args['orderby'] = 'post_date';
                $args['order'] = 'DESC';
                break;

            case 'most_like':
                $args['meta_query'] = [
                    [
                        'relation' => 'OR',
                        'key'      => 'streamit_post_like_count',
                        'value'    => '',
                        'compare'  => '>=',
                    ],
                ];
                break;

            case 'genre':
                $args['tax_query'] = $this->buildTaxQuery($slider[$postType . 'Genres'] ?? []);
                $ids = $slider[$postType . 'Genres'];
                break;

            case 'tag':
                $args['tax_query'] = $this->buildTaxQuery($slider[$postType . 'Tags'] ?? []);
                $ids = $slider[$postType . 'Tags'];
                break;

            default:
                $args = [
                    'paged'     => 1,
                    'per_page'  => 10,
                    'orderby'   => 'meta_value_num',
                    'order'     => 'DESC',
                    'meta_key'  => 'post_views_count',
                    'meta_query' => [
                        [
                            'key'     => 'name_upcoming',
                            'value'   => '0',
                            'compare' => '=',
                        ],
                    ],
                ];
        }

        return ['args' => $args, 'ids' => $ids];
    }

    /**
     * Build tax query for genres or tags.
     *
     * @param array $termIds Term IDs for taxonomy query.
     *
     * @return array Taxonomy query.
     */
    private function buildTaxQuery(array $termIds): array
    {
        $taxQuery = ['relation' => 'OR'];

        foreach ($termIds as $termId) {
            $taxQuery[] = [
                'field'    => 'term_id',
                'terms'    => $termId,
                'operator' => '=',
            ];
        }

        return $taxQuery;
    }

    /**
     * Get Genre / category List.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function genre_list(WP_REST_Request $request)
    {

        $parameters = $request->get_params();

        $paged          = $parameters['paged'] ?? 1;
        $posts_per_page = $parameters['posts_per_page'] ?? 10;
        $type      = $parameters['type'] ?? '';
        $type = in_array($type, ['movie', 'tv_show', 'video'], true)
        ? ($type === 'tv_show' ? 'tvshow' : $type)
        : '';
        $taxonomy       = in_array($type , ['video']) ? $type  . '_category' :  $type  . '_genre';
        if ($paged < 1) $paged = 1;

        $args = array(
            'taxonomy'   => [$taxonomy],
            'paged'      => $paged,
            'per_page'   => $posts_per_page,
            'order'      => 'ASC',
        );
        $term_list = function_exists('streamit_get_terms') ? streamit_get_terms($args) : '';
        if (empty($term_list->results)) {
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('No genres found.', 'streamit-api'),
                "data"       => []
            ]);
        }

        $response = array();
        foreach ($term_list->results as $term) {
            $response[] = [
                'id'             => $term->get_term_id(),
                'name'           => $term->get_term_name(),
                'slug'           => $term->get_term_slug(),
                'genre_image'    => !empty($term->get_thumbnail()) ?  wp_get_attachment_thumb_url($term->get_thumbnail()) : ""
            ];
        }

        return st_comman_custom_response([
            'status'     => true,
            'message'    => esc_html__('Genre result.', 'streamit-api'),
            'data'       => $response
        ]);
    }

    /**
     * Get Data List based on genre name.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function data_by_genre(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        $user_id = $data['user_id'] ?? null;

        $parameters = $request->get_params();
        $post_type = $parameters['type'] === 'tv_show' ? 'tvshow' : ($parameters['type'] ?? '');
        $genre_slug = $parameters['genre'] ?? '';

        if (empty($post_type) || empty($genre_slug)) {
            return st_comman_custom_response([
                "status" => false,
                "message" => esc_html__('Missing required parameters.', 'streamit-api'),
                "data" => []
            ]);
        }

        $taxonomy = $post_type === 'video' ? 'video_category' : "{$post_type}_genre";

        if (function_exists('streamit_get_term')) {
            $term = streamit_get_term($genre_slug, $taxonomy);
            if (!empty($term) && !is_wp_error($term)) {
                $args = [
                    'per_page' => -1,
                    'tax_query' => [
                        [
                            'field' => 'term_id',
                            'terms' => $term->get_term_id(),
                            'operator' => '='
                        ]
                    ]
                ];
                $function_name = "streamit_get_{$post_type}s";
                if (function_exists($function_name)) {
                    $posts = $function_name($args)->results;
                    if (!empty($posts)) {
                        $display_data = array_map(function ($post) use ($post_type) {
                            $format_function = "st_{$post_type}_content_format";
                            return function_exists($format_function) ? $format_function($post) : $post;
                        }, $posts);

                        return st_comman_custom_response([
                            "status" => true,
                            "message" => esc_html__('Result.', 'streamit-api'),
                            "data" => $display_data
                        ]);
                    }
                }
            }
        }

        return st_comman_custom_response([
            "status" => true,
            "message" => esc_html__('No data found.', 'streamit-api'),
            "data" => []
        ]);
    }

    private function generate_woo_keys($app_name, $app_user_id, $scope = 'read_write') 
    {
        if (!class_exists('WooCommerce')) {
            return [
                'consumer_key'    => null,
                'consumer_secret' => null,
            ];
        }
    
        global $wpdb;
        $wpdb->delete($wpdb->prefix . 'woocommerce_api_keys', ['user_id' => $app_user_id]);
    
        $description = sprintf(
            '%s - API (%s)',
            wc_trim_string(wc_clean($app_name), 170),
            gmdate('Y-m-d H:i:s')
        );
    
        $permissions = in_array($scope, ['read', 'write', 'read_write'], true) ? sanitize_text_field($scope) : 'read';
    
        $consumer_key = 'ck_' . wc_rand_hash();
        $consumer_secret = 'cs_' . wc_rand_hash();
    
        $wpdb->insert(
            $wpdb->prefix . 'woocommerce_api_keys',
            [
                'user_id'         => $app_user_id,
                'description'     => $description,
                'permissions'     => $permissions,
                'consumer_key'    => wc_api_hash($consumer_key),
                'consumer_secret' => $consumer_secret,
                'truncated_key'   => substr($consumer_key, -7),
            ],
            ['%d', '%s', '%s', '%s', '%s', '%s']
        );
    
        return [
            'consumer_key'    => $consumer_key,
            'consumer_secret' => $consumer_secret,
        ];
    }

    /**
     * Get Data List based on genre name.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function settings(WP_REST_Request $request)
    {
        global $pmpro_currency, $st_app_settings;

        $data = st_token_validation($request);
        $user_id = isset($data['user_id']) && !empty($data['user_id']) ? $data['user_id'] : 0;


        $show_titles = $st_app_settings["st_firebase"]["show_titles"] ?? false;
        $pmpro_payment_type = $st_app_settings["st_pmp_options"]["payment_type"] ?? false;
        $woo_keys = $this->generate_woo_keys('StreamitApp', $user_id, 'read_write');

        $app_logo_id = $st_app_settings["st_firebase"]["app_logo"] ?? '';
        if ($app_logo_id && is_numeric($app_logo_id)) {
            $app_logo_url = wp_get_attachment_url($app_logo_id);
        }

        $is_membership_enable = is_plugin_active('paid-memberships-pro/paid-memberships-pro.php') ? 1 : 0;

        $settings = [
            "show_titles"          => ($show_titles) ? 1 : 0,
            "comment"              => st_get_comment_settings(),
            "pmpro_currency"       => "",
            "currency_symbol"      => "",
            "pmpro_payments"       => [],
            'wc_consumer_key'      => $woo_keys['consumer_key'],
            'wc_consumer_secret'   => $woo_keys['consumer_secret'],
            "is_membership_enable" => $is_membership_enable,
            "app_logo_url"         => $app_logo_url
        ];

        if (function_exists("pmpro_get_currency")) {
            $settings["pmpro_currency"] = pmpro_get_currency()["symbol"] ?? "";
            $settings["currency_symbol"] = $pmpro_currency;
        }

        if ($pmpro_payment_type == 2) {
            $user_id = get_current_user_id();

            $settings["pmpro_payments"] = [
                "type"               => "in-app",
                "entitlement_id"     => $st_app_settings["st_pmp_options"]["entitlement_id"] ?? "",
                'google_api_key'     => $st_app_settings["st_pmp_options"]["google_api_key"] ?? "",
                'apple_api_key'      => $st_app_settings["st_pmp_options"]["apple_api_key"] ?? "",
            ];
        }

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('App comman settings.', 'streamit-api'),
            "data"       => $settings
        ]);
    }

    /**
     * Get view all data as per fillters
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function view_all(WP_REST_Request $request)
    {
        $parameters = $request->get_params();

        $post_type  = $parameters['post_type'];
        $filter     = $parameters['filter'];
        $per_page   = isset($parameters['per_page']) ? intval($parameters['per_page']) : 10;
        $paged      = isset($parameters['paged']) ? intval($parameters['paged']) : 1;
        $post_type = $parameters['post_type'] === 'tv_show' ? 'tvshow' : ($parameters['post_type'] ?? '');
        $get_function = 'streamit_get_' . $post_type . 's';

        $args = [
            'paged' => $paged,
            'per_page' => $per_page
        ];

        switch ($filter) {
            case 'selected':
                if (!empty($parameters['selected'])) {
                    $selected_items = explode(',', $parameters['selected']); // Convert string to array
                    $args['include'] = array_map('intval', $selected_items); // Convert values to integers
                }
                break;
            case 'top_ten':
                if ($paged > 1) {
                    return st_comman_custom_response([
                        "status"     => true,
                        "data"       => []
                    ]);
                }
                $args = [
                    'paged'          => 1,
                    'posts_per_page' => 10,
                    'orderby'        => 'meta_value_num',
                    'order'          => 'DESC',
                    'meta_key'       => 'post_views_count',
                    'meta_query'     => [
                        [
                            'key'     => 'name_upcoming',
                            'value'   => '0',
                            'compare' => '='
                        ]
                    ]
                ];
                break;
            case 'view':
                $args = [
                    'paged'     => $paged,
                    'per_page'  => $per_page,
                    'orderby'   => 'meta_value_num',
                    'order'     => 'DESC',
                    'meta_key'  => 'post_views_count',
                    'meta_query' => [
                        [
                            'key'     => 'name_upcoming',
                            'value'   => '0',
                            'compare' => '=',
                        ],
                    ],
                ];
                break;
            case 'upcoming':
                $args['meta_query'] = [['key' => 'name_upcoming', 'value' => '1', 'compare' => '=']];
                break;

            case 'latest':
                $args['orderby'] = 'post_date';
                $args['order'] = 'DESC';
                break;

            case 'most_like':
                $args['meta_query'] = [[
                    'relation' => 'OR',
                    'key'      => 'streamit_post_like_count',
                    'value'    => '',
                    'compare'  => '>='
                ]];
                break;

            default:
                $selected_terms = ($filter === 'tag') ? array_map('intval', (explode(',', $parameters['tag']))) : array_map('intval', (explode(',', $parameters['genre'])));
                if (!empty($selected_terms)) {
                    $tax_query = array('relation' => 'OR');
                    foreach ($selected_terms as $term_id) {
                        $tax_query[] = array(
                            'field'    => 'term_id',
                            'terms'    => $term_id,
                            'operator' => '=',
                        );
                    }
                    $args['tax_query'] = $tax_query;
                }
                break;
        }

        $post_data = call_user_func($get_function, $args)->results;

        if (empty($post_data))
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('Content Not available.', 'streamit-api'),
                "data"       => []
            ]);

        $result = [];
        foreach ($post_data as $post) :
            $format_function = 'st_' . $post_type . '_content_format';
            $result[] = $format_function($post);
        endforeach;

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('View all results.', 'streamit-api'),
            "data"       => $result
        ]);
    }

    /**
     * Get the all recent searches.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function recent_search_list(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id = $data['user_id'] ?? null;

        $recent_searches = get_user_meta($user_id, 'st_recent_search', true);

        if (!is_array($recent_searches) || empty($recent_searches)) {
            return st_comman_custom_response([
                'status'    => true,
                'message'   => __('No recent searches found.', 'streamit-api'),
                'data'      => []
            ]);
        }

        return st_comman_custom_response([
            'status'    => true,
            'message'   => __('Searches.', 'streamit-api'),
            'data'   => $recent_searches
        ]);
    }

    /**
     * Add a recent searched word.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function recent_search(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id = $data['user_id'] ?? null;

        $parameters = $request->get_query_params();
        $search     = isset($parameters['search']) ? trim($parameters['search']) : '';


        if (!empty($search)) {

            $existing_searches = get_user_meta($user_id, 'st_recent_search', true);

            if (!is_array($existing_searches)) {
                $existing_searches = [];
            }

            $new_search = [
                'id'        => 0,
                'term'      => $search,
                'timestamp' => current_time('mysql')
            ];

            array_unshift($existing_searches, $new_search);


            foreach ($existing_searches as $index => &$search) {
                $search['id'] = $index + 1;
            }
            unset($search);

            update_user_meta($user_id, 'st_recent_search', $existing_searches);
        }


        $current_searches = get_user_meta($user_id, 'st_recent_search', true);
        if (!is_array($current_searches)) {
            $current_searches = [];
        }

        return st_comman_custom_response([
            'status'    => true,
            'message'   => __('Search Result.', 'streamit-api'),
            'data'      => $current_searches
        ]);
    }

    /**
     * Remove a recent searched word.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function remove_recent_search(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id = $data['user_id'] ?? null;

        $parameters = $request->get_query_params();
        $search_id = isset($parameters['id']) ? absint($parameters['id']) : 0;

        if ($search_id <= 0) {
            return st_comman_custom_response([
                'status'    => false,
                'message'   => __('Valid search ID is required.', 'streamit-api'),
                'data'      => []
            ]);
        }

        $existing_searches = get_user_meta($user_id, 'st_recent_search', true);

        if (!is_array($existing_searches) || empty($existing_searches)) {
            return st_comman_custom_response([
                'status'    => true,
                'message'   => __('No recent searches found.', 'streamit-api'),
                'data'      => []
            ]);
        }

        $updated_searches = array_filter($existing_searches, function ($search) use ($search_id) {
            return $search['id'] !== $search_id;
        });

        if (count($updated_searches) === count($existing_searches)) {
            return st_comman_custom_response([
                'status'    => false,
                'message'   => __('Search ID not found.', 'streamit-api'),
                'data'      => $existing_searches
            ]);
        }

        $updated_searches = array_values($updated_searches);
        foreach ($updated_searches as $index => &$search) {
            $search['id'] = $index + 1;
        }
        unset($search);

        update_user_meta($user_id, 'st_recent_search', $updated_searches);

        return st_comman_custom_response([
            'status'    => true,
            'message'   => __('Search removed successfully.', 'streamit-api')
        ]);
    }
}

new St_Streamit_Route_Callback();
