<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Streamit_Route_Controller
{
    public $module = 'content';
    public $name_space;

    public function __construct()
    {
        $this->name_space = STREAMIT_API_NAMESPACE;

        // Register REST routes
        add_action('rest_api_init', [$this, 'register_streamit_routes']);
    }

    /**
     * Register REST routes for the user module.
     *
     * @return void
     */
    public function register_streamit_routes() : void {
        $callbacks = new St_Streamit_Route_Callback();

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/search',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'search_list'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/dashboard/(?P<type>home|movie|tv_show|video)',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'dashboard'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/(?P<type>home|movie|tv_show|video)/genre',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'genre_list'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '(?P<type>movie|tv_show|video)/genre/(?P<genre>[a-zA-Z_-]+)',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'data_by_genre'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/settings',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'settings'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/view-all',
            array( 
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'view_all'],
                'permission_callback' => '__return_true',
                'args'                => array(
                    'post_type' => array(
                        'description' => 'Post type: movie, tv_show or video',
                        'type'        => 'string',
                        'required'    => true,
                        'enum'        => ['movie', 'tv_show', 'video'],
                    ),
                    'filter' => array(
                        'description' => 'Filter parameter',
                        'type'        => 'string',
                        'required'    => true,
                    ),
                    'genre' => array(
                        'description' => 'Genre / Categories ids',
                        'type'        => 'string',
                        'required'    => false,
                    ),
                    'tag' => array(
                        'description' => 'Tag ids',
                        'type'        => 'string',
                        'required'    => false,
                    ),
                    'selected' => array(
                        'description' => 'Selected post ids (comma-separated)',
                        'type'        => 'string',
                        'required'    => false,
                    ),
                    'paged' => array(
                        'description' => 'Current page number',
                        'type'        => 'int',
                        'required'    => false,
                        'default'     => 1
                    ),
                    'per_page' => array(
                        'description' => 'per page data count',
                        'type'        => 'int',
                        'required'    => false,
                        'default'     => 10
                    )
                ),
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/recent-search-list',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callbacks, 'recent_search_list'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/recent-search-add',
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => [$callbacks, 'recent_search'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/recent-search-remove',
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => [$callbacks, 'remove_recent_search'],
                'permission_callback' => '__return_true'
            )
        );

    }
}

new St_Streamit_Route_Controller();