<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}


class St_User_Route_Callback
{
    /**
     * Create a new user.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function create_user(WP_REST_Request $request)
    {
        $parameters = $request->get_params();

        $user_login = $parameters['user_login'];
        $user_email = $parameters['user_email'];
        $first_name = $parameters['first_name'];
        $last_name  = $parameters['last_name'];
        $user_pass  = $parameters['user_pass'];

        $errors = [];

        // Username validation.
        $valid_format = array('-', '_');
        if (username_exists($user_login)) {
            $errors[] = esc_html__('Username already exists.', 'streamit-api');
        } elseif (!ctype_alnum(str_replace($valid_format, '', $user_login))) {
            $errors[] = esc_html__('Username can only contain letters, numbers, "_", and "-".', 'streamit-api');
        }

        // Email validation.
        if (email_exists($user_email)) {
            $errors[] = esc_html__('Email already exists.', 'streamit-api');
        }

        // Password validation.
        if (empty($user_pass) || strlen($user_pass) < 6) {
            $errors[] = esc_html__('Password must be at least 6 characters long.', 'streamit-api');
        }

        // If errors exist, return them.
        if (!empty($errors)) {
            return st_comman_custom_response([
                'status'  => false,
                'message' => esc_html__('Registration failed.', 'streamit-api'),
                'errors'  => $errors,
            ], 400);
        }

        // User data array.
        $user_data = array(
            'user_login'   => $user_login,
            'user_pass'    => $user_pass,
            'user_email'   => $user_email,
            'first_name'   => $first_name,
            'last_name'    => $last_name,
        );

        // Attempt to create the user.
        $user_id = wp_insert_user($user_data);

        // Check if user creation was successful.
        if (is_wp_error($user_id)) {
            return st_comman_custom_response([
                'status'  => false,
                'message' => esc_html__('Registration failed.', 'streamit-api'),
                'errors'  => $user_id->get_error_messages(),
            ], 500);
        }
        $free_plan = function_exists('streamit_get_pmp_free_plans') ? streamit_get_pmp_free_plans() : '';
        if (!empty($free_plan)) {
            pmpro_changeMembershipLevel($free_plan[0], $user_id);
        }

        wp_update_user([
            'ID'             => $user_id,
            'first_name'     => $user_data['first_name'],
            'last_name'     => $user_data['last_name']
        ]);

        return st_comman_custom_response([
            'status'  => true,
            'message' => esc_html__('User registered successfully.', 'streamit-api'),
            'user_id' => $user_id,
        ], 201);
    }

    /**
     * Validate Token.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function validate_token(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, 401);

        $device_id = $request->get_param('device_id') ?? 0;
        if (!$device_id)
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Valid token.', 'streamit-api'),
            ]);


        $user_id = $data['user_id'];
        $loggedin_devices = (array) get_user_meta($user_id, 'streamit_loggedin_devices', true);

        if (!array_key_exists($device_id, $loggedin_devices))
            return st_comman_custom_response([
                'status'    => false,
                'message'   => esc_html__('Token has been expired.', 'streamit-api'),
            ]);


        return st_comman_custom_response([
            'status'    => true,
            'message'   => esc_html__('Valid token.', 'streamit-api'),
        ]);
    }

    /**
     * View user profile.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function view_profile(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status']) {
            return st_comman_custom_response($data, $data['status_code']);
        }

        $user_id = $data['user_id'];
        $user    = get_userdata($user_id);
        $img     = get_user_meta($user_id, 'streamit_profile_image', true);

        $response = [
            'first_name'    => $user->first_name,
            'last_name'     => $user->last_name,
            'user_id'       => $user->ID,
            'username'      => $user->user_login,
            'user_email'    => $user->user_email,
            'plan'          => st_user_plans($user->ID),
            'profile_image' => $img,
        ];

        return st_comman_custom_response([
            'status'  => true,
            'message' => esc_html__('User profile.', 'streamit-api'),
            'data'    => $response,
        ]);
    }

    /**
     * Update user profile.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function update_profile(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status']) {
            return st_comman_custom_response($data, $data['status_code']);
        }

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        $paramaters = $request->get_params();
        $userid = $data['user_id'];

        wp_update_user([
            'ID'            => $userid,
            'first_name'    => $paramaters['first_name'],
            'last_name'     => $paramaters['last_name'],
            'display_name'  => $paramaters['first_name'] . ' ' . $paramaters['last_name'],
        ]);

        $users = get_userdata($userid);

        $response = [
            'ID'            => $users->ID,
            'first_name'    => $users->first_name,
            'last_name'     => $users->last_name,
            'user_email'    => $users->user_email,
            'user_login'    => $users->user_login,
            'display_name'  => $users->display_name,
        ];

        if (isset($_FILES['profile_image']) && $_FILES['profile_image'] != null) {
            $profile_image = media_handle_upload('profile_image', 0);
            update_user_meta($userid, 'streamit_profile_image', wp_get_attachment_url($profile_image));
        }

        $response['profile_image'] = get_user_meta($userid, 'streamit_profile_image', true);

        return st_comman_custom_response([
            'status'  => true,
            'message' => esc_html__('Profile has been updated successfully', 'streamit-api'),
            'data'    => $response,
        ]);
    }

    /**
     * Change user account password.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function change_password(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id        = $data['user_id'];
        $parameters     = $request->get_params();
        $userdata       = get_user_by('ID', $user_id);

        if (!$userdata)
            return st_comman_custom_response([
                'status'    => false,
                'message'   => esc_html__('User not found.', 'streamit-api'),
            ]);

        if (!wp_check_password($parameters['old_password'], $userdata->data->user_pass))
            return st_comman_custom_response([
                'status'    => false,
                'message'   => esc_html__('Old password is invalid.', 'streamit-api'),
            ]);

        wp_set_password($parameters['new_password'], $userdata->ID);
        return st_comman_custom_response([
            'status'    => true,
            'message'   => esc_html__('Password has been changed successfully.', 'streamit-api'),
        ]);
    }

    /**
     * Forgot Password.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function forgot_password(WP_REST_Request $request)
    {
        // Get the parameters and sanitize the email input
        $parameters = $request->get_params();
        $email      = isset($parameters['email']) ? sanitize_email($parameters['email']) : '';

        // Check if user exists by email
        $user = get_user_by('email', $email);
        if (! $user) {
            return st_comman_custom_response(
                [
                    'status'  => false,
                    'message' => esc_html__('User not found with this email address.', 'streamit-api'),
                ]
            );
        }

        // Generate a new password and prepare the email content
        $title    = esc_html__('New Password', 'streamit-api');
        $password = st_string_generator();
        $message  = '<p><strong>' . esc_html__('Hello,', 'streamit-api') . '</strong></p>';
        $message .= '<p>' . esc_html__('You recently requested to reset your password. Here is the new password for your App:', 'streamit-api') . '</p>';
        $message .= '<p><strong>' . esc_html__('New Password:', 'streamit-api') . '</strong> ' . esc_html($password) . '</p>';
        $message .= '<p>' . esc_html__('Thanks,', 'streamit-api') . '</p>';

        // Set headers for HTML email
        $headers  = array();
        $headers[] = 'Content-Type: text/html; charset=UTF-8';

        // Try sending the email using wp_mail
        if (wp_mail($email, $title, $message, $headers)) {
            // Set the new password for the user
            wp_set_password($password, $user->ID);
            $response_message = esc_html__('Password has been sent successfully to your email address.', 'streamit-api');
        } else {
            // Fallback to PHP's mail function
            if (mail($email, $title, $message, implode("\r\n", $headers))) {
                wp_set_password($password, $user->ID);
                $response_message = esc_html__('Password has been sent successfully to your email address.', 'streamit-api');
            } else {
                // Return error if email could not be sent
                return st_comman_custom_response(
                    [
                        'status'  => false,
                        'message' => esc_html__('Currently, the email service is disabled by the admin.', 'streamit-api'),
                    ]
                );
            }
        }

        // Return a success response
        return st_comman_custom_response(
            [
                'status'  => true,
                'message' => $response_message,
            ]
        );
    }

    /**
     * Delete User Account.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function delete_user_account(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        require_once(ABSPATH . 'wp-admin/includes/user.php');
        $user_id = $data['user_id'];
        $user    = wp_delete_user($user_id, true);

        if ($user)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('User Deleted Successfully.', 'streamit-api'),
            ]);

        return st_comman_custom_response([
            "status"    => false,
            "message"   => esc_html__('User not Deleted.', 'streamit-api'),
        ]);
    }

    /**
     * Rest Nonce.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function rest_nonce(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $action = 'wc_store_api';
        $nonce  = wp_create_nonce($action);

        return st_comman_custom_response([
            "status"    => true,
            "message"   => esc_html__('Store api nonce.', 'streamit-api'),
            "data"      => ['nonce' => $nonce]
        ]);
    }

    /**
     * Get User Devices.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_devices(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, 401);

        $user_id = $data['user_id'];

        $logged_in_devices = get_user_meta($user_id, 'streamit_loggedin_devices', true);

        if ($logged_in_devices)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('List of logged-in devices.', 'streamit-api'),
                "data"      => $logged_in_devices
            ]);

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('No devices found.', 'streamit-api'),
            "data"       => []
        ]);
    }

    /**
     * Add User Devices.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function add_device(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, 401);

        $user_id        = $data['user_id'];
        $paramaters     = $request->get_params();
        $device_id      = sanitize_text_field($paramaters['device_id']);
        $device_model   = sanitize_text_field($paramaters['device_model']);
        $token          = sanitize_text_field($paramaters['login_token']);

        $logged_in_devices = get_user_meta($user_id, 'streamit_loggedin_devices', true);
        if (!$logged_in_devices)
            $logged_in_devices = [];

        $logged_in_devices[$device_id] = [
            'device_id'     => $device_id,
            'device_mdel'   => $device_model,
            'login_time'    => current_time('mysql'),
            'token'         => $token
        ];
        update_user_meta($user_id, 'streamit_loggedin_devices', $logged_in_devices);

        return st_comman_custom_response([
            "status"    => true,
            "message"   => esc_html__('List of logged-in devices.', 'streamit-api'),
            "data"      => $logged_in_devices
        ]);
    }

    /**
     * Remove User Devices.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function remove_device(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, 401);

        $user_id        = $data['user_id'];
        $paramaters     = $request->get_params();
        $device_id      = $paramaters["device_id"] ?? false;

        if (empty($device_id)) {
            update_user_meta($user_id, 'streamit_loggedin_devices', false);
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('All devices are removed.', 'streamit-api'),
                "data"       => []
            ]);
        }

        $device_id = sanitize_text_field($paramaters['device_id']);

        $logged_in_devices = get_user_meta($user_id, 'streamit_loggedin_devices', true);
        if ($logged_in_devices && key_exists($device_id, $logged_in_devices))
            unset($logged_in_devices[$device_id]);

        update_user_meta($user_id, 'streamit_loggedin_devices', $logged_in_devices);

        if (empty($logged_in_devices)) {
            update_user_meta($user_id, 'streamit_loggedin_devices', false);
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('No devices found.', 'streamit-api'),
                "data"       => []
            ]);
        }

        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('List of logged-in devices.', 'streamit-api'),
            "data"       => $logged_in_devices
        ]);
    }
    /**
     * Get User WatchList.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_watchlist(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id    = $data['user_id'];

        $response = [];
        $movie_data = $video_data = $tvshow_data = $episode_data = [];
        $movie_ids = $tvshow_ids = $video_ids = $episode_ids = [];
        $parameters     = $request->get_params();
        $posts_per_page = isset($parameters["posts_per_page"]) ? $parameters["posts_per_page"] : 10;
        $paged          = isset($parameters["page"]) ? $parameters["page"] : 1;
        $post_type      = $parameters['type'] === 'tv_show' ? 'tvshow' : ($parameters['type'] ?? 'movie');
        $watchlist_data = function_exists('streamit_user_watchlist') ? streamit_user_watchlist($user_id) : [];

        if (empty($watchlist_data))
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('No data found.', 'streamit-api'),
                "data"      => []
            ]);

        // If a post_type is provided, filter watchlist data by that type
        if ($post_type && isset($watchlist_data[$post_type])) {
            $watchlist_data = [$post_type => $watchlist_data[$post_type]];
        }

        foreach ($watchlist_data as $section => $section_list) {
            foreach ($section_list as $post_id) {
                if ($section === 'movie') {
                    $movie_ids[] = (int) $post_id;
                } elseif ($section === 'tvshow') {
                    $tvshow_ids[] = (int) $post_id;
                } elseif ($section === 'video') {
                    $video_ids[] = (int) $post_id;
                } elseif ($section === 'episode') {
                    $episode_ids[] = (int) $post_id;
                }
            }
        }

        // Fetch the data based on available post IDs
        if (!empty($movie_ids)) $movie_data = streamit_get_movies(['per_page' => $posts_per_page, 'paged' => $paged, 'include' => $movie_ids])->results;
        if (!empty($tvshow_ids)) $tvshow_data = streamit_get_tvshows(['per_page' => $posts_per_page, 'paged' => $paged, 'include' => $tvshow_ids])->results;
        if (!empty($video_ids)) $video_data = streamit_get_videos(['per_page' => $posts_per_page, 'paged' => $paged, 'include' => $video_ids])->results;
        if (!empty($episode_ids)) $episode_data = streamit_get_episodes(['per_page' => $posts_per_page, 'paged' => $paged, 'include' => $episode_ids])->results;

        // Format the response based on the retrieved data
        foreach ($movie_data as $movie) {
            $response[]  = st_movie_content_format($movie);
        }

        foreach ($tvshow_data as $tvshow) {
            $response[]  = st_tvshow_content_format($tvshow); // Corrected to $tvshow
        }
        foreach ($video_data as $video) {
            $response[]  = st_video_content_format($video); // Corrected to $video
        }
        foreach ($episode_data as $episode) {
            $response[]  = st_episode_content_format($episode); // Corrected to $episode
        }

        return st_comman_custom_response([
            "status"    => true,
            "message"    => esc_html__('Watchlist result.', 'streamit-api'),
            "data"        => $response
        ]);
    }


    /**
     * Manage User WatchList.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function manage_watchlist(WP_REST_Request $request)
    {

        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id = $data['user_id'];
        $post_id = $request->get_param('post_id');
        $post_type = $request->get_param('post_type');
        $post_type  = $post_type === 'tv_show' ? 'tvshow' : ($post_type ?? 'movie');
        $action  = $request->get_param('action');
        if (empty($post_id) || empty($post_type))
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('No data found.', 'streamit-api'),
                "data"      => (object) []
            ]);

        $isAdded = false;
        $message = esc_html__('Somthing Went Wrong.', 'streamit-api');
        if ($action == 'add') {
            $result = function_exists('streamit_add_to_watchlist') ? streamit_add_to_watchlist($post_id, $post_type, $user_id) : false;
            if ($result) {
                $isAdded = true;
                switch ($post_type) {
                    case 'movie':
                        $message = esc_html__('Movie added to watchlist successfully!', 'streamit-api');
                        break;
                    case 'tvshow':
                        $message = esc_html__('Tv Show added to watchlist successfully!', 'streamit-api');
                        break;
                    case 'video':
                        $message = esc_html__('Video added to watchlist successfully!', 'streamit-api');
                        break;
                    default:
                        $message = esc_html__('Added to watchlist.', 'streamit-api');
                        break;
                }
            }
        } elseif ($action == 'remove') {
            $result = function_exists('streamit_remove_from_watchlist') ? streamit_remove_from_watchlist($post_id, $post_type, $user_id) : false;
            if ($result) {
                switch ($post_type) {
                    case 'movie':
                        $message = esc_html__('Movie removed from your watchlist.', 'streamit-api');
                        break;
                    case 'tvshow':
                        $message = esc_html__('Tv Show removed from your watchlist.', 'streamit-api');
                        break;
                    case 'video':
                        $message = esc_html__('Video removed from your watchlist.', 'streamit-api');
                        break;
                    default:
                        $message = esc_html__('Removed from watchlist.', 'streamit-api');
                        break;
                }
            }
        }

        return st_comman_custom_response([
            "status"    => true,
            "message"   => $message,
            "data"      => array('is_added' => $isAdded)
        ]);
    }

    /**
     * Get User continue watch list.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_continue_watch(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status']) {
            return st_comman_custom_response($data, $data['status_code']);
        }

        $userid     = $data['user_id'];
        $parameters = $request->get_params();
        $type       = $parameters['post_type'] ?? '';
        $per_page   = isset($parameters['per_page']) ? max(1, (int) $parameters['per_page']) : 10;
        $page       = isset($parameters['page']) ? max(1, (int) $parameters['page']) : 1;

        $continue_watch_post_type = in_array($type, ['movie', 'episode', 'video'], true) ? $type : '';

        $continue_watch = streamit_get_continue_watching($userid, $continue_watch_post_type);
        $finalResult = [];  

        if (!empty($continue_watch) && is_array($continue_watch)) {
            $post_types = ['movie', 'video', 'episode'];

            foreach ($post_types as $post_type) {
                $posts = $continue_watch[$post_type] ?? [];

                if (!is_array($posts) || empty($posts)) {
                    continue;
                }

                foreach ($posts as $post_id => $watch_data) {
                    $post_id     = (int) $post_id;
                    $getter_func = 'streamit_get_' . $post_type;
                    $format_func = 'st_' . $post_type . '_content_format';

                    if (!function_exists($getter_func)) {
                        continue;
                    }

                    $post_data = call_user_func($getter_func, $post_id);

                    if (is_wp_error($post_data) || empty($post_data)) {
                        continue;
                    }

                    $formatted_data = function_exists($format_func) ? call_user_func($format_func, $post_data) : [];

                    if (empty($formatted_data)) {
                        continue;
                    }

                    $formatted_data['watched_duration'] = [
                        'watched_time'            => (int)($watch_data['watched_time'] ?? 0),
                        'watched_total_time'      => (int)($watch_data['watched_total_time'] ?? 0),
                        'watched_time_percentage' => (float)($watch_data['watched_time_percentage'] ?? 0),
                    ];

                    $finalResult[] = $formatted_data;
                }
            }
        }

        if (empty($finalResult)) {
            return st_comman_custom_response([
                "status"  => true,
                "message" => esc_html__('No data found.', 'streamit-api'),
                "data"    => [],
            ]);
        }

        // Paginate results
        $total_items = count($finalResult);
        $total_pages = (int) ceil($total_items / $per_page);
        $offset      = ($page - 1) * $per_page;
        $paginated_results = array_slice($finalResult, $offset, $per_page);

        return st_comman_custom_response([
            "status"      => true,
            "message"     => esc_html__('Continue watch Result.', 'streamit-api'),
            "data"        => $paginated_results,
            "pagination"  => [
                'total'      => $total_items,
                'per_page'   => $per_page,
                'current'    => $page,
                'total_page' => $total_pages,
            ],
        ]);
    }

    /**
     * Add User continue watch list.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function add_continue_watch(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $userid = $data['user_id'];
        $parameters             = $request->get_params();
        $postID                 = $parameters['post_id'];
        $post_type              = $parameters['post_type'];

        $watchedTime            = $parameters['watched_time'];
        $watchedTotalTime       = $parameters['watched_total_time'];

        $data = [
            'post_id'           => $postID,
            'watched_time'      => $watchedTime,
            'watched_total_time' => $watchedTotalTime
        ];
        $response = streamit_manage_continue_watching($userid, $post_type, $data);

        if (!$response)
            return st_comman_custom_response([
                'status'     => true,
                'message'    => esc_html__('No data found.', 'streamit-api'),
                'data'       => []
            ]);

        $continue_watching_list = streamit_get_continue_watching($userid);
        $finalResult = [];
        if (! empty($continue_watching_list) && is_array($continue_watching_list)) {

            $post_types = ['movie', 'video', 'episode'];

            foreach ($post_types as $post_type) {

                $posts = $continue_watching_list[$post_type] ?? [];

                if (! is_array($posts) || empty($posts)) {
                    continue;
                }

                foreach ($posts as $post_id => $data) {

                    $post_id       = (int) $post_id;
                    $getter_func   = 'streamit_get_' . $post_type;
                    $format_func   = 'st_' . $post_type . '_content_format';

                    if (! function_exists($getter_func)) {
                        continue;
                    }

                    $post_data = call_user_func($getter_func, $post_id);

                    if (is_wp_error($post_data) || empty($post_data)) {
                        continue;
                    }

                    $formatted_data = function_exists($format_func) ? call_user_func($format_func, $post_data) : [];

                    if (empty($formatted_data)) {
                        continue;
                    }

                    $formatted_data['watched_duration'] = [
                        'watched_time'            => (int) ($data['watched_time'] ?? 0),
                        'watched_total_time'      => (int) ($data['watched_total_time'] ?? 0),
                        'watched_time_percentage' => (float) ($data['watched_time_percentage'] ?? 0),
                    ];

                    $finalResult[] = $formatted_data;
                }
            }
        }

        return st_comman_custom_response([
            'status'     => true,
            'message'    => esc_html__('Continue watch Result.', 'streamit-api'),
            'data'       => $finalResult
        ]);
    }

    /**
     * Remove User continue watch list.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function remove_continue_watch(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $userid = $data['user_id'];
        $parameters = $request->get_params();
        $post_id    = $parameters['post_id'];
        $post_type  = $parameters['post_type'];

        $response = streamit_remove_continue_watching($userid, $post_type, $post_id);

        if (empty($response))
            return st_comman_custom_response([
                "status"     => true,
                "message"    => esc_html__('No data found.', 'streamit-api'),
                "data"       => []
            ]);

        $continue_watching_list = streamit_get_continue_watching($userid);
        $finalResult = [];
        if (! empty($continue_watching_list) && is_array($continue_watching_list)) {

            $post_types = ['movie', 'video', 'episode'];

            foreach ($post_types as $post_type) {

                $posts = $continue_watching_list[$post_type] ?? [];

                if (! is_array($posts) || empty($posts)) {
                    continue;
                }

                foreach ($posts as $post_id => $data) {

                    $post_id       = (int) $post_id;
                    $getter_func   = 'streamit_get_' . $post_type;
                    $format_func   = 'st_' . $post_type . '_content_format';

                    if (! function_exists($getter_func)) {
                        continue;
                    }

                    $post_data = call_user_func($getter_func, $post_id);

                    if (is_wp_error($post_data) || empty($post_data)) {
                        continue;
                    }

                    $formatted_data = function_exists($format_func) ? call_user_func($format_func, $post_data) : [];

                    if (empty($formatted_data)) {
                        continue;
                    }

                    $formatted_data['watched_duration'] = [
                        'watched_time'            => (int) ($data['watched_time'] ?? 0),
                        'watched_total_time'      => (int) ($data['watched_total_time'] ?? 0),
                        'watched_time_percentage' => (float) ($data['watched_time_percentage'] ?? 0),
                    ];

                    $finalResult[] = $formatted_data;
                }
            }
        }
        return st_comman_custom_response([
            "status"     => true,
            "message"    => esc_html__('Continue watch Result.', 'streamit-api'),
            "data"       => $finalResult
        ]);
    }

    /**
     * Manage Like/Dislike actions.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function manage_like_dislike(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        // Validate token
        if (!$data['status']) {
            return st_comman_custom_response($data, $data['status_code']);
        }

        $user_id = $data['user_id'];
        $parameters = $request->get_params();

        // Validate required parameters
        if (empty($parameters['post_id']) || empty($parameters['post_type'])) {
            return st_comman_custom_response([
                "status"  => false,
                "message" => esc_html__('Post ID and Post Type are required.', 'streamit-api'),
                "data"    => []
            ]);
        }

        // Sanitize the input parameters
        $post_id = absint($parameters['post_id']);
        $post_type =  $parameters['post_type'] === 'tv_show' ? 'tvshow' : ($parameters['post_type'] ?? '');

        // Handle like or dislike action
        $do_like = function_exists('streamit_handle_like') ? streamit_handle_like([
            'post_id'   => $post_id,
            'user_id'   => $user_id,
            'post_type' => $post_type,
            'action'    => 'like',
        ]) : false;

        // Check if like/dislike operation was successful
        if ($do_like) {
            $is_liked = streamit_is_like($post_id, $post_type, $user_id);

            // Prepare response based on like/dislike status
            if ($is_liked) {
                $message = esc_html__('You liked this.', 'streamit-api');
                $is_liked_status = true;
            } else {
                $message = esc_html__('You unliked this.', 'streamit-api');
                $is_liked_status = false;
            }

            // Return success response
            return st_comman_custom_response([
                "status"  => true,
                "message" => $message,
                "data"    => ['is_added' => $is_liked_status]
            ]);
        }

        // Handle failure case
        return st_comman_custom_response([
            "status"  => false,
            "message" => esc_html__('Something went wrong. Please try again.', 'streamit-api'),
            "data"    => []
        ]);
    }


    /**
     * Add Player Id.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function add_player_id(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $current_user_id = $data['user_id'];
        $parameters = $request->get_params();

        $parameters = st_sanitize_recursive_text_fields($parameters);

        // FireBase Notification Token
        $firebase_tokens = [];
        if ($user_firebase_tokens = get_user_meta($current_user_id, 'streamit_firebase_tokens', true)) {
            $firebase_tokens = $user_firebase_tokens;
        }
        if ($request->has_param('firebase_token') && !empty($request->get_param('firebase_token'))) {
            array_push($firebase_tokens, $request->get_param('firebase_token'));
        }
        update_user_meta($current_user_id, 'streamit_firebase_tokens', array_unique($firebase_tokens));

        return st_comman_custom_response([
            "status"   => true,
            "message"  => esc_html__('Player ID`s.', 'streamit-api'),
            "data"     => $firebase_tokens
        ]);
    }

    /**
     * Remove Player Id.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function remove_player_id($request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $current_user_id = $data['user_id'];
        $parameters = $request->get_params();
        $parameters = st_sanitize_recursive_text_fields($parameters);
        $player_id = $parameters['player_id'];

        if (!empty($player_id)) {
            $firebase_tokens = get_user_meta($current_user_id, 'streamit_firebase_tokens', true);
            $firebase_tokens = json_encode($firebase_tokens);

            if (is_array($firebase_tokens) && in_array($player_id, $firebase_tokens)) {
                $index  = array_search($player_id, $firebase_tokens);
                unset($firebase_tokens[$index]);
                update_user_meta($current_user_id, "streamit_firebase_tokens", json_encode(array_filter($firebase_tokens)));
                return st_comman_custom_response([
                    'status'    => true,
                    'message'   => esc_html__('Player ID`s', 'streamit-api'),
                    'data'      => $firebase_tokens
                ], 200);
            }
        }

        return st_comman_custom_response([
            'status'    => true,
            'message'   => esc_html__('Player Id not present | records not available.', 'streamit-api'),
            'data'      => []
        ], 200);
    }
}
