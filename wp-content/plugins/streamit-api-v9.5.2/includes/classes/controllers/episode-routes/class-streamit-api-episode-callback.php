<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Episode_Route_Callback
{

    /**
     * Get Episodes Details.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function episode_details(WP_REST_Request $request)
    {
        $data       = st_token_validation($request);

        $user_id    = null;
        if ($data['status'])
            $user_id = $data['user_id'];


        $response   = [];
        $parameters = $request->get_params();
        $episode_id = $parameters['episode_id'] ?? 0;
        $data = function_exists('streamit_get_episode') ? streamit_get_episode((int)$episode_id) : '';

        if (empty($data))
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('No data found.', 'streamit-api'),
                'data'      => (object) []
            ]);

        $response = st_episode_content_format($data, false);

        if (function_exists('streamit_manage_views_count'))
            streamit_manage_views_count($data);

        return st_comman_custom_response([
            'status'    => true,
            'message'   => esc_html__('Episode Details', 'streamit-api'),
            'data'      => $response
        ]);
    }
}
