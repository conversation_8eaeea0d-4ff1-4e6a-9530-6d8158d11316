<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Playlist_Route_Callback
{

    /**
     * Get Playlist Details.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_playlist(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status']) {
            return st_comman_custom_response($data, $data['status_code']);
        }

        $user_id      = $data['user_id'];
        $parameters   = $request->get_params();
        $post_type    = $parameters['post_type'] ?? false;
        $post_id      = isset($parameters['post_id']) ? $parameters['post_id'] : '';

        if (!$post_type || !in_array($post_type, ['movie_playlist', 'episode_playlist', 'video_playlist'])) {
            return st_comman_custom_response([
                'status'  => true,
                'message' => esc_html__('Playlists not found.', 'streamit-api'),
                'data'    => []
            ]);
        }

        $args = [
            'user_id'     => $user_id,
            'per_page'    => -1,
        ];

        $playlist_function = 'streamit_get_' . $post_type . 's';
        $playlists = $playlist_function($args);

        if (empty($playlists->results)) {
            return st_comman_custom_response([
                'status'  => true,
                'message' => esc_html__('Playlists not found.', 'streamit-api'),
                'data'    => []
            ]);
        }

        $playlist_data = [];
        $post = explode('_', $post_type)[0]; // Extract 'movie', 'episode', 'video'

        foreach ($playlists->results as $st_data) {
            $playlist_id = $st_data->get_playlist_id();
            $playlist_relation = streamit_get_playlist_item($playlist_id, $post);
            $first_data = !empty($playlist_relation[0]) ? $playlist_relation[0] : '';

            // Initialize image URL
            $image_url = st_app_get_default_image();

            if (!empty($first_data)) {
                $movie_data = streamit_get_movie((int) $first_data);
                if (!empty($movie_data)) {
                    $thumbnail_id = $movie_data->get_meta('thumbnail_id');
                    $image_url = !empty($thumbnail_id) ? wp_get_attachment_image_url($thumbnail_id, 'full') : $image_url;
                }
            }

            $data_count = is_array($playlist_relation) ? count($playlist_relation) : 0;
            $count_text = sprintf(
                /* Translators: %s represents the number of items in the playlist */
                esc_html(_n('%s item', '%s items', $data_count, 'streamit-api')),
                number_format_i18n($data_count)
            );
            $attach_post_ids = [];
            if (!empty($post_id)) :
                $attach_post_ids = streamit_get_playlist_item($playlist_id, $post);
            endif;

            $playlist_data[] = [
                'playlist_id'   => $playlist_id,
                'playlist_name' => $st_data->get_playlist_name(),
                'playlist_slug' => $st_data->get_playlist_slug(),
                'image'         => $image_url,
                'data_count'    => $count_text, // Proper formatted text
                'post_type'     => $post_type,
                'label'         => rtrim($post_type, '_playlist'),
                'is_in_playlist' =>  in_array($post_id, $attach_post_ids)
            ];
        }

        return st_comman_custom_response([
            'status'  => true,
            'message' => esc_html__('Playlists retrieved successfully.', 'streamit-api'),
            'data'    => $playlist_data
        ]);
    }


    /**
     * Creat Playlist Details.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function create_playlist(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters = $request->get_params();
        $post_type  = $parameters['post_type'] ?? false;
        $playlist_id = isset($parameters['id']) ? $parameters['id'] : '';

        if (!$post_type || !in_array($post_type, ['movie_playlist', 'episode_playlist', 'video_playlist']))
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Playlist type not found.', 'streamit-api'),
                'data'      => []
            ]);

        if (!isset($parameters['title']) || empty($parameters['title'])) {
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Please Add Playlist Name.', 'streamit-api'),
                'data'      => []
            ]);
        }
        $playlist_function = empty($playlist_id) ? 'streamit_add_' . $post_type : 'streamit_update_' . $post_type;

        $args = array(
            'playlist_name' => $parameters['title'],
            'user_id'       => $data['user_id']
        );
        
        if (!empty($playlist_id)) {
            // Call the update function with both parameters
            $playlist = call_user_func($playlist_function, (int) $playlist_id, $args);
        } else {
            // Call the add function with only the $args array
            $playlist = call_user_func($playlist_function, $args);
        }
        
        if (!is_wp_error($playlist)) {
            $success_message = empty($playlist_id) 
                ? esc_html__('Playlist created successfully!', 'streamit-api')
                : esc_html__('Playlist changes saved successfully.', 'streamit-api');
            
            return st_comman_custom_response([
                'status'    => true,
                'message'   => $success_message
            ]);
        }
    
        return st_comman_custom_response([
            'status'    => false,
            'message'   => esc_html__('Something went wrong. Try again.', 'streamit-api')
        ]);
    }

    /**
     * Delete Playlist Details.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function delete_playlist(WP_REST_Request $request)
    {
        $data = st_token_validation($request);

        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters = $request->get_params();
        $user_id      = $data['user_id'];

        $post_type  = $parameters['post_type'] ?? false;
        $playlist_id = $parameters['playlist_id'];

        if (!$post_type || !in_array($post_type, ['movie_playlist', 'episode_playlist', 'video_playlist']))
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Playlist type not found.', 'streamit-api'),
                'data'      => []
            ]);

        $playlist_function = 'streamit_get_' . $post_type;
        $playlists_details = $playlist_function((int)$playlist_id);
        if (empty($playlists_details) || is_wp_error($playlists_details)) {
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Playlist not available', 'streamit-api'),
                "data"      => []
            ]);
        }

        if ((int)$playlists_details->get_user_id() !== $user_id)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Your are not allow to delete playlist.', 'streamit-api'),
                "data"      => []
            ]);

        $playlist_function = 'streamit_delete_' . $post_type;

        $playlist = $playlist_function((int)$playlist_id);

        if (!is_wp_error($playlist))
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Playlist deleted successfully.', 'streamit-api'),
            ]);

        return st_comman_custom_response([
            'status'    => false,
            'message'   => esc_html__('Something went wrong. Try again.', 'streamit-api')
        ]);
    }

    /**
     * get Platylist Data
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function get_playlist_items(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters     = $request->get_params();
        $user_id      = $data['user_id'];
        $per_page       = $parameters['posts_per_page'] ?? 10;
        $paged          = $parameters['page'] ?? 1;
        $playlist_id    = $parameters['playlist_id'];
        $playlist_post_type      = $parameters['post_type'];
        $post_type     = rtrim($playlist_post_type, "_playlist");

        $playlist_function = 'streamit_get_' . $playlist_post_type;
        $playlists_details = $playlist_function((int)$playlist_id);
        if (empty($playlists_details) || is_wp_error($playlists_details)) {
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Playlist not available', 'streamit-api'),
                "data"      => []
            ]);
        }

        if ((int)$playlists_details->get_user_id() !== $user_id)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Your are not allow to see playlist data.', 'streamit-api'),
                "data"      => []
            ]);

        $post_ids = function_exists('streamit_get_playlist_item') ? streamit_get_playlist_item($playlist_id, $post_type) : '';
        if (empty($post_ids) || !is_array($post_ids))
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Playlist is empty.', 'streamit-api'),
                "data"      => []
            ]);


        $args = [
            'per_page' => $per_page,
            'paged'    => $paged,
            'include'  => $post_ids
        ];

        $function_name = 'streamit_get_' . $post_type . 's';
        $datas = $function_name($args)->results;
        foreach ($datas as $post) {
            $format_function = 'st_' . $post_type . '_content_format';
            $playlist_content[] = $format_function($post);
        }


        return st_comman_custom_response([
            "status"    => true,
            "message"   => esc_html__('Playlist.', 'streamit-api'),
            "data"      => $playlist_content
        ]);
    }


    public function add_playlist_items(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);
    
        $parameters     = $request->get_params();
        $user_id      = $data['user_id'];
        $playlist_id    = $parameters['playlist_id'];
        $post_id        = absint($parameters['post_id']);
        $type           = $parameters['post_type'];
        $post_type      = rtrim($type, "_playlist");

        $playlist_function = 'streamit_get_' . $type;
        $playlists_details = $playlist_function((int)$playlist_id);
        if (empty($playlists_details) || is_wp_error($playlists_details)) {
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Playlist not available', 'streamit-api'),
                "data"      => []
            ]);
        }

        if ((int)$playlists_details->get_user_id() !== $user_id)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Your are not allow to add item.', 'streamit-api'),
                "data"      => []
            ]);

        $args = array(
            'playlist_id'        => (int)$playlist_id,
            'post_type'          => $post_type,
            'post_id'            => (int)$post_id,
        );
        $response_msg   = function_exists('streamit_add_playlist_relation') ? streamit_add_playlist_relation($args) : false;
        if (is_wp_error($response_msg))
            return st_comman_custom_response([
                "status"    => false,
                "message"   => esc_html__('Playlist media Not added. Try Again.', 'streamit-api')
            ]);
    
        // Set type-specific success message
        $success_message = '';
        switch ($type) {
            case 'movie_playlist':
                $success_message = esc_html__('Movie added to your playlist successfully!', 'streamit-api');
                break;
            case 'tv_show_playlist':
                $success_message = esc_html__('Tv Show added to your playlist successfully!', 'streamit-api');
                break;
            case 'video_playlist':
                $success_message = esc_html__('Video added to your playlist successfully!', 'streamit-api');
                break;
            default:
                $success_message = esc_html__('Post added successfully.', 'streamit-api');
        }
    
        return st_comman_custom_response([
            "status"    => true,
            "message"   => $success_message
        ]);
    }

    /**
     * Delete Platylist Data
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function delete_playlist_items(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters     = $request->get_params();
        $user_id        = $data['user_id'];
        $playlist_id    = $parameters['playlist_id'];
        $post_id        = absint($parameters['post_id']);
        $type           = $parameters['post_type'];
        $post_type      = rtrim($type, "_playlist");

        $playlist_function = 'streamit_get_' . $type;
        $playlists_details = $playlist_function((int)$playlist_id);
        if (empty($playlists_details) || is_wp_error($playlists_details)) {
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Playlist not available', 'streamit-api'),
                "data"      => []
            ]);
        }

        if ((int)$playlists_details->get_user_id() !== $user_id)
            return st_comman_custom_response([
                "status"    => true,
                "message"   => esc_html__('Your are not allow to delete item.', 'streamit-api'),
                "data"      => []
            ]);

        $response_msg   = function_exists('streamit_delete_playlist_item') ? streamit_delete_playlist_item($playlist_id, $post_type, $post_id) : false;
        if (is_wp_error($response_msg))
            return st_comman_custom_response([
                'status'    => false,
                'message'   => esc_html__('Playlist media not removed. Try again.', 'streamit-api')
            ]);

        $success_message = '';
        switch ($type) {
            case 'movie_playlist':
                $success_message = esc_html__('Movie removed from your playlist successfully!', 'streamit-api');
                break;
            case 'tv_show_playlist':
                $success_message = esc_html__('Tv Show removed from your playlist successfully!', 'streamit-api');
                break;
            case 'video_playlist':
                $success_message = esc_html__('Video removed from your playlist successfully!', 'streamit-api');
                break;
            default:
                $success_message = esc_html__('Post deleted successfully.', 'streamit-api');
                break;
        }
    
        return st_comman_custom_response([
            'status'    => true,
            'message'   => $success_message
        ]);
    }
}
