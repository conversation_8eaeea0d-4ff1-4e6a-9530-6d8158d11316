<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_Cast_Route_Callback
{

    /**
     * Get Person Details.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function person_details(WP_REST_Request $request)
    {
        $parameters = $request->get_params();

        $cast_id    = $parameters['cast_id'];
        $most_viewed_content         = [];
        $response                     = null;

        $person_data = function_exists('streamit_get_person') ? streamit_get_person((int)$cast_id) : [];
        if (is_wp_error($person_data) || empty($person_data))
            return st_comman_custom_response(array(
                'status'    => true,
                'message'    => esc_html__('Details not found.', 'streamit-api'),
                'data'         => (object) []
            ));

        $term_name = array();
        $term_list = streamit_get_term_relationships($cast_id, 'person_category');

        if (!is_wp_error($term_list) && !empty($term_list)) {
            foreach ($term_list as $term_id) {
                $term = streamit_get_term($term_id, 'person_category');
                $term_name[] = $term->get_term_name();
            }
        }

        $term_name_string = !empty($term_name) ? implode(', ', $term_name) : '';


        $movie_cast      = (array) $person_data->get_meta('_movie_cast');
        $movie_crew      = (array) $person_data->get_meta('_movie_crew');
        $tv_show_cast    = (array) $person_data->get_meta('_tvshow_cast');
        $tv_show_crew    = (array) $person_data->get_meta('_tvshow_crew');

        $birthday        = date_i18n($person_data->get_meta('_birthday'));
        $deathday        = date_i18n($person_data->get_meta('_deathday'));
        $image_url       = wp_get_attachment_image_src($person_data->get_meta('thumbnail_id'), 'full');


        $movie_cast   = array_filter($movie_cast);
        $movie_crew   = array_filter($movie_crew);
        $tv_show_cast = array_filter($tv_show_cast);
        $tv_show_crew = array_filter($tv_show_crew);

        $all_ids = array_merge($movie_cast, $movie_crew, $tv_show_cast, $tv_show_crew);
        $credits = count($all_ids);

        $response = [
            'id'                => $cast_id,
            'title'             => $person_data->get_post_title(),
            'description'       => wp_strip_all_tags($person_data->get_post_content()),
            'image'             => !empty($image_url) ? $image_url[0] : '',
            'category'          => $term_name_string,
            'credits'           => $credits,
            'also_known_as'     => $person_data->get_meta('_also_known_as'),
            'place_of_birth'    => $person_data->get_meta('_place_of_birth'),
            'birthday'          => $birthday,
            'deathday'          => $deathday,
        ];

        $most_viewed_content = array();
        if (!empty($movie_cast)) {
            foreach ($movie_cast as $movie_id) {
                $movie_data = streamit_get_movie((int)$movie_id);
                if (!is_wp_error($movie_data) && !empty($movie_data)) {
                    $most_viewed_content[] = $this->streamit_person_work_history($movie_data,  $cast_id, '_cast');
                }
            }
        }
        if (!empty($movie_crew)) {
            foreach ($movie_crew as $movie_id) {
                $movie_data = streamit_get_movie((int)$movie_id);
                if (!is_wp_error($movie_data) && !empty($movie_data)) {
                    $most_viewed_content[] = $this->streamit_person_work_history($movie_data,  $cast_id, '_crew');
                }
            }
        }
        if (!empty($tv_show_cast)) {
            foreach ($tv_show_cast as $tvshow_id) {
                $tvshow_data = streamit_get_tvshow((int)$tvshow_id);
                if (!is_wp_error($tvshow_data) && !empty($tvshow_data)) {
                    $most_viewed_content[] = $this->streamit_person_work_history($tvshow_data, $cast_id, '_cast');
                }
            }
        }
        if (!empty($tv_show_crew)) {
            foreach ($tv_show_cast as $tvshow_id) {
                $tvshow_data = streamit_get_tvshow((int)$tvshow_id);
                if (!is_wp_error($tvshow_data) && !empty($tvshow_data)) {
                    $most_viewed_content[] = $this->streamit_person_work_history($tvshow_data, $cast_id, '_crew');
                }
            }
        }
        return st_comman_custom_response(array(
            'status'    => true,
            'message'    => esc_html__('Cast details.', 'streamit-api'),
            'data'         => [
                'details'                 => $response,
                'most_viewed_content'     => $most_viewed_content
            ]
        ));
    }

    /**
     * Get Person Work History.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function person_work_history(WP_REST_Request $request)
    {
        $response             = [];
        $parameters         = $request->get_params();
        $cast_id             = $parameters['cast_id'];
        $cats_data  =  function_exists('streamit_get_person') ? streamit_get_person((int) $cast_id) : [];
        if ($cats_data === null || empty($cats_data))
            return st_comman_custom_response(array(
                'status'    => true,
                'message'    => esc_html__('No data found.', 'streamit-api'),
                'data'         => []
            ));

        $type            = $parameters['type'] ?? "all";
        $movie_cast      = (array) $cats_data->get_meta('_movie_cast');
        $movie_crew      = (array) $cats_data->get_meta('_movie_crew');
        $tv_show_cast    = (array) $cats_data->get_meta('_tvshow_cast');
        $tv_show_crew    = (array) $cats_data->get_meta('_tvshow_crew');


        $movie_cast   = array_filter($movie_cast);
        $movie_crew   = array_filter($movie_crew);
        $tv_show_cast = array_filter($tv_show_cast);
        $tv_show_crew = array_filter($tv_show_crew);

        $response = array();
        if (!empty($movie_cast)) {
            foreach ($movie_cast as $movie_id) {
                $movie_data = streamit_get_movie((int)$movie_id);
                if (!is_wp_error($movie_data) && !empty($movie_data)) {
                    $response[] = $this->streamit_person_work_history($movie_data,  $cast_id, '_cast');
                }
            }
        }
        if (!empty($movie_crew)) {
            foreach ($movie_crew as $movie_id) {
                $movie_data = streamit_get_movie((int)$movie_id);
                if (!is_wp_error($movie_data) && !empty($movie_data)) {
                    $response[] = $this->streamit_person_work_history($movie_data,  $cast_id, '_crew');
                }
            }
        }
        if (!empty($tv_show_cast)) {
            foreach ($tv_show_cast as $tvshow_id) {
                $tvshow_data = streamit_get_tvshow((int)$tvshow_id);
                if (!is_wp_error($tvshow_data) && !empty($tvshow_data)) {
                    $response[] = $this->streamit_person_work_history($tvshow_data, $cast_id, '_cast');
                }
            }
        }
        if (!empty($tv_show_crew)) {
            foreach ($tv_show_cast as $tvshow_id) {
                $tvshow_data = streamit_get_tvshow((int)$tvshow_id);
                if (!is_wp_error($tvshow_data) && !empty($tvshow_data)) {
                    $response[] = $this->streamit_person_work_history($tvshow_data, $cast_id, '_crew');
                }
            }
        }

        if (count($response) > 0)
            return st_comman_custom_response([
                'status'    => true,
                'message'   => esc_html__('Details.', 'streamit-api'),
                'data'      =>  $response
            ]);

        return st_comman_custom_response([
            "status"    => true,
            "message"   => esc_html__('No data found.', 'streamit-api'),
            "data"      =>  []
        ]);
    }

    public function streamit_person_work_history($post_data, $person_id, $meta_key)
    {
        $thubnail_image = $post_data->get_meta('thumbnail_id');
        $image = wp_get_attachment_image_src($thubnail_image, [300, 300]);
        $shareurl = function_exists('streamit_get_permalink') ? streamit_get_permalink($post_data->get_post_type(), $post_data->get_post_name()) : '';

        $character_name = st_cast_details($post_data->get_ID(), $post_data->get_post_type(),  $meta_key, $person_id);
        if (is_array($character_name)) {
            $character_name = implode(', ', $character_name); // Convert array to string
        }
        $temp = [
            'id'                => $post_data->get_ID(),
            'title'             => $post_data->get_post_title(),
            'image'             => !empty($image) ? $image[0] : null,
            'post_type'         => $post_data->get_post_type(),
            'character_name'    => !empty($character_name) ? $character_name : '',
            'share_url'         => $shareurl
        ];

        if ($post_data->get_post_type()  == 'tvshow') {
            $tv_show_season = $post_data->get_meta('_seasons');
            $temp['total_seasons'] = 0;
            $year = null;
            if (!empty($tv_show_season)) {
                $temp['total_seasons'] = count($tv_show_season);
            }
            $temp['release_year'] = $year;
        } else {
            $temp['release_year']     = $post_data->get_meta('_movie_release_date');
            $temp['run_time']         = $post_data->get_meta('_movie_run_time');
        }

        return $temp;
    }
}
