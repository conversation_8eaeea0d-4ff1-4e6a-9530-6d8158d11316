<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_notification_Route_Callback
{

    /**
     * Get Notification List.
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function notifications_list(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $parameters = $request->get_params();
        $user_id = $data['user_id'];

        $user = get_userdata($user_id);
        $user_registered = strtotime($user->user_registered);

        $action = ($parameters['action'] == "read") ? 1 : 0;
        $args = ['is_seen' => $action, 'is_current_user' => $user_id];
        $notification_list = function_exists('streamit_get_notifications') ? streamit_get_notifications($args)->results : [];
        if (empty($notification_list) || !is_array($notification_list)) {
            return st_comman_custom_response(array(
                'status'   => true,
                'message'  => esc_html__('Notification not found.', 'streamit-api'),
                'data'     => []
            ));
        }
        $is_pmp_active = class_exists('PMPro_Membership_Level');
        $list = [];
        foreach ($notification_list as $notification) {
            $notification_content = json_decode($notification->get_content());
            $notification_time = strtotime($notification_content->current_date);

            if ($notification_time < $user_registered) {
                continue;
            }

            $action = $notification->get_action();

            $image_id = absint($notification_content->thumbnail_id);

            $notification_id = $notification->get_id();

            $image_url = isset($image_id) && !empty($image_id) ? wp_get_attachment_image_url($image_id) : st_app_get_default_image();
            $formated_details = streamit_format_notification_message($action, $notification_content);
            $message = esc_html($formated_details['message']);
            $st_data = sanitize_text_field(json_decode($notification->get_content())->current_date);
            $meta_message = sprintf(_x('%s ago', 'time difference', 'streamit-api'), human_time_diff(strtotime($st_data), current_time('timestamp')));

            if ($action === 'new_release') {
                $post_type = $notification_content->post_type;
                $id = $notification_content->post_id;
            } elseif (in_array($action , ['pmp_plan_purchase' ,'pmp_new_plan'])) {
                if (!$is_pmp_active) continue;
                $post_type = '';
                $id = $notification_content->plan_id;
            }

            $list[] = [
                'notification_id' => $notification_id,
                'image_url'       => $image_url,
                'message'         => $message,
                'action'          => $action,
                'meta_message'    => $meta_message,
                'post_type'       => $post_type,
                'id'              => $id
            ];
        }

        return st_comman_custom_response(array(
            'status'   => true,
            'message'  => esc_html__('Notification List.', 'streamit-api'),
            'data'     => $list
        ));
    }

    /**
     * Change Notification Action
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function change_action(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $user_id = $data['user_id'];
        $parameters = $request->get_params();

        $action = ($parameters['action'] == "read") ? 1 : 0;
        $notification_id = $parameters['notification_id'];

        $update = streamit_update_notification_seen_status($user_id, $notification_id, $action);
        if ($update) {
            return st_comman_custom_response(array(
                'status'   => true,
                'message'  => ( $action == '1' ) ? esc_html__('Read notification.', 'streamit-api') : esc_html__('Unread notification', 'streamit-api')
            ));
        }

        return st_comman_custom_response(array(
            'status'   => true,
            'message'  => esc_html__('somthing went wrong.', 'streamit-api')
        ));
    }

    /**
     * Get Notification count
     *
     * @param WP_REST_Request $request The REST request.
     * @return WP_REST_Response
     */
    public function notification_count(WP_REST_Request $request)
    {
        $data = st_token_validation($request);
        if (!$data['status'])
            return st_comman_custom_response($data, $data['status_code']);

        $current_user_id = $data['user_id'];
        
        $user = get_userdata($current_user_id);
        $user_registered = strtotime($user->user_registered);

        $args = [
            'is_current_user' => $current_user_id,
            'is_seen' => 0,
            'per_page'=> -1
        ];
        $notifications = function_exists('streamit_get_notifications') ? streamit_get_notifications($args)->results : [];
        
        $filtered_count = 0;
        if (!empty($notifications) && is_array($notifications)) {
            foreach ($notifications as $notification) {
                $notification_content = json_decode($notification->get_content());
                $notification_time = strtotime($notification_content->current_date);
                
                if ($notification_time >= $user_registered) {
                    $filtered_count++;
                }
            }
        }
        
        return st_comman_custom_response([
            "status" => true,
            "message" => esc_html__("Notification Count", 'streamit-api'),
            "data" => [
                "total_notification_count" => $filtered_count,
            ]
        ]);
    }
}
