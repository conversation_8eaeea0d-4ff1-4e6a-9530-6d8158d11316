<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class St_notification_Route_Controller
{
    public $module = 'notification';
    public $name_space;

    public function __construct()
    {
        $this->name_space = STREAMIT_API_NAMESPACE;

        // Register REST routes
        add_action('rest_api_init', [$this, 'register_notification_routes']);
    }

    public function register_notification_routes():void
    {
        $callback = new St_notification_Route_Callback();

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/list/(?P<action>read|unread)',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callback, 'notifications_list'],
                'permission_callback' => '__return_true'
            )
        );


        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/(?P<action>read|unread)/(?P<notification_id>\d+)',
            array(
                'methods'             => WP_REST_Server::EDITABLE,
                'callback'            => [$callback, 'change_action'],
                'permission_callback' => '__return_true'
            )
        );

        register_rest_route(
            $this->name_space . '/api/v1/' . $this->module,
            '/count',
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$callback, 'notification_count'],
                'permission_callback' => '__return_true'
            )
        );


    }
}

new St_notification_Route_Controller();