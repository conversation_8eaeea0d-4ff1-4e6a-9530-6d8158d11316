<?php

use Firebase\JWT\JWT;

/**
 * Retrieve Firebase configuration from app settings.
 *
 * @return array|WP_Error The Firebase configuration array or WP_Error if invalid configuration.
 */
function sa_get_firebase_configuration()
{
    global $st_app_settings;
    // Ensure the Firebase configuration exists
    if (empty($st_app_settings['st_firebase'])) {
        return new WP_Error('firebase_config_error', 'Firebase configuration is missing from app settings.');
    }

    $firebase_configuration = $st_app_settings['st_firebase'];

    // Validate the necessary fields exist in the configuration
    $required_keys = ['client_email', 'private_key', 'project_id', 'app_name'];
    foreach ($required_keys as $key) {
        if (empty($firebase_configuration[$key])) {
            return new WP_Error('firebase_config_error', "Missing required field in Firebase configuration: {$key}");
        }
    }

    // Return the configuration as an array with sanitized values
    return [
        'client_email' => sanitize_email($firebase_configuration['client_email']),
        'private_key'  => $firebase_configuration['private_key'],
        'project_id'   => sanitize_text_field($firebase_configuration['project_id']),
        'app_name'     => sanitize_text_field($firebase_configuration['app_name']),
    ];
}

function st_format_pem_key($pem_string)
{
    // Replace escaped newline characters with actual newlines
    $pem_string = str_replace(['\\n', '\r\n', '\n'], "\n", $pem_string);

    // Trim any extra whitespace or quotes
    $pem_string = trim($pem_string, "\"' \n");

    // Check if the key contains the header and footer
    $hasHeader = strpos($pem_string, "-----BEGIN PRIVATE KEY-----") !== false;
    $hasFooter = strpos($pem_string, "-----END PRIVATE KEY-----") !== false;

    if ($hasHeader && $hasFooter) {
        // Extract the content between header and footer
        $start = strpos($pem_string, "-----BEGIN PRIVATE KEY-----");
        $end = strpos($pem_string, "-----END PRIVATE KEY-----");

        // Calculate the length of the header/footer block
        $header = "-----BEGIN PRIVATE KEY-----";
        $footer = "-----END PRIVATE KEY-----";

        // Remove header and footer from the key to get only the base64 content
        $content = trim(str_replace([$header, $footer], '', $pem_string));

        // Remove any whitespace or line breaks already in the content
        $content = preg_replace('/\s+/', '', $content);

        // Re-wrap the key at 64 characters per line
        $wrapped_content = wordwrap($content, 64, "\n", true);

        // Reassemble the key with proper header and footer
        $pem_string = $header . "\n" . $wrapped_content . "\n" . $footer;
    } else {
        // If missing header/footer, add them and wordwrap the entire key
        $pem_string = "-----BEGIN PRIVATE KEY-----\n" . wordwrap($pem_string, 64, "\n", true) . "\n-----END PRIVATE KEY-----";
    }

    return $pem_string;
}



add_action('streamit_after_notification_insert', 'streamit_send_push_notification', 10, 2);
function streamit_send_push_notification($notification_id, $new_release_data)
{
    $action = $new_release_data['action'];

    // Decode JSON content into an object
    $content = json_decode($new_release_data['content']);
    if ($action === 'new_release') {
        // Extract values from the content object
        $post_type  = $content->post_type;
        $post_title = $content->post_title;
        $post_name  = $content->post_name;
        $post_id    = $content->post_id;
        $image      = !empty($content->thumbnail_id)
            ? wp_get_attachment_url($content->thumbnail_id, 'full')
            : st_app_get_default_image();
        // Construct the URL for the content (adjust this as needed)
        $url = streamit_get_permalink($post_type, $post_name);
        $body_message = streamit_format_notification_message('new_release', $content);
        // Prepare the push notification payload using the new release data
        $payload['notification'] = [
            'title' => $post_title,
            'body'  => $body_message['message'],
            'image' => $image,
        ];
        $payload['data'] = [
            'notification_type' => 'content_added',
            'post_type'  => $post_type,
            'id'    => $post_id,
            'url'   => $url,
           'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
        ];

        // Send the notification using the improved FCM function
        streamit_send_fcm_notification($payload);
    } elseif ($action === 'pmp_new_plan') {
        // Extract values from the content object
        $post_title = $content->plan_name;
        $plan_id    = $content->plan_id;
        // Construct the URL for the content (adjust this as needed)
        $url = function_exists('pmpro_url') ? pmpro_url("checkout") . "?level=" . $plan_id : '';
        $body_message = streamit_format_notification_message('pmp_new_plan', $content);
        // Prepare the push notification payload using the new release data

        $payload['notification'] = [
            'title' => $post_title,
            'body'  => $body_message['message'],
        ];
        $payload['data'] = [
            'notification_type' => 'subscription_plan_added',
            'id'    => $plan_id,
            'url'   => $url,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
        ];
        // Send the notification using the improved FCM function
        streamit_send_fcm_notification($payload);
    } elseif ($action === 'pmp_plan_purchase') {
        $current_user_id  = $new_release_data['user_id'];

        // Extract values from the content object
        $post_title = $content->plan_name;
        $plan_id    = $content->plan_id;
        // Construct the URL for the content (adjust this as needed)
        $url = function_exists('pmpro_url') ? pmpro_url("checkout") . "?level=" . $plan_id : '';
        $body_message = streamit_format_notification_message('pmp_plan_purchase', $content);
        // Prepare the push notification payload using the new release data

        $payload['notification'] = [
            'title' => $post_title,
            'body'  => $body_message['message'],
            "click_action" => "FLUTTER_NOTIFICATION_CLICK",
        ];
        $payload['data'] = [
            'notification_type' => 'subscription_purchased',
            'id'    => $plan_id,
            'url'   => $url,
            "click_action" => "FLUTTER_NOTIFICATION_CLICK",
        ];

        $topic = "user_{$current_user_id}";
        // Send the notification using the improved FCM function
        streamit_send_fcm_notification($payload, $topic);
    }
}

function streamit_send_fcm_notification($payload, $topic = null)
{
    // Retrieve Firebase configuration from your app settings
    $firebase_config = sa_get_firebase_configuration();
    if (is_wp_error($firebase_config)) {
        return;
    }

    // Construct the FCM HTTP v1 API URL using your project ID
    $firebase_url = 'https://fcm.googleapis.com/v1/projects/' . $firebase_config['project_id'] . '/messages:send';

    // Generate an OAuth2 access token using the service account credentials
    $access_token = st_generate_firebase_oauth2_token($firebase_config['client_email'], $firebase_config['private_key']);

    if (!$access_token) {
        return;
    }

    if ($topic) {
        $target = ["topic" => $topic];
    } else {
        $target = ["topic" => $firebase_config['app_name']];
    }

    // Build the FCM message payload, including notification and custom data fields
    $data = apply_filters('streamit_send_fcm_notification_data', [
        'message' => array_merge(
            $target,
            [
                'notification' => $payload['notification'],
                'data' => $payload['data'],
                "android" => [
                    "notification" => [
                        "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'category' => 'NEW_MESSAGE_CATEGORY',
                        ],
                    ],
                ],
            ]
        )
    ], $payload, $topic);

    // Set up the HTTP headers with the OAuth2 Bearer token
    $headers = [
        'Authorization: Bearer ' . $access_token,
        'Content-Type: application/json',
    ];


    // Initialize cURL session for the FCM endpoint
    $ch = curl_init($firebase_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

    // Optional: Enable verbose mode for debugging
    // curl_setopt($ch, CURLOPT_VERBOSE, true);

    $response = curl_exec($ch);

    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $response;
}

function st_generate_firebase_oauth2_token($clientEmail, $privateKey)
{
    $payload = [
        'iss'   => $clientEmail,
        'aud'   => 'https://oauth2.googleapis.com/token',
        'exp'   => time() + 3600,  // Token validity period (1 hour)
        'iat'   => time(),
        'scope' => 'https://www.googleapis.com/auth/cloud-platform',
    ];
    $jwt = JWT::encode($payload, st_format_pem_key($privateKey), 'RS256');
    // Request an access token using the JWT assertion
    $tokenResponse = file_get_contents('https://oauth2.googleapis.com/token', false, stream_context_create([
        'http' => [
            'method'  => 'POST',
            'header'  => 'Content-Type: application/x-www-form-urlencoded',
            'content' => http_build_query([
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion'  => $jwt,
            ]),
        ],
    ]));

    $tokenData = json_decode($tokenResponse, true);
    return $tokenData['access_token'] ?? null;
}
