<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}


function st_movie_content_format($data, $is_short = true)
{
    if (is_wp_error($data) || empty($data) || $data === null)
        return [];

    $post_id            = $data->get_ID();
    $post_type          = $data->get_post_type();
    $thumbnai_image_id  = $data->get_meta('thumbnail_id');
    $portrait_image_id  = $data->get_meta('_portrait_thumbmail');
    $image              = !empty($thumbnai_image_id) ? wp_get_attachment_image_url($thumbnai_image_id, "full") : '';
    $portrait_image     = !empty($portrait_image_id) ? wp_get_attachment_image_url($portrait_image_id, "full") : '';

    $user_id            = get_current_user_id();
    $trailer_link       = !empty($data->get_meta('_name_trailer_link')) ? $data->get_meta('_name_trailer_link') : null;

    if ($is_short) {
        return apply_filters('st_movie_short_content_format',  [
            'id'               => $post_id,
            'title'            => $data->get_post_title(),
            'image'            => !empty($image) ? $image : null,
            'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
            'run_time'         => !empty($data->get_meta('_movie_run_time')) ? $data->get_meta('_movie_run_time') : '',
            'post_type'        =>  $post_type,
            'stream_type'      => !empty($data->get_meta('_movie_choice')) ? $data->get_meta('_movie_choice') : '',
            'trailer_link'     => $trailer_link,
            'trailer_link_type' => st_check_video_url_type($trailer_link),
        ], $data);
    }

    $movie_like_count_args = [
        'post_id' => $post_id,
        'post_type' => $post_type
    ];

    $avg_rating     = !empty($data->get_meta('_movie_run_time')) ? $data->get_meta('_movie_run_time') : '';
    $temp = [
        'id'                    => $post_id,
        'title'                 => $data->get_post_title(),
        'image'                 => !empty($image) ? $image : null,
        'portrait_image'        => !empty($portrait_image) ? $portrait_image : null,
        'post_type'             => $post_type,
        'description'           => !empty($data->get_post_content()) ? $data->get_post_content() : '',
        'excerpt'               => !empty($data->get_post_excerpt()) ? wp_strip_all_tags($data->get_post_excerpt()) : '',
        'share_url'             => function_exists('streamit_get_permalink') ? streamit_get_permalink($post_type, $data->get_post_name()) : '',
        'is_comment_open'       => true,
        'no_of_comments'        => $data->get_comment_count(),
        'trailer_link'          => $trailer_link,
        'trailer_link_type'     => st_check_video_url_type($trailer_link),
        'is_liked'              => function_exists("streamit_is_like") ? streamit_is_like($post_id, 'movie', $user_id) : false,
        'likes'                 => function_exists("streamit_get_like_count") ? streamit_get_like_count($movie_like_count_args) : 0,
        'is_watchlist'          => function_exists("streamit_is_watchlist")   ? streamit_is_watchlist($user_id, $post_id, 'movie') : false,
        'avg_rating'            => ($avg_rating === null ? 0 : $avg_rating),
        'imdb_rating'           =>  $data->get_meta('name_custom_imdb_rating') ? floatval($data->get_meta('name_custom_imdb_rating')) / 2  : 0,
        'embed_content'         => !empty($data->get_meta('_movie_embed_content')) ? $data->get_meta('_movie_embed_content') : '',
        'movie_choice'          => !empty($data->get_meta('_movie_choice')) ? $data->get_meta('_movie_choice') : '',
        'sources'               => !empty($data->get_meta('_source')) ? $data->get_meta('_source') : [],
        'url_link'              => !empty($data->get_meta('_movie_url_link')) ? $data->get_meta('_movie_url_link') : '',
        'genre'                 => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'movie_genre') : null,
        'tag'                   => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'movie_tag') : null,
        'run_time'              =>  !empty($data->get_meta('_movie_run_time')) ? $data->get_meta('_movie_run_time') : '',
        'censor_rating'         =>  !empty($data->get_meta('_movie_censor_rating')) ? $data->get_meta('_movie_censor_rating') : '',
        'release_date'          =>  !empty($data->get_meta('_movie_release_date')) ? $data->get_meta('_movie_release_date') : '',
        'views'                 =>  !empty($data->get_meta('post_views_count')) ? (int) $data->get_meta('post_views_count') : 0,
        'publish_date'          => $data->get_post_date(),
        'publish_date_gmt'      => $data->get_post_date_gmt(),
        'casts'                 => st_cast_details($post_id, 'movie', '_cast'),
        'crews'                 => st_cast_details($post_id, 'movie', '_crew'),
        'is_password_protected' => false,
        'total_review'          => $data->get_meta('streamit_comment_count'),
        'is_upcoming'           => !empty($data->get_meta('name_upcoming')) && ( $data->get_meta('name_upcoming') == '1') ? true : false,
    ];

    $movie_file         =  !empty($data->get_meta('_movie_attachment_id')) ? wp_get_attachment_url($data->get_meta('_movie_attachment_id')) : null;
    $temp['movie_file'] = $movie_file ? $movie_file : null;

    $logo               = !empty($data->get_meta('name_logo')) ? wp_get_attachment_url($data->get_meta('name_logo'), [300, 300]) : null;
    $temp['logo']       = $logo[0] ?? null;

    $post_plans         = !empty($data->get_meta('_pmp_level')) ? $data->get_meta('_pmp_level') : [];
    $plan_list          = restrictedPlanList($post_plans, $user_id);
    $temp               = array_merge($temp, $plan_list);

    //rate and review 
    $temp['user_comment'] = $user_comment_id = $other_comments = [];
    if (!empty($user_id)) :
        $user_comment = streamit_user_current_post_comment($post_id, $post_type, $user_id);
        if ($user_comment !== null) :
            $user_comment_id = $user_comment->get_id();
            $temp['user_comment'] = st_rate_formate($user_comment);
        endif;
    endif;

    $comments = streamit_get_comments(['comment_post_ID' => $post_id, 'post_type' => [$post_type], 'per_page' => 3, 'exclude' => array($user_comment_id)]);
    foreach ($comments->results as $comment) :
        $other_comments[] = st_rate_formate($comment);
    endforeach;
    $temp['other_comments'] = $other_comments;

    return apply_filters('st_movie_content_format', $temp, $data);
}


function st_video_content_format($data, $is_short = true)
{
    if (is_wp_error($data) || empty($data) || $data === null)
        return [];

    $post_id            = $data->get_ID();
    $post_type          = $data->get_post_type();
    $thumbnai_image_id  = $data->get_meta('thumbnail_id');
    $portrait_image_id  = $data->get_meta('_portrait_thumbmail');
    $image              = !empty($thumbnai_image_id) ? wp_get_attachment_image_url($thumbnai_image_id, "full") : '';
    $portrait_image     = !empty($portrait_image_id) ? wp_get_attachment_image_url($portrait_image_id, "full") : '';
    $user_id            = get_current_user_id();
    $trailer_link       = !empty($data->get_meta('_name_trailer_img')) ? $data->get_meta('_name_trailer_img') : null;

    if ($is_short) {
        return apply_filters('st_video_short_content_format',  [
            'id'            => $post_id,
            'title'         => $data->get_post_title(),
            'image'            => !empty($image) ? $image : null,
            'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
            'run_time'      => !empty($data->get_meta('_video_run_time')) ? $data->get_meta('_video_run_time') : '',
            'post_type'     => $post_type,
            'stream_type'   => !empty($data->get_meta('_video_choice')) ? $data->get_meta('_video_choice') : '',
            'trailer_link'  => $trailer_link,
            'trailer_link_type' => st_check_video_url_type($trailer_link),
        ], $data);
    }

    $video_like_count_args = [
        'post_id' => $post_id,
        'post_type' => $post_type
    ];

    $avg_rating     = isset($post_meta['_average_rating']) ? $post_meta['_average_rating'][0] : '';
    $temp = [
        'id'                    => $post_id,
        'title'                 => $data->get_post_title(),
        'image'            => !empty($image) ? $image : null,
        'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
        'post_type'             => $post_type,
        'description'           => !empty($data->get_post_content()) ? $data->get_post_content() : '',
        'excerpt'               => !empty($data->get_post_excerpt()) ? wp_strip_all_tags($data->get_post_excerpt()) : '',
        'share_url'             => function_exists('streamit_get_permalink') ? streamit_get_permalink($post_type, $data->get_post_name()) : '',
        'is_comment_open'       => true,
        'no_of_comments'        => $data->get_comment_count(),
        'trailer_link'          => $trailer_link,
        'trailer_link_type'     => st_check_video_url_type($trailer_link),
        'is_liked'              => function_exists("streamit_get_like_count") ? streamit_is_like($post_id, 'video', $user_id) : false,
        'likes'                 => function_exists("streamit_get_like_count") ? streamit_get_like_count($video_like_count_args) : 0,
        'is_watchlist'          => function_exists("streamit_is_watchlist")   ? streamit_is_watchlist($user_id, $post_id, 'video') : false,
        'avg_rating'            => ($avg_rating === null ? 0 : $avg_rating),
        'imdb_rating'           => !empty($data->get_meta('name_custom_imdb_rating')) ? floatval($data->get_meta('name_custom_imdb_rating')) / 2  : 0,
        'embed_content'         => !empty($data->get_meta('_video_embed_content')) ? $data->get_meta('_video_embed_content') : '',
        'video_choice'          => !empty($data->get_meta('_video_choice')) ? $data->get_meta('_video_choice') : '',
        'url_link'              => !empty($data->get_meta('_video_url_link')) ? $data->get_meta('_video_url_link') : '',
        'genre'                 => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'video_category') : null,
        'tag'                   => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'video_tag') : null,
        'run_time'              => !empty($data->get_meta('_video_run_time')) ? $data->get_meta('_video_run_time') : '',
        'views'                 => !empty($data->get_meta('post_views_count')) ? (int) $data->get_meta('post_views_count') : 0,
        'publish_date'          => $data->get_post_date(),
        'publish_date_gmt'      => $data->get_post_date_gmt(),
        'casts'                 => st_cast_details($post_id, 'video', '_cast'),
        'crews'                 => st_cast_details($post_id, 'video', '_crew'),
        'comments'              => '',
        'is_password_protected' => false,
        'is_upcoming'           => !empty($data->get_meta('name_upcoming')) && ( $data->get_meta('name_upcoming') == '1') ? true : false,
    ];

    $video_file         = !empty($data->get_meta('_video_attachment_id')) ? wp_get_attachment_url($data->get_meta('_video_attachment_id')) : null;
    $temp['video_file'] = $video_file ? $video_file : null;

    $logo               = !empty($data->get_meta('name_logo')) ?  wp_get_attachment_url($data->get_meta('name_logo'), [300, 300]) : null;
    $temp['logo']       = $logo[0] ?? null;

    $post_plans = !empty($data->get_meta('_pmp_level')) ? $data->get_meta('_pmp_level') : [];
    $plan_list          = restrictedPlanList($post_plans, $user_id);
    $temp = array_merge($temp, $plan_list);

    return apply_filters('st_video_content_format', $temp, $data);
}


function st_tvshow_content_format($data, $is_short = true)
{
    if (is_wp_error($data) || empty($data) || $data === null) {
        return [];
    }

    $post_id            = $data->get_ID();
    $post_type          = 'tv_show';
    $thumbnai_image_id  = $data->get_meta('thumbnail_id');
    $portrait_image_id  = $data->get_meta('_portrait_thumbmail');
    $image              = !empty($thumbnai_image_id) ? wp_get_attachment_image_url($thumbnai_image_id, "full") : '';
    $portrait_image     = !empty($portrait_image_id) ? wp_get_attachment_image_url($portrait_image_id, "full") : '';
    $user_id            = get_current_user_id();
    $trailer_link   = !empty($data->get_meta('name_trailer_link')) ? $data->get_meta('name_trailer_link') : null;

    if ($is_short) {
        return apply_filters('st_tvshow_short_content_format',  [
            'id'            => $post_id,
            'title'         => $data->get_post_title(),
            'image'            => !empty($image) ? $image : null,
            'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
            'post_type'     => $post_type,
        ], $data);
    }

    $tvshow_like_count_args = [
        'post_id' => $post_id,
        'post_type' => $post_type
    ];

    $avg_rating    = !empty($data->get_meta('_average_rating')) ? $data->get_meta('_average_rating') : '';
    $temp = [
        'id'                    => $post_id,
        'title'                 => $data->get_post_title(),
        'image'            => !empty($image) ? $image : null,
        'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
        'post_type'             => $post_type,
        'description'           => !empty($data->get_post_content()) ? $data->get_post_content() : '',
        'excerpt'               => !empty($data->get_post_excerpt()) ? wp_strip_all_tags($data->get_post_excerpt()) : '',
        'share_url'             => function_exists('streamit_get_permalink') ? streamit_get_permalink($post_type, $data->get_post_name()) : '',
        'is_comment_open'       => true,
        'no_of_comments'        => $data->get_comment_count(),
        'trailer_link'          => $trailer_link,
        'trailer_link_type'     => st_check_video_url_type($trailer_link),
        'is_liked'              => function_exists("streamit_is_like") ? streamit_is_like($post_id, 'tvshow', $user_id) : false,
        'likes'                 => function_exists("streamit_get_like_count") ? streamit_get_like_count($tvshow_like_count_args) : 0,
        'is_watchlist'          => function_exists("streamit_is_watchlist")   ? streamit_is_watchlist($user_id, $post_id, 'tvshow') : false,
        'avg_rating'            => ($avg_rating === null ? 0 : $avg_rating),
        'imdb_rating'           => !empty($data->get_meta('name_custom_imdb_rating')) ? floatval($data->get_meta('name_custom_imdb_rating')) / 2  : 0,
        'genre'                 => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'tvshow_genre') : null,
        'tag'                   => function_exists('streamit_get_term_names') ? streamit_get_term_names($post_id, 'tvshow_tag') : null,
        'publish_date'          => $data->get_post_date(),
        'publish_date_gmt'      => $data->get_post_date_gmt(),
        'casts'                 => st_cast_details($post_id, 'tvshow', '_cast'),
        'crews'                 => st_cast_details($post_id, 'tvshow', '_crew'),
        'comments'              => '',
        'is_password_protected' => false,
        'is_upcoming'           => !empty($data->get_meta('name_upcoming')) && ( $data->get_meta('name_upcoming') == '1') ? true : false,
    ];

    $seasons            = !empty($data->get_meta('_seasons')) ? $data->get_meta('_seasons') : [];
    $total_seasons      = count($seasons);
    if ($total_seasons > 0) {
        $seasons_data = array();
        foreach ($seasons as $season) {
            $seasons_data[] = array(
                'id'    => $season['id'],
                'name'  => $season['name']
            );
        }
        $temp['seasons'] = ["count" => $total_seasons, "data" => $seasons_data];
    } else {
        $temp['seasons'] = (object) [];
    }

    $logo           = !empty($data->get_meta('name_logo')) ? wp_get_attachment_url(!empty($data->get_meta('name_logo')), [300, 300]) : null;
    $temp['logo']   = $logo[0] ?? null;

    $post_plans = !empty($data->get_meta('_pmp_level')) ? $data->get_meta('_pmp_level') : [];
    $plan_list          = restrictedPlanList($post_plans, $user_id);
    $temp = array_merge($temp, $plan_list);

    return apply_filters('st_tvshow_content_format', $temp, $data);
}

function st_episode_content_format($data, $is_short = true)
{
    if (is_wp_error($data) || empty($data) || $data === null) {
        return [];
    }

    $post_id            = $data->get_ID();
    $post_type          = $data->get_post_type();
    $thumbnai_image_id  = $data->get_meta('thumbnail_id');
    $portrait_image_id  = $data->get_meta('_portrait_thumbmail');
    $image              = !empty($thumbnai_image_id) ? wp_get_attachment_image_url($thumbnai_image_id, "full") : '';
    $portrait_image     = !empty($portrait_image_id) ? wp_get_attachment_image_url($portrait_image_id, "full") : '';
    $user_id            = get_current_user_id();


    if ($is_short) {
        return apply_filters('st_episode_short_content_format',  [
            'id'            => $post_id,
            'title'         => $data->get_post_title(),
            'image'         => !empty($image) ? $image : null,
            'post_type'     => $post_type,
            'stream_type'       => !empty($data->get_meta('_portrait_thumbmail')) ? $data->get_meta('_portrait_thumbmail') : '',
            'run_time'          =>  !empty($data->get_meta('_episode_run_time')) ? $data->get_meta('_episode_run_time') : '',
            'release_date'      => date_i18n(get_option('date_format'), $data->get_meta('_episode_release_date'))
        ], $data);
    }

    $trailer_link = !empty($data->get_meta('name_trailer_link'))  ? $data->get_meta('name_trailer_link') : null;
    $temp = [
        'id'                    => $post_id,
        'title'                 => $data->get_post_title(),
        'image'            => !empty($image) ? $image : null,
        'portrait_image'   => !empty($portrait_image) ? $portrait_image : null,
        'post_type'             => $post_type,
        'description'           => !empty($data->get_post_content()) ? $data->get_post_content() : '',
        'excerpt'               => !empty($data->get_post_excerpt()) ? wp_strip_all_tags($data->get_post_excerpt()) : '',
        'share_url'             => function_exists('streamit_get_permalink') ? streamit_get_permalink($post_type, $data->get_post_name()) : '',
        'is_comment_open'       => true,
        'no_of_comments'        => $data->get_comment_count(),
        'trailer_link'          => $trailer_link,
        'trailer_link_type'     => st_check_video_url_type($trailer_link),
        'is_liked'              => function_exists("streamit_is_like") ? streamit_is_like($post_id, 'episode', $user_id) : false,
        'likes'                 => function_exists("streamit_get_like_count") ? streamit_get_like_count($post_id, 'episode') : 0,
        'is_watchlist'          => function_exists("streamit_is_watchlist")   ? streamit_is_watchlist($user_id, $post_id, 'episode') : false,
        'imdb_rating'           => !empty($data->get_meta('name_custom_imdb_rating')) ? floatval($data->get_meta('name_custom_imdb_rating')) / 2  : 0,
        'tv_show_id'            => !empty($data->get_meta('tvshow_id')) ? $data->get_meta('tvshow_id') : '',
        'embed_content'         => !empty($data->get_meta('_episode_embed_content')) ? $data->get_meta('_episode_embed_content') : '',
        'episode_choice'        => !empty($data->get_meta('_episode_choice')) ? $data->get_meta('_episode_choice') : '',
        'sources'               => streamit_source_list($data->get_meta('_sources')),
        'url_link'              => !empty($data->get_meta('_episode_url_link')) ? $data->get_meta('_episode_url_link') : '',
        'run_time'              => !empty($data->get_meta('_episode_run_time')) ? $data->get_meta('_episode_run_time') : '',
        'release_date'          => date_i18n(get_option('date_format'), $data->get_meta('_episode_run_time')),
        'comments'              => '',
        'is_password_protected' => false
    ];

    $episode_file  = !empty($data->get_meta('_episode_attachment_id')) ? wp_get_attachment_url($data->get_meta('_episode_attachment_id')) : '';
    $temp['episode_file']   = $episode_file ? $episode_file : null;

    $post_plans = !empty($data->get_meta('_pmp_level')) ? $data->get_meta('_pmp_level') : [];
    $plan_list          = restrictedPlanList($post_plans, $user_id);
    $temp = array_merge($temp, $plan_list);

    return apply_filters('st_episode_content_format', $temp, $data);
}

/**
 * Determines the type of video URL.
 *
 * Checks whether the given video URL is from YouTube, Vimeo, or any other platform.
 *
 * @param string $url The video URL to check.
 * @return string The video platform type ('YouTube', 'Vimeo', or 'other').
 */
function st_check_video_url_type($url)
{
    // Return an empty string for null or empty URLs.
    if (empty($url)) {
        return '';
    }

    // Normalize the URL for consistent processing.
    $url = trim(esc_url_raw($url));

    // Define patterns for supported video platforms.
    $platforms = array(
        'YouTube' => array(
            'youtube.com',
            'youtu.be',
        ),
        'Vimeo' => array(
            'vimeo.com',
        ),
    );

    /**
     * Filters the platform patterns for detecting video URL types.
     *
     * @param array $platforms Associative array where the key is the platform name, and the value is an array of patterns.
     * @param string $url      The video URL being checked.
     */
    $platforms = apply_filters('st_video_platform_patterns', $platforms, $url);

    // Iterate through the platform patterns to identify the URL type.
    foreach ($platforms as $platform => $patterns) {
        foreach ($patterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                /**
                 * Filters the detected platform name.
                 *
                 * @param string $platform The detected platform name.
                 * @param string $url      The video URL being checked.
                 */
                return apply_filters('st_detected_video_platform', $platform, $url);
            }
        }
    }

    // If no match is found, return 'other'.
    return apply_filters('st_detected_video_platform', 'other', $url);
}



function st_cast_details($post_id, $post_type, $meta_key = '_cast', $cast_id = null)
{
    $post_meta = 'streamit_get_' . $post_type . '_meta';
    $cast_list = function_exists($post_meta) ? $post_meta($post_id, $meta_key) : [];

    // If the cast list is not empty, process the data.
    if (!empty($cast_list) && is_array($cast_list)) {
        // Initialize arrays for character names and cast data.
        $character = [];
        $cast_data = [];

        // Loop through the cast list.
        foreach ($cast_list as $cast) {
            // If a specific cast ID is provided, check for matches.
            if ($cast_id !== null) {
                if ($cast['id'] == $cast_id) {
                    $character[] = $cast['character'];
                }
            } else {
                $person_details = streamit_get_person((int)$cast['id']);
                $person_name = !empty($person_details) && !is_wp_error($person_details) ? $person_details->get_post_title() : "";
                $cast_image = wp_get_attachment_url(streamit_get_person_meta($cast['id'], 'thumbnail_id'), [300, 300]);
                $cast_data[] = [
                    'id' => $cast['id'],
                    'image' => !empty($cast_image) ? $cast_image : null,
                    'name' =>  $person_name,
                ];
            }
        }

        // Return character names if a specific cast ID is provided, otherwise return full cast data.
        return $cast_id !== null ? $character : $cast_data;
    }

    return []; // Return an empty array if no cast data is found.
}


function st_rate_formate($rate)
{
    $user_avatar = get_avatar_url($rate->get_user_id(), array('size' => 96));
    $user_avatar_new = get_user_meta($rate->get_user_id(), 'user_avatar', true);
    $user_image = !empty($user_avatar_new) ? esc_url($user_avatar_new) : $user_avatar;
    return apply_filters('st_rate_formate', [
        'user_image'    => $user_image,
        'user_name'     => $rate->get_comment_author(),
        'rate_content'  => $rate->get_comment_content(),
        'rate'          => (int) $rate->get_rating(),
        'date'          => $rate->get_comment_date(),
    ], $rate);
}
