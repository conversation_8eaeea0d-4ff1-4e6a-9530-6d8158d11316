<?php

add_filter("pmpro_rest_api_permissions", function ($permission) {
    return true;
});

/**
 * Get the limit login settings.
 *
 * @return mixed Limit login settings array or false if not found.
 */
function st_get_limit_login_settings()
{
    global $st_app_settings;

    return isset($st_app_settings['st_device_lmit']) ? $st_app_settings['st_device_lmit'] : false;
}

/**
 * Get the user membership history based on subscription ID.
 *
 * @param int $subscription_id The subscription ID of the user.
 * @return string|null The status of the user's membership or null if not found.
 */
function st_get_user_history($subscription_id)
{
    global $wpdb;

    // Ensure the subscription ID is an integer to prevent SQL injection.
    $subscription_id = intval($subscription_id);

    // Prepare the query to retrieve the status from the database.
    $query = $wpdb->prepare(
        "SELECT `status` FROM {$wpdb->pmpro_memberships_users} WHERE `id` = %d ORDER BY `id` DESC",
        $subscription_id
    );

    // Execute the query and return the result.
    return $wpdb->get_var($query);
}

/**
 * Get the current subscription plan details for a user.
 *
 * @param int $user_id The ID of the user to retrieve the subscription plan for.
 * @return object Subscription plan details as an object or empty array if the plugin is not active or no plan is found.
 */
function st_user_plans($user_id)
{
    // Ensure Paid Memberships Pro plugin is active.
    if (! is_plugin_active('paid-memberships-pro/paid-memberships-pro.php')) {
        return array();
    }

    // Get the user's membership level.
    $user_level = pmpro_getMembershipLevelsForUser($user_id, false)[0] ?? false;
    if (! $user_level) {
        return array();
    }


    // Get limit login settings.
    $limit_login_settings = st_get_limit_login_settings();

    // Prepare the user's current subscription plan data.
    $users_current_plan = array(
        'subscription_plan_id'               => $user_level->id,
        'subscription_id'                    => $user_level->subscription_id,
        'start_date'                         => (! empty($user_level->startdate)) ? ucfirst(date_i18n(get_option('date_format'), $user_level->startdate)) : '',
        'expiration_date'                    => (! empty($user_level->enddate)) ? ucfirst(date_i18n(get_option('date_format'), $user_level->enddate)) : '',
        'subscription_plan_name'             => esc_html($user_level->name),
        'initial_payment'                    => floatval($user_level->initial_payment),
        'billing_amount'                     => floatval($user_level->billing_amount),
        'google_in_app_purchase_identifier'  => get_pmpro_membership_level_meta($user_level->id, 'st_google_in_app_purchase_identifier', true),
        'apple_in_app_purchase_identifier'   => get_pmpro_membership_level_meta($user_level->id, 'st_apple_in_app_purchase_identifier', true),
        'status'                             => esc_html(st_get_user_history($user_level->subscription_id))
    );

    // Add login limits if they exist.
    if ($limit_login_settings) {
        $users_current_plan['default_login_limit']      = intval($limit_login_settings['default_limit']);
        $users_current_plan['current_plan_login_limit'] = intval($limit_login_settings[$user_level->id]);
    }

    // Return the plan details as an object.
    return (object) $users_current_plan;
}

function st_pmpro_getAllLevels($user_id = false, $include_hidden = false)
{
    global $pmpro_levels, $wpdb;

    // build query
    $sqlQuery = "SELECT * FROM $wpdb->pmpro_membership_levels ";
    if (!$include_hidden) {
        $sqlQuery .= ' WHERE allow_signups = 1 ORDER BY id';
    }

    // get levels from the DB
    $raw_levels     = $wpdb->get_results($sqlQuery);
    if (!$raw_levels || is_wp_error($raw_levels)) return [];

    $pmpro_levels       = array();
    $user_levels        = $user_id ? array_column(pmpro_getMembershipLevelsForUser($user_id, true), "ID") : false;
    $pmpro_checkout_url = pmpro_url("checkout");
    foreach ($raw_levels as $raw_level) {
        $raw_level->initial_payment     = pmpro_round_price($raw_level->initial_payment);
        $raw_level->is_initial          = !$user_levels || !in_array($raw_level->id, $user_levels);
        $raw_level->billing_amount      = pmpro_round_price($raw_level->billing_amount);
        $raw_level->trial_amount        = pmpro_round_price($raw_level->trial_amount);
        $raw_level->checkout_url        = add_query_arg("level", $raw_level->id, $pmpro_checkout_url);
        $raw_level->product_id          = st_get_product_by_level_id($raw_level->id);
        $raw_level->google_in_app_purchase_identifier = get_pmpro_membership_level_meta($raw_level->id, "st_google_in_app_purchase_identifier", true);
        $raw_level->apple_in_app_purchase_identifier = get_pmpro_membership_level_meta($raw_level->id, "st_apple_in_app_purchase_identifier", true);
        $pmpro_levels[$raw_level->id]   = $raw_level;
    }

    return $pmpro_levels;
}

function st_get_product_by_level_id($level_id)
{
    $args = [
        'post_type'         => 'product',
        'post_status'       => 'publish',
        'meta_key'          => '_membership_product_level',
        'meta_value'        => $level_id,
        'posts_per_page'    => 1
    ];
    $wc_query = get_posts($args);

    wp_reset_query();
    return $wc_query[0]->ID ?? 0;
}



function st_get_pmp_orders($results)
{
    $orders = [];

    foreach ($results  as $order) {
        $billing = [
            "name"      => $order->billing_name,
            "street"    => $order->billing_street,
            "city"      => $order->billing_city,
            "state"     => $order->billing_state,
            "zip"       => $order->billing_zip,
            "country"   => $order->billing_country,
            "phone"     => $order->billing_phone
        ];

        $membership_level = pmpro_getLevel($order->membership_id);

        $orders[] = [
            "id"                            => $order->id,
            "code"                          => $order->code,
            "user_id"                       => $order->user_id,
            "in_app_purchase_identifier"    => get_pmpro_membership_order_meta($order->id, "st_in_app_purchase_identifier", true),
            "membership_id"                 => $order->membership_id,
            "membership_name"               => $membership_level ? $membership_level->name : "",
            "billing"                       => $billing,
            "subtotal"                      => $order->subtotal,
            "tax"                           => $order->tax,
            "total"                         => $order->total,
            "payment_type"                  => $order->payment_type,
            "cardtype"                      => $order->cardtype,
            "accountnumber"                 => $order->accountnumber,
            "expirationmonth"               => $order->expirationmonth,
            "expirationyear"                => $order->expirationyear,
            "status"                        => $order->status,
            "gateway"                       => $order->gateway,
            "gateway_environment"           => $order->gateway_environment,
            "payment_transaction_id"        => $order->payment_transaction_id,
            "subscription_transaction_id"   => $order->subscription_transaction_id,
            "timestamp"                     => $order->timestamp,
            "affiliate_id"                  => $order->affiliate_id,
            "affiliate_subid"               => $order->affiliate_subid,
            "notes"                         => $order->notes,
            "checkout_id"                   => $order->checkout_id
        ];
    }
    return $orders;
}

function st_change_membership_level($level_id, $user_id, $args = [])
{
    global $wpdb;

    $pmpro_level = pmpro_getLevel($level_id);
    $args = wp_parse_args(
        $args,
        ["discount_code" => ""]
    );

    $startdate = current_time("mysql");
    $startdate = apply_filters("pmpro_checkout_start_date", $startdate, $user_id, $pmpro_level);

    if (!empty($pmpro_level->expiration_number)) {
        if ($pmpro_level->expiration_period == 'Hour') {
            $enddate =  date("Y-m-d H:i:s", strtotime("+ " . $pmpro_level->expiration_number . " " . $pmpro_level->expiration_period, current_time("timestamp")));
        } else {
            $enddate =  date("Y-m-d 23:59:59", strtotime("+ " . $pmpro_level->expiration_number . " " . $pmpro_level->expiration_period, current_time("timestamp")));
        }
    } else {
        $enddate = "NULL";
    }

    $enddate = apply_filters("pmpro_checkout_end_date", $enddate, $user_id, $pmpro_level, $startdate);

    if (!empty($args["discount_code"])) {
        $discount_code_id = $wpdb->get_var("SELECT id FROM $wpdb->pmpro_discount_codes WHERE code = '" . esc_sql($args["discount_code"]) . "' LIMIT 1");
    } else {
        $discount_code_id = "";
    }

    $custom_level = array(
        'user_id'         => $user_id,
        'membership_id'   => $pmpro_level->id,
        'code_id'         => $discount_code_id,
        'initial_payment' => pmpro_round_price($pmpro_level->initial_payment),
        'billing_amount'  => pmpro_round_price($pmpro_level->billing_amount),
        'cycle_number'    => $pmpro_level->cycle_number,
        'cycle_period'    => $pmpro_level->cycle_period,
        'billing_limit'   => $pmpro_level->billing_limit,
        'trial_amount'    => pmpro_round_price($pmpro_level->trial_amount),
        'trial_limit'     => $pmpro_level->trial_limit,
        'startdate'       => $startdate,
        'enddate'         => $enddate
    );

    return pmpro_changeMembershipLevel($custom_level, $user_id, "changed");
}

// ======= add fields of in-app purchase identifier keys for IOS and Google Stores ===== //
function st_additional_level_fields($level)
{
    global $st_app_settings;
    $pmpro_paymnet_type = $st_app_settings["st_pmp_options"]["payment_type"] ?? false;
    if ($pmpro_paymnet_type != 2) return;

    $google_purchase_identifier = get_pmpro_membership_level_meta($level->id, "st_google_in_app_purchase_identifier", true);
    $apple_purchase_identifier = get_pmpro_membership_level_meta($level->id, "st_apple_in_app_purchase_identifier", true);
?>
    <hr>
    <h3><?php _e("In-App Purchase"); ?></h3>
    <table class="form-table">
        <tbody>
            <tr>
                <th scope="row" valign="top"><label><?php esc_html_e('Play store identifier (Google)', 'socialv-api'); ?></label></th>
                <td>
                    <input name="google_in_app_purchase_identifier" type="text" value="<?php echo esc_attr($google_purchase_identifier); ?>" class="regular-text" />
                    <p for="google_in_app_purchase_identifier">
                        <?php esc_html_e('Enter in-app purchase identifier for the Play Store (Andorid device: Google play store).', 'socialv-api'); ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row" valign="top"><label><?php esc_html_e('App store identifier (IOS)', 'socialv-api'); ?></label></th>
                <td>
                    <input name="apple_in_app_purchase_identifier" type="text" value="<?php echo esc_attr($apple_purchase_identifier); ?>" class="regular-text" />
                    <p for="apple_in_app_purchase_identifier">
                        <?php esc_html_e('Enter in-app purchase identifier for the App Store (IOS device).', 'socialv-api'); ?>
                    </p>
                </td>
            </tr>
        </tbody>
    </table>
<?php
}
add_action("pmpro_membership_level_after_other_settings", "st_additional_level_fields", 1, 9);

// ======= save fields of in-app purchase identifier keys for IOS and Google Stores ===== //
function st_save_additional_level_fields($level_id)
{
    global $st_app_settings;
    $pmpro_paymnet_type = $st_app_settings["st_pmp_options"]["payment_type"] ?? false;
    if ($pmpro_paymnet_type != 2) return;

    if (isset($_REQUEST['google_in_app_purchase_identifier']))
        update_pmpro_membership_level_meta($level_id, 'st_google_in_app_purchase_identifier', esc_html(trim($_REQUEST['google_in_app_purchase_identifier'])));

    if (isset($_REQUEST['apple_in_app_purchase_identifier']))
        update_pmpro_membership_level_meta($level_id, 'st_apple_in_app_purchase_identifier', esc_html(trim($_REQUEST['apple_in_app_purchase_identifier'])));
}
add_action("pmpro_save_membership_level", "st_save_additional_level_fields");

function st_alter_pmpro_currencies($currencies)
{
    $currencies['INR'] = !is_array($currencies['INR']) ? [
        'name' => $currencies['INR'],
        'symbol' => '&#8377;'
    ] : $currencies['INR'];

    return $currencies;
}
add_filter("pmpro_currencies", 'st_alter_pmpro_currencies');


/**
 * Handles user login in webview via custom token validation.
 *
 * @return void
 */
function st_pmp_web_view_do_user_login()
{
 
    // Detect WebView flag from header or query param
    $is_webview = false;
    $bearer_token = '';

    if (!empty($_SERVER['HTTP_STREAMIT_WEBVIEW']) && $_SERVER['HTTP_STREAMIT_WEBVIEW'] === 'true') {
        $is_webview = true;
        $bearer_token = st_get_authorization_header(); // You should sanitize inside this function
    } elseif (!empty($_GET['HTTP_STREAMIT_WEBVIEW']) && $_GET['HTTP_STREAMIT_WEBVIEW'] === 'true') {
        $is_webview = true;
        $bearer_token = sanitize_text_field($_GET['auth_token'] ?? '');
    }
    if (!$is_webview || empty($bearer_token)) {
        return;
    }

    // Validate token and get user
    $user_response = st_validate_custom_token($bearer_token);
    if (is_wp_error($user_response) || empty($user_response->data->user->id)) {
        return;
    }

    $user_id = (int) $user_response->data->user->id;
    $user = get_userdata($user_id);
    if (!$user) {
        return;
    }

    // Log the user in
    wp_clear_auth_cookie();
    wp_set_current_user($user->ID, $user->user_login);
    
    // Set cookie for subsequent access if needed
    add_action('set_logged_in_cookie', function ($logged_in_cookie) {
        $_COOKIE[LOGGED_IN_COOKIE] = $logged_in_cookie;
    });
    wp_set_auth_cookie($user->ID);
}
add_action('init', 'st_pmp_web_view_do_user_login');


function st_get_authorization_header()
{
    $headers = [];

    // Check standard HTTP_AUTHORIZATION header
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        return $_SERVER['HTTP_AUTHORIZATION'];
    }

    // Check Apache's mod_rewrite forwarded headers
    if (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
        return $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
    }

    // Check double redirect cases (some configurations prepend multiple REDIRECT_)
    if (isset($_SERVER['REDIRECT_REDIRECT_HTTP_AUTHORIZATION'])) {
        return $_SERVER['REDIRECT_REDIRECT_HTTP_AUTHORIZATION'];
    }

    // For servers running PHP as a CGI module (Common Gateway Interface)
    if (function_exists('apache_request_headers')) {
        $headers = apache_request_headers();
        if (isset($headers['Authorization'])) {
            return $headers['Authorization'];
        }
    }

    // If nothing is found, return false
    return false;
}

add_filter('jwt_auth_token_before_dispatch', 'st_jwt_authentication_device_limit_response', 10, 2);
/**
 * Adds custom data and responce to the JWT authentication response.
 *
 * @param array $data The JWT authentication response data.
 * @param WP_User $user The authenticated user object.
 * @return array The modified JWT authentication response data.
 */
function st_jwt_authentication_device_limit_response($data, $user)
{
    // Get the user data
    $user_id   = $user->ID;
    $img       = get_user_meta($user_id, 'streamit_profile_image', true);
    $user_info = get_userdata($user_id);

    $data['first_name']    = $user_info->first_name;
    $data['last_name']     = $user_info->last_name;
    $data['user_id']       = $user_id;
    $data['username']      = $user->user_login;
    $data['user_email']    = $user->user_email;
    $data['profile_image'] = $img;

    $subscription_level = st_user_plans($user_id);
    $limit_settings = st_get_limit_login_settings();

    if ($limit_settings && (isset($limit_settings["limitlogin_is_enable"]) && $limit_settings["limitlogin_is_enable"] == 1)) {
        
        $subscription_plan_id = is_object($subscription_level) ? $subscription_level->subscription_plan_id : 0;
        
        // First try to get the subscription-specific limit
        $limit = isset($limit_settings[$subscription_plan_id]) ? 
                 intval($limit_settings[$subscription_plan_id]) : 0;

        // If no specific limit is set for the subscription level, use the default limit
        if ($limit === 0 && isset($limit_settings['default_limit'])) {
            $limit = intval($limit_settings['default_limit']);
        }

        if ($limit > 0) {
            // Check if the user has reached the login limit
            $loggedin_devices = get_user_meta($user_id, "streamit_loggedin_devices", true);
            if ($loggedin_devices && $limit <= count($loggedin_devices)) {
                return new WP_Error(
                    'streamit_login_limit_exceeded',
                    __('Account Limit Exceeded.', "streamit-api"),
                    [
                        'status' => 422,
                    ]
                );
            }
        }
    }

    return $data;
}
