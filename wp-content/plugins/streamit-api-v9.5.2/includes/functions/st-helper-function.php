<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

use Tmeister\Firebase\JWT\JWT;
use Tmeister\Firebase\JWT\Key;
/**
 * Custom response wrapper for WP REST API.
 *
 * @param array $res         The response data to be returned.
 * @param int   $status_code The HTTP status code for the response. Default is 200.
 * 
 * @return WP_REST_Response Returns a formatted WP REST API response.
 */
function st_comman_custom_response($res, $status_code = 200)
{
    // Create a new WP_REST_Response object with the provided data.
    $response = new WP_REST_Response($res);

    // Set the HTTP status code for the response.
    $response->set_status($status_code);

    // Return the response object.
    return $response;
}

/**
 * Validate JWT token for authorization.
 *
 * @param WP_REST_Request $request The REST API request object.
 * @param bool $access_request_without_auth Optional flag to allow access without authentication.
 * 
 * @return array Contains the validation result.
 */
function st_token_validation($request, $access_request_without_auth = false)
{
    if ($access_request_without_auth) {
        if (empty($request->get_header('Authorization'))) {
            return [
                'status'        => true,
                'status_code'   => 200,
                'message'       => 'Valid token.',
                'user_id'       => 0
            ];
        }
    }

    // Validate the token using Jwt_Auth_Public
    $response = (new Jwt_Auth_Public('jwt-auth', '1.1.0'))->validate_token($request, false);

    // Check if the response is a WP_Error object
    if (is_wp_error($response)) {
        return [
            'status_code'   => $response->get_error_data('status') ?? 401,
            'status'        => false,
            'message'       => $response->get_error_message() ?? __("Authorization failed", 'streamit-api'),
        ];
    }

    return [
        'status'        => true,
        'status_code'   => 200,
        'message'       => 'Valid token.',
        'user_id'       => get_current_user_id()
    ];
}


/**
 * Generate a random string of a specified length.
 *
 * @param int $length_of_string The length of the generated string. Default is 10.
 * 
 * @return string The generated random string.
 */
function st_string_generator($length_of_string = 10)
{
    // Ensure the length is a positive integer.
    $length_of_string = absint($length_of_string);

    // Define the characters to use for string generation.
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    $characters_length = strlen($characters);
    $random_string = '';

    // Generate the random string securely.
    for ($i = 0; $i < $length_of_string; $i++) {
        $random_string .= $characters[random_int(0, $characters_length - 1)];
    }

    return $random_string;
}


/**
 * Recursively sanitize text fields in an array.
 *
 * This function sanitizes an array of values by recursively applying the
 * sanitize_text_field() function to each element. HTML is removed from all
 * text fields except arrays.
 *
 * @param array $data The array of data to sanitize.
 * @return array The sanitized array.
 */
function st_sanitize_recursive_text_fields(array $data): array
{
    $sanitized_data = [];

    foreach ($data as $key => $value) {
        if (is_array($value)) {
            // Recursively sanitize arrays.
            $sanitized_data[$key] = st_sanitize_recursive_text_fields($value);
        } else {
            // Sanitize individual values.
            $sanitized_data[$key] = ('' === $value) ? null : sanitize_text_field($value);
        }
    }

    return $sanitized_data;
}
/**
 * Restrict plan list based on membership access.
 *
 * This function checks if the Paid Memberships Pro plugin is active and
 * determines user access to specific subscription plans.
 *
 * @param array $planList List of subscription plan IDs.
 * @param int   $userId   User ID.
 *
 * @return array Returns an array with user access status and subscription levels.
 */
function restrictedPlanList(array $planList, int $userId): array
{
    // Default response
    $data = [
        'user_has_access'     => true,
        'subscription_levels' => [],
    ];

    // Ensure Paid Memberships Pro functions exist
    if (!function_exists('pmpro_getLevel')) {
        return $data;
    }

    // Fetch and sanitize plan details
    $list = array_map(static function ($planId) {
        $plan = pmpro_getLevel((int) $planId);
        return $plan ? [
            'id'    => (int) $planId,
            'label' => esc_html($plan->name),
        ] : null;
    }, $planList);

    // Remove null values from the list
    $data['subscription_levels'] = array_filter($list);

    // Check user access using the custom function if available

    global $st_app_settings;

    $pmpro_payment_type = $st_app_settings["st_pmp_options"]["payment_type"] ?? false;
    if ( (int) $pmpro_payment_type === 0 ) {
        $data['user_has_access'] = true;
    }elseif(function_exists('streamit_is_pmp_access')){
        $data['user_has_access'] = streamit_is_pmp_access($planList, $userId);
    }


    return $data;
}


function streamit_source_list($sources)
{
    if (!empty($sources) && is_array($sources)) {
        $source = array_map(function ($source) {
            return $source;
        }, $sources);
    } else {
        $source = [];
    }

    return $source;
}


function st_get_comment_settings()
{
    global $st_app_settings;
    $settings = [
        "movie_comments"    => (int) (isset($st_app_settings["st_comment"]["movie_comments"]) && $st_app_settings["st_comment"]["movie_comments"]),
        "tv_show_comments"  => (int) (isset($st_app_settings["st_comment"]["tvshow_comments"]) && $st_app_settings["st_comment"]["tvshow_comments"]),
        "episode_comments"  => (int) (isset($st_app_settings["st_comment"]["episode_comments"]) && $st_app_settings["st_comment"]["episode_comments"]),
        "video_comments"    => (int) (isset($st_app_settings["st_comment"]["video_comments"]) && $st_app_settings["st_comment"]["video_comments"])
    ];

    return $settings;
}



function st_app_get_default_image()
{
    global $st_app_settings;

    $image_url = isset($st_app_settings["st_firebase"]["default_image"]);

    $image_url = !empty($image_url) ? wp_get_attachment_url($image_url, "full") : plugins_url('admin/assets/images/placeholder.png', STREAMIT_API_PLUGIN_FILE); 

    return apply_filters('st_app_get_default_image' , $image_url);
}

function st_validate_custom_token($custom_token = false)
{
    /*
     * Looking for the Authorization header
     *
     * There is two ways to get the authorization token
     *  1. via WP_REST_Request
     *  2. via custom_token, we get this for all the other API requests
     *
     * The get_header( 'Authorization' ) checks for the header in the following order:
     * 1. HTTP_AUTHORIZATION
     * 2. REDIRECT_HTTP_AUTHORIZATION
     *
     * @see https://core.trac.wordpress.org/ticket/47077
     */

    $auth_header = $custom_token;

    if (!$auth_header) {
        return new WP_Error(
            'jwt_auth_no_auth_header',
            'Authorization header not found.',
            [
                'status' => 403,
            ]
        );
    }

    /*
     * Extract the authorization header
     */
    [$token] = sscanf($auth_header, 'Bearer %s');

    /**
     * if the format is not valid return an error.
     */
    if (!$token) {
        return new WP_Error(
            'jwt_auth_bad_auth_header',
            'Authorization header malformed.',
            [
                'status' => 403,
            ]
        );
    }

    /** Get the Secret Key */
    $secret_key = defined('JWT_AUTH_SECRET_KEY') ? JWT_AUTH_SECRET_KEY : false;
    if (!$secret_key) {
        return new WP_Error(
            'jwt_auth_bad_config',
            'JWT is not configured properly, please contact the admin',
            [
                'status' => 403,
            ]
        );
    }

    /** Try to decode the token */
    try {
        $algorithm = "HS256";
        if ($algorithm === false) {
            return new WP_Error(
                'jwt_auth_unsupported_algorithm',
                __(
                    'Algorithm not supported, see https://www.rfc-editor.org/rfc/rfc7518#section-3',
                    'wp-api-jwt-auth'
                ),
                [
                    'status' => 403,
                ]
            );
        }

        $token = JWT::decode($token, new Key($secret_key, $algorithm));

        /** The Token is decoded now validate the iss */
        if ($token->iss !== get_bloginfo('url')) {
            /** The iss do not match, return error */
            return new WP_Error(
                'jwt_auth_bad_iss',
                'The iss do not match with this server',
                [
                    'status' => 403,
                ]
            );
        }

        /** So far so good, validate the user id in the token */
        if (!isset($token->data->user->id)) {
            /** No user id in the token, abort!! */
            return new WP_Error(
                'jwt_auth_bad_request',
                'User ID not found in the token',
                [
                    'status' => 403,
                ]
            );
        }

        /** Everything looks good return the decoded token if we are using the custom_token */
        if ($custom_token) {
            return $token;
        }
    } catch (Exception $e) {
        /** Something were wrong trying to decode the token, send back the error */
        return new WP_Error(
            'jwt_auth_invalid_token',
            $e->getMessage(),
            [
                'status' => 403,
            ]
        );
    }
}