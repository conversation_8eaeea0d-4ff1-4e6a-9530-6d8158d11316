<?php

/**
 * Fired during plugin activation
 *
 * @link       https://iqonic.design
 * @since      1.0.0
 *
 * @package    Streamit_Api
 * @subpackage Streamit_Api/includes
 */

class Streamit_Api_Activator
{

    /**
     * Fired when the plugin is activated.
     *
     * @since    1.0.0
     */
    public static function activate()
    {
        $require_plugins = [
            'jwt-authentication-for-wp-rest-api'    => [false],
            'paid-memberships-pro'                  => [true, 'https://assets.iqonic.design/wp/plugins/paid-memberships-pro.zip'],
            'woocommerce'                           => [false],
            'pmpro-woocommerce'                     => [true, 'http://assets.iqonic.design/wp/plugins/streamit_4/pmpro-woocommerce.zip']
        ];

        if (!class_exists('MasVideos')):
            $require_plugins['streamit']  = [true, 'http://assets.iqonic.design/wp/plugins/streamit_4/streamit.zip'];
        endif;
        
        self::install_depended_plugins($require_plugins);

        self::streamit_api_grant_pmpro_api_access();
    }

    /**
     * Install required plugins.
     *
     * @param array $require_plugins List of plugins to install.
     */
    private static function install_depended_plugins($require_plugins)
    {
        include_once ABSPATH . 'wp-admin/includes/plugin.php';
        include_once ABSPATH . 'wp-admin/includes/file.php';
        include_once ABSPATH . 'wp-admin/includes/misc.php';
        include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
        include_once ABSPATH . 'wp-admin/includes/plugin-install.php';

        $upgrader = new Plugin_Upgrader(new WP_Ajax_Upgrader_Skin());
    
        $plugin_main_files = [
            'jwt-authentication-for-wp-rest-api' => 'jwt-auth.php',
            'paid-memberships-pro' => 'paid-memberships-pro.php',
            'woocommerce' => 'woocommerce.php',
            'pmpro-woocommerce' => 'pmpro-woocommerce.php',
            'streamit' => 'streamit.php',
        ];
    
        foreach ($require_plugins as $plugin_slug => $plugin_data) {
            $main_file = $plugin_main_files[$plugin_slug] ?? $plugin_slug . '.php';
            $plugin_path = WP_PLUGIN_DIR . '/' . $plugin_slug;
            $plugin_file = $plugin_path . '/' . $main_file;
    
            if (is_plugin_active($plugin_slug . '/' . $main_file)) {
                continue; // Skip active plugins
            }

            if (!is_dir($plugin_path)) {
                if ($plugin_data[0] === true) {
                    // Install from external source
                    $download_link = $plugin_data[1] ?? '';
                    if (!empty($download_link)) {
                        $download_file = download_url($download_link);
                        if (is_wp_error($download_file)) {
                            continue;
                        }
                        $result = $upgrader->install($download_file);
                        @unlink($download_file);
                    }
                } else {
                    // Install from WordPress repository
                    $api = plugins_api('plugin_information', ['slug' => $plugin_slug]);
                    if (!is_wp_error($api)) {
                        $upgrader->install($api->download_link);
                    }
                }
            }

            // Activate the plugin after installation
            if (file_exists($plugin_file)) {
                activate_plugin($plugin_file);
            }
        }
    }

   
    /**
     * Function to grant API access to all subscriber users.
     */
    private static function streamit_api_grant_pmpro_api_access()
    {
        $role = get_role('subscriber'); // Get the Subscriber role
        if ($role) {
            $role->add_cap('pmpro_edit_members');
            $role->add_cap('pmpro_edit_memberships');
            $role->add_cap('read'); // Ensure they can read
            $role->add_cap('pmpro_memberships_menu');
            $role->add_cap('pmpro_manage_memberships');
            $role->add_cap('pmpro_get_membership_level_for_user'); 
        }
    }
}
