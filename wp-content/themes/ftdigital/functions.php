<?php
if (!defined('ABSPATH')) {
    exit;
}

include_once __DIR__ . '/classes/vendor/autoload.php';
require_once get_template_directory() . '/classes/class-wp-bootstrap-navwalker.php';
require_once ABSPATH . 'wp-includes/ID3/getid3.php';
include_once __DIR__ . '/classes/search/FT_Search.php';
include_once __DIR__ . '/classes/tools/TopicImporter.php';
include_once __DIR__ . '/classes/tools/PostImporter.php';
include_once __DIR__ . '/classes/tools.php';
include_once __DIR__ . '/classes/members/MembersOnly.php';
include_once __DIR__ . '/classes/video-manager/VideoManager.php';
include_once __DIR__ . '/classes/tools/UserImporter.php';

add_filter('xmlrpc_enabled', '__return_false');

add_filter('rest_endpoints', function ($endpoints) {
    if (isset($endpoints['/wp/v2/users'])) {
        unset($endpoints['/wp/v2/users']);
    }
    if (isset($endpoints['/wp/v2/users/(?P<id>[\d]+)'])) {
        unset($endpoints['/wp/v2/users/(?P<id>[\d]+)']);
    }

    return $endpoints;
});


function dd($data, $is_admin = true)
{
    echo '<pre style="text-align: left; direction: ltr;">';
    print_r($data);
    die;
}

function remove_archive_title_prefix($title)
{
    if (is_category()) {
        $title = single_cat_title('', false);
    } elseif (is_tag()) {
        $title = single_tag_title('', false);
    } elseif (is_author()) {
        $title = '<span class="vcard">' . get_the_author() . '</span>';
    } elseif (is_year()) {
        $title = get_the_date(_x('Y', 'yearly archives date format', 'textdomain'));
    } elseif (is_month()) {
        $title = get_the_date(_x('F Y', 'monthly archives date format', 'textdomain'));
    } elseif (is_day()) {
        $title = get_the_date(_x('F j, Y', 'daily archives date format', 'textdomain'));
    } elseif (is_post_type_archive()) {
        $title = post_type_archive_title('', false);
    } elseif (is_tax()) {
        $title = single_term_title('', false);
    }

    return $title;
}

add_filter('get_the_archive_title', 'remove_archive_title_prefix');
function validate_dns_email($email)
{
    if (is_email($email)) {
        $arr = explode("@", $email);
        if (checkdnsrr(array_pop($arr), "MX")) {
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
}

add_action('wp_enqueue_scripts', 'theme_assets');
function theme_assets()
{
    $ver = wp_get_theme()->get('Version');

    wp_enqueue_style('ebtech-style', get_stylesheet_uri(), [
        'ebtech-getbootstrap-min',
        'ebtech-swiper-css'
    ], '1.2.2' . time());
    wp_enqueue_style('ebtech-custom', get_template_directory_uri() . '/assets/css/custom.css', ['ebtech-style'], '1.3' . time());
    wp_enqueue_style('ftdigital-google-fonts',
        'https://fonts.googleapis.com/css2?family=Assistant:wght@200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap',
        null);
    wp_enqueue_script('ebtech-js', get_template_directory_uri() . '/assets/js/script.js', ['ebtech-swiper-js'], '1.3' . time(), true);

    wp_script_add_data('ebtech-js', 'async', true);
    wp_localize_script('ebtech-js', 'EBUrls', [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'page_template' => get_page_template(),
        'get_template_directory_uri' => get_template_directory_uri(),
    ]);

    /*animation*/
    wp_enqueue_style('aos', 'https://unpkg.com/aos@next/dist/aos.css', [], null);
    wp_enqueue_script('aos', 'https://unpkg.com/aos@next/dist/aos.js', ['jquery'], null, ['in_footer' => true]);


    wp_enqueue_style('ebtech-getbootstrap-min', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css', [ /*'ebtech-swiper-css'*/], '5.3.3');
//	wp_enqueue_style( 'ebtech-getbootstrap-min', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css', [ /*'ebtech-swiper-css'*/ ], '5.3.3' );
    wp_enqueue_script('ebtech-popper-js', 'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js', ['jquery'], '2.11.8', true);
    wp_enqueue_script('ebtech-getbootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', [
        'jquery',
        'ebtech-popper-js'
    ], '5.3.3', ['in_footer' => true]);

//    wp_enqueue_script('ebtech-api-video', 'https://unpkg.com/@api.video/player-sdk', ['jquery'], 1, ['in_footer' => true]);

    wp_enqueue_script('ebtech-swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js', null, 11);
    wp_enqueue_style('ebtech-swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css', ['ebtech-getbootstrap-min'], 11);

//    wp_enqueue_style('video-js', 'https://vjs.zencdn.net/8.10.0/video-js.css', [], '8.10.0');
//    wp_enqueue_script('video-js', 'https://vjs.zencdn.net/8.10.0/video.min.js', [], '8.10.0', true);

    wp_enqueue_style('ebtech-toastify-css', 'https://cdnjs.cloudflare.com/ajax/libs/toastify-js/1.6.1/toastify.min.css', [], '1.6.1');
    wp_enqueue_script('ebtech-toastify-js', 'https://cdnjs.cloudflare.com/ajax/libs/toastify-js/1.6.1/toastify.min.js', ['jquery'], '1.6.1', ['in_footer' => true]);

    if ((!is_admin()) && is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}

function ebtech_theme_support()
{
    add_theme_support('automatic-feed-links');
    add_theme_support(
        'custom-background',
        [
            'default-color' => 'f5efe0',
        ]
    );
    add_theme_support('post-thumbnails');
    set_post_thumbnail_size(1200, 9999);
    add_image_size('ebtech-fullscreen', 1980, 9999);
    $logo_width = 154;
    $logo_height = 37;

    if (get_theme_mod('retina_logo', false)) {
        $logo_width = floor($logo_width * 2);
        $logo_height = floor($logo_height * 2);
    }

    add_theme_support(
        'custom-logo',
        [
            'height' => $logo_height,
            'width' => $logo_width,
            'flex-height' => true,
            'flex-width' => true,
        ]
    );

    add_theme_support(
        'white-logo',
        [
            'height' => $logo_height,
            'width' => $logo_width,
            'flex-height' => true,
            'flex-width' => true,
        ]
    );
    add_theme_support('title-tag');
    add_theme_support('post-formats', ['aside', 'image', 'video', 'quote', 'link']);
    add_theme_support(
        'html5',
        [
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'script',
            'style',
        ]
    );

    load_theme_textdomain('ebtech');

    add_theme_support('align-wide');
    add_theme_support('customize-selective-refresh-widgets');
}

add_action('after_setup_theme', 'ebtech_theme_support');

function ebtech_menus()
{
    $locations = [
        'primary' => __('Primary Menu', 'ftdigital'),
        'expanded' => __('Primary Expanded Menu', 'ftdigital'),
        'mobile' => __('Mobile Menu', 'ftdigital'),
        'footer' => __('Footer Menu', 'ftdigital'),
        'social' => __('Social Menu', 'ftdigital'),
        'footer_useful_links' => __('Footer Useful Links', 'ftdigital'),
        'footer_lessons' => __('Footer Lessons', 'ftdigital'),
    ];
    register_nav_menus($locations);
}

add_action('init', 'ebtech_menus');
function ebtech_get_color_for_area($area = 'content', $context = 'text')
{

    $settings = get_theme_mod(
        'accent_accessible_colors',
        array(
            'content' => array(
                'text' => '#000000',
                'accent' => '#cd2653',
                'secondary' => '#6d6d6d',
                'borders' => '#dcd7ca',
            ),
            'header-footer' => array(
                'text' => '#000000',
                'accent' => '#cd2653',
                'secondary' => '#6d6d6d',
                'borders' => '#dcd7ca',
            ),
        )
    );

    // If we have a value return it.
    if (isset($settings[$area]) && isset($settings[$area][$context])) {
        return $settings[$area][$context];
    }

    // Return false if the option doesn't exist.
    return false;
}

add_action('upload_mimes', 'add_file_types_to_uploads');
function add_file_types_to_uploads($file_types)
{
    $new_filetypes = [];
    $new_filetypes['svg'] = 'image/svg+xml';
    $file_types = array_merge($file_types, $new_filetypes);

    return $file_types;
}

function get_bootstrap_pagination()
{
    $links = paginate_links(array(
        'type' => 'array',
        'prev_next' => true,
        'prev_text' => '<span aria-hidden="true">' . wp_kses_post(__('<i class="lqd-icn-ess icon-ion-ios-arrow-back"></i>', 'ftdigital')) . '</span>',
        'next_text' => '<span aria-hidden="true">' . wp_kses_post(__('<i class="lqd-icn-ess icon-ion-ios-arrow-forward"></i>', 'ftdigital')) . '</span>'
    ));

    if (!empty($links)) {
        echo '<nav aria-label="Page navigation">';
        echo '<ul class="pagination justify-content-center gap-1">';
        foreach ($links as $link) {
            echo '<li class="page-item">' . str_replace('page-numbers', 'page-link', $link) . '</li>';
        }
        echo '</ul>';
        echo '</nav>';
    }
}

add_filter('use_block_editor_for_post', '__return_false');
add_filter('use_widgets_block_editor', '__return_false');

add_action('wp_ajax_handle_contact_form', 'handle_contact_form_callback');
add_action('wp_ajax_nopriv_handle_contact_form', 'handle_contact_form_callback');

function handle_contact_form_callback()
{
    if (!check_ajax_referer('contact_form_nonce', 'nonce', false)) {
        wp_send_json_error(['message' => 'אימות נכשל']);
    }

    $fullname = sanitize_text_field($_POST['fullname']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';
    $receiveemails = isset($_POST['receiveemails']) && $_POST['receiveemails'] === 'yes' ? 'Yes' : 'No';
    $receiptsms = isset($_POST['receiptsms']) && $_POST['receiptsms'] === 'yes' ? 'Yes' : 'No';

    if (empty($fullname) || empty($email)) {
        wp_send_json_error(['message' => 'שדות חובה חסרים']);
    }

    $contact_page = get_field('contact_page', get_option('page_for_contact') ?: get_queried_object_id());
    $to = sanitize_email($contact_page['email_address']);
    $subject = 'New Contact Form Submission';
    $body = "Name: $fullname\nEmail: $email\nPhone: $phone\nReceive Emails: $receiveemails\nReceive SMS: $receiptsms";
    if ($message) {
        $body .= "\nMessage:\n$message";
    }
    $headers = ['From: ' . $email];

    if (wp_mail($to, $subject, $body, $headers)) {
        wp_send_json_success();
    } else {
        wp_send_json_error(['message' => 'שליחת המייל נכשלה']);
    }
}

add_filter('streamit_api_admin_tabs', function(){
    return array(
        'home' => array(
            'label' => esc_html__('Home', 'streamit-api'),
            'target' => 'admin_home_tab',
            'class' => array(),
        ),
        'video' => array(
            'label' => esc_html__('Training', 'streamit-api'),
            'target' => 'admin_video_tab',
            'class' => array(),
        ),
    );
});

// Order posts by ID DESC for taxonomy archives
add_action('pre_get_posts', function($query) {
    if (!is_admin() && $query->is_main_query() && is_tax()) {
        $query->set('orderby', 'ID');
        $query->set('order', 'DESC');
    }
});