<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpClient\Internal;

use Symfony\Component\HttpClient\Response\CurlResponse;

/**
 * A pushed response with its request headers.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class PushedResponse
{
    public function __construct(
        public CurlResponse $response,
        public array $requestHeaders,
        public array $parentOptions,
        public \CurlHandle $handle,
    ) {
    }
}
