{"name": "api-video/php-api-client", "description": "PHP client for api.video web services.", "keywords": ["api.video"], "homepage": "https://api.video/", "license": "MIT", "type": "library", "authors": [{"name": "ApiVideo", "homepage": "https://api.video/"}], "require": {"php": "^7.3 || ^8.0", "ext-json": "*", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "php-http/multipart-stream-builder": "^1.1"}, "require-dev": {"phpunit/phpunit": "^9", "symfony/http-client": "^5.2", "nyholm/psr7": "^1.4"}, "autoload": {"psr-4": {"ApiVideo\\Client\\": "src/"}}, "autoload-dev": {"psr-4": {"ApiVideo\\Client\\Tests\\": "tests/"}}}