# # PlayerSessionEvent

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **string** | Possible values are: ready, play, pause, resume, seek.backward, seek.forward, end | [optional]
**emittedAt** | [**\DateTime**](\DateTime.md) | When an event occurred, presented in ATOM UTC format. | [optional]
**at** | **int** |  | [optional]
**from** | **int** |  | [optional]
**to** | **int** |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
