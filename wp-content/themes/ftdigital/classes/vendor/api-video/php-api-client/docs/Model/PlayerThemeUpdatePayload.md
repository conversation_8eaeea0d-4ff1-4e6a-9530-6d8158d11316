# # PlayerThemeUpdatePayload

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | Add a name for your player theme here. | [optional]
**text** | **string** | RGBA color for timer text. Default: rgba(255, 255, 255, 1) | [optional]
**link** | **string** | RGBA color for all controls. Default: rgba(255, 255, 255, 1) | [optional]
**linkHover** | **string** | RGBA color for all controls when hovered. Default: rgba(255, 255, 255, 1) | [optional]
**linkActive** | **string** | RGBA color for the play button when hovered. | [optional]
**trackPlayed** | **string** | RGBA color playback bar: played content. Default: rgba(88, 131, 255, .95) | [optional]
**trackUnplayed** | **string** | RGBA color playback bar: downloaded but unplayed (buffered) content. Default: rgba(255, 255, 255, .35) | [optional]
**trackBackground** | **string** | RGBA color playback bar: background. Default: rgba(255, 255, 255, .2) | [optional]
**backgroundTop** | **string** | RGBA color: top 50% of background. Default: rgba(0, 0, 0, .7) | [optional]
**backgroundBottom** | **string** | RGBA color: bottom 50% of background. Default: rgba(0, 0, 0, .7) | [optional]
**backgroundText** | **string** | RGBA color for title text. Default: rgba(255, 255, 255, 1) | [optional]
**enableApi** | **bool** | enable/disable player SDK access. Default: true | [optional]
**enableControls** | **bool** | enable/disable player controls. Default: true | [optional]
**forceAutoplay** | **bool** | enable/disable player autoplay. Default: false | [optional]
**hideTitle** | **bool** | enable/disable title. Default: false | [optional]
**forceLoop** | **bool** | enable/disable looping. Default: false | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
