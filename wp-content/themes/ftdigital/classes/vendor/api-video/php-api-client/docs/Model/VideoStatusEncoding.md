# # VideoStatusEncoding

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**playable** | **bool** | Whether the video is playable or not. | [optional]
**qualities** | [**\ApiVideo\Client\Model\Quality[]**](Quality.md) | Available qualities the video can be viewed in. | [optional]
**metadata** | [**\ApiVideo\Client\Model\VideoStatusEncodingMetadata**](VideoStatusEncodingMetadata.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
