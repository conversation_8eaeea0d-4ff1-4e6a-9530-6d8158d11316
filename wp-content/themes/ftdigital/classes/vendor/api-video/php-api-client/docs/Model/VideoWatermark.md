# # VideoWatermark

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** | id of the watermark | [optional]
**top** | **string** | Distance expressed in px or % between the top-border of the video and the watermark-image. | [optional]
**left** | **string** | Distance expressed in px or % between the left-border of the video and the watermark-image. | [optional]
**bottom** | **string** | Distance expressed in px or % between the bottom-border of the video and the watermark-image. | [optional]
**right** | **string** | Distance expressed in px or % between the right-border of the video and the watermark-image. | [optional]
**width** | **string** | Width of the watermark-image relative to the video if expressed in %. Otherwise a fixed width. NOTE: To keep intrinsic watermark-image width use &#x60;initial&#x60;. | [optional]
**height** | **string** | Height of the watermark-image relative to the video if expressed in %. Otherwise a fixed height. NOTE: To keep intrinsic watermark-image height use &#x60;initial&#x60;. | [optional]
**opacity** | **string** | Opacity expressed in % only to specify the degree of the watermark-image transparency with the video. | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
