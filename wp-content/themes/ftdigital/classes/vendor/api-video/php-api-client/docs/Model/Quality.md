# # Quality

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **string** | The type of video (hls or mp4). | [optional]
**quality** | **string** | The quality of the video you have, in pixels. Choices include 360p, 480p, 720p, 1080p, and 2160p. | [optional]
**status** | **string** | The status of your video. Statuses include waiting - the video is waiting to be encoded. encoding - the video is in the process of being encoded. encoded - the video was successfully encoded. failed - the video failed to be encoded. | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
