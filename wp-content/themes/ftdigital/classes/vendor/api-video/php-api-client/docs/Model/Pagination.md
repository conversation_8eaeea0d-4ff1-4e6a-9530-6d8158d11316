# # Pagination

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**itemsTotal** | **int** | Total number of items that exist. | [optional] [readonly]
**pagesTotal** | **int** | Number of items listed in the current page. | [optional] [readonly]
**pageSize** | **int** | Maximum number of item per page. | [optional] [readonly]
**currentPage** | **int** | The current page index. | [optional] [readonly]
**currentPageItems** | **int** | The number of items on the current page. | [optional] [readonly]
**links** | [**\ApiVideo\Client\Model\PaginationLink[]**](PaginationLink.md) |  |

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
