# # VideoThumbnailPickPayload

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**timecode** | **string** | Frame in video to be used as a placeholder before the video plays.  Example: &#39;\&quot;00:01:00.000\&quot; for 1 minute into the video.&#39; <PERSON><PERSON>s:  \&quot;hh:mm:ss.ms\&quot; \&quot;hh:mm:ss:frameNumber\&quot; \&quot;124\&quot; (integer value is reported as seconds)  If selection is out of range, \&quot;00:00:00.00\&quot; will be chosen. |

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
