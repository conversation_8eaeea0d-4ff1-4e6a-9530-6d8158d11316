# # UploadToken

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**token** | **string** | The unique identifier for the token you will use to authenticate an upload. | [optional]
**ttl** | **int** | Time-to-live - how long the upload token is valid for. | [optional]
**createdAt** | [**\DateTime**](\DateTime.md) | When the token was created, displayed in ATOM UTC format. | [optional]
**expiresAt** | [**\DateTime**](\DateTime.md) | When the token expires, displayed in ATOM UTC format. | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
