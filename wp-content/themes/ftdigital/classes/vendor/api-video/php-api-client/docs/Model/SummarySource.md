# # SummarySource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**abstract** | **string** | A short outline of the contents of the video. The length of an &#x60;abstract&#x60; depends on the amount of content in a video that can be transcribed. The API condenses the contents into minimum 20, maximum 300 words. | [optional]
**takeaways** | **string[]** | A list of 3 key points from the video, in chronological order. | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
