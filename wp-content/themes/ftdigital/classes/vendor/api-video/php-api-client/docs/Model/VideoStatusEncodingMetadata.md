# # VideoStatusEncodingMetadata

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**width** | **int** | The width of the video in pixels. | [optional]
**height** | **int** | The height of the video in pixels. | [optional]
**bitrate** | **float** | The number of bits processed per second. | [optional]
**duration** | **int** | The length of the video. | [optional]
**framerate** | **int** | The frequency with which consecutive images or frames appear on a display. Shown in this API as frames per second (fps). | [optional]
**samplerate** | **int** | How many samples per second a digital audio system uses to record an audio signal. The higher the rate, the higher the frequencies that can be recorded. They are presented in this API using hertz. | [optional]
**videoCodec** | **string** | The method used to compress and decompress digital video. API Video supports all codecs in the libavcodec library. | [optional]
**audioCodec** | **string** | The method used to compress and decompress digital audio for your video. | [optional]
**aspectRatio** | **string** |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
