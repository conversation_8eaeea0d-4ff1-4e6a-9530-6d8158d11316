# # AnalyticsMetricsOverTimeResponseData

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**emittedAt** | **string** | Returns the timestamp of the event that belongs to a specific metric in ATOM date-time format. For example, if you set &#x60;play&#x60; with an &#x60;hour&#x60; interval in your request, then &#x60;emittedAt&#x60; returns the hourly timestamps of every play event within the timeframe you defined. | [optional]
**metricValue** | **float** | Returns the data for a specific metric value. | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
