[2021-11-18 09:03:30] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from information_schema.tables where table_schema = holiday and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (SQL: select * from information_schema.tables where table_schema = holiday and table_name = migrations and table_type = 'BASE TABLE') at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php:703)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(775): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(666): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(367): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(652): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(106): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(77): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(585): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(94): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct('mysql:host=127....', 'root', '', Array)
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1021): call_user_func(Object(Closure))
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1057): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(433): Illuminate\\Database\\Connection->getReadPdo()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(359): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(696): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(775): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(755): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(666): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(367): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(44): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(652): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(106): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(77): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(585): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(94): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2021-11-18 09:19:06] local.ERROR: Class 'Database\Seeders\Country' not found {"exception":"[object] (Error(code: 0): Class 'Database\\Seeders\\Country' not found at /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php:24)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 05:03:26] local.ERROR: Command "make" is not defined.

Did you mean one of these?
    make:cast
    make:channel
    make:command
    make:component
    make:controller
    make:event
    make:exception
    make:factory
    make:job
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:observer
    make:policy
    make:provider
    make:request
    make:resource
    make:rule
    make:seeder
    make:test {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make\" is not defined.

Did you mean one of these?
    make:cast
    make:channel
    make:command
    make:component
    make:controller
    make:event
    make:exception
    make:factory
    make:job
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:observer
    make:policy
    make:provider
    make:request
    make:resource
    make:rule
    make:seeder
    make:test at /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php:682)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(255): Symfony\\Component\\Console\\Application->find('make')
#1 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2021-11-19 05:44:32] local.ERROR: Target class [VideoController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [VideoController] does not exist. at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:879)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('VideoController')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('VideoController', Array, true)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('VideoController', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('VideoController', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(276): Illuminate\\Foundation\\Application->make('VideoController')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1069): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1012): Illuminate\\Routing\\Route->controllerMiddleware()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(708): Illuminate\\Routing\\Route->gatherMiddleware()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(688): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#33 {main}

[previous exception] [object] (ReflectionException(code: -1): Class VideoController does not exist at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:877)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(877): ReflectionClass->__construct('VideoController')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('VideoController')
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('VideoController', Array, true)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('VideoController', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('VideoController', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(276): Illuminate\\Foundation\\Application->make('VideoController')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1069): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1012): Illuminate\\Routing\\Route->controllerMiddleware()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(708): Illuminate\\Routing\\Route->gatherMiddleware()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(688): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#34 {main}
"} 
[2021-11-19 05:48:32] local.ERROR: Class 'App\Model\InfiniteScroll' not found {"exception":"[object] (Error(code: 0): Class 'App\\Model\\InfiniteScroll' not found at /Users/<USER>/Lara/holiday/app/Http/Controllers/VideoController.php:11)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\VideoController->InfiniteScroll(Object(Illuminate\\Http\\Request))
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('InfiniteScroll', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\VideoController), 'InfiniteScroll')
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(695): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(697): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#45 {main}
"} 
[2021-11-19 07:25:37] local.ERROR: Target class [VideoController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [VideoController] does not exist. at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:879)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('VideoController')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('VideoController', Array, true)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('VideoController', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('VideoController', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(276): Illuminate\\Foundation\\Application->make('VideoController')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1069): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1012): Illuminate\\Routing\\Route->controllerMiddleware()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(708): Illuminate\\Routing\\Route->gatherMiddleware()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(688): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#33 {main}

[previous exception] [object] (ReflectionException(code: -1): Class VideoController does not exist at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:877)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(877): ReflectionClass->__construct('VideoController')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('VideoController')
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('VideoController', Array, true)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('VideoController', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('VideoController', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(276): Illuminate\\Foundation\\Application->make('VideoController')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1069): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Route.php(1012): Illuminate\\Routing\\Route->controllerMiddleware()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(708): Illuminate\\Routing\\Route->gatherMiddleware()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(688): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#34 {main}
"} 
[2021-11-19 07:26:12] local.ERROR: syntax error, unexpected ''Title'' (T_CONSTANT_ENCAPSED_STRING), expecting identifier (T_STRING) or variable (T_VARIABLE) or '{' or '$' (View: /Users/<USER>/Lara/holiday/resources/views/data.blade.php) {"view":{"view":"/Users/<USER>/Lara/holiday/resources/views/data.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1580375952 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#285</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1580375952\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","posts":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#299</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>5</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#308</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#309</a> &hellip;29}
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#310</a> &hellip;29}
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#311</a> &hellip;29}
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#312</a> &hellip;29}
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#313</a> &hellip;29}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>5</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/load-more-data</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/load-more-data</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): syntax error, unexpected ''Title'' (T_CONSTANT_ENCAPSED_STRING), expecting identifier (T_STRING) or variable (T_VARIABLE) or '{' or '$' (View: /Users/<USER>/Lara/holiday/resources/views/data.blade.php) at /Users/<USER>/Lara/holiday/resources/views/data.blade.php:4)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#3 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#7 /Users/<USER>/Lara/holiday/resources/views/holidayView.blade.php(20): Illuminate\\View\\View->render()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(107): require('/Users/<USER>')
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#12 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(794): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(763): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(695): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(697): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#61 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected ''Title'' (T_CONSTANT_ENCAPSED_STRING), expecting identifier (T_STRING) or variable (T_VARIABLE) or '{' or '$' at /Users/<USER>/Lara/holiday/storage/framework/views/867cd9720f1bf0b1357b6548d82462b13154b111.php:4)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#3 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#7 /Users/<USER>/Lara/holiday/storage/framework/views/d6428d95aba9911792b9d553cd20ef7bdbd21407.php(20): Illuminate\\View\\View->render()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(107): require('/Users/<USER>')
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#12 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(794): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(763): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(695): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(697): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#61 {main}
"} 
[2021-11-19 07:28:01] local.ERROR: syntax error, unexpected 'Link' (T_STRING), expecting ')' (View: /Users/<USER>/Lara/holiday/resources/views/data.blade.php) {"view":{"view":"/Users/<USER>/Lara/holiday/resources/views/data.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1201490474 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#285</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1201490474\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","posts":"<pre class=sf-dump id=sf-dump-2018015370 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#299</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>5</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#308</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#309</a> &hellip;29}
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#310</a> &hellip;29}
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#311</a> &hellip;29}
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#312</a> &hellip;29}
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Model\\InfiniteScroll
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Model</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InfiniteScroll</span> {<a class=sf-dump-ref>#313</a> &hellip;29}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>5</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/load-more-data</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/load-more-data</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-2018015370\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): syntax error, unexpected 'Link' (T_STRING), expecting ')' (View: /Users/<USER>/Lara/holiday/resources/views/data.blade.php) at /Users/<USER>/Lara/holiday/resources/views/data.blade.php:7)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#3 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#7 /Users/<USER>/Lara/holiday/resources/views/holidayView.blade.php(20): Illuminate\\View\\View->render()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(107): require('/Users/<USER>')
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#12 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(794): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(763): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(695): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(697): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#61 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected 'Link' (T_STRING), expecting ')' at /Users/<USER>/Lara/holiday/storage/framework/views/867cd9720f1bf0b1357b6548d82462b13154b111.php:7)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#3 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#7 /Users/<USER>/Lara/holiday/storage/framework/views/d6428d95aba9911792b9d553cd20ef7bdbd21407.php(20): Illuminate\\View\\View->render()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(107): require('/Users/<USER>')
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/Users/<USER>', Array)
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(61): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/Users/<USER>', Array)
#12 /Users/<USER>/Lara/holiday/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(139): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('/Users/<USER>', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(122): Illuminate\\View\\View->getContents()
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/View.php(91): Illuminate\\View\\View->renderContents()
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(794): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(763): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(695): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(697): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(672): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(636): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Routing/Router.php(625): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Lara/holiday/vendor/fruitcake/laravel-cors/src/HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Lara/holiday/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Lara/holiday/server.php(21): require_once('/Users/<USER>')
#61 {main}
"} 
[2021-11-19 07:35:18] local.ERROR: No arguments expected for "clear" command, got "cche". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): No arguments expected for \"clear\" command, got \"cche\". at /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php:186)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php(80): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('cche')
#1 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/Input.php(55): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#2 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(258): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#4 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ClearCompiledCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2021-11-19 07:35:21] local.ERROR: No arguments expected for "clear" command, got "cache". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): No arguments expected for \"clear\" command, got \"cache\". at /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php:186)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php(80): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('cache')
#1 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/Input.php(55): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#2 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(258): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#4 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ClearCompiledCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2021-11-19 07:40:40] local.ERROR: Target class [Database\Seeders\CountrySeeder] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Database\\Seeders\\CountrySeeder] does not exist. at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:879)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('Database\\\\Seeder...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('Database\\\\Seeder...', Array, true)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('Database\\\\Seeder...', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('Database\\\\Seeder...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(96): Illuminate\\Foundation\\Application->make('Database\\\\Seeder...')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#14 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (ReflectionException(code: -1): Class Database\\Seeders\\CountrySeeder does not exist at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:877)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(877): ReflectionClass->__construct('Database\\\\Seeder...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('Database\\\\Seeder...')
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('Database\\\\Seeder...', Array, true)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('Database\\\\Seeder...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('Database\\\\Seeder...', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(96): Illuminate\\Foundation\\Application->make('Database\\\\Seeder...')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 07:40:52] local.ERROR: Target class [Database\Seeders\HolidayySeeder] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Database\\Seeders\\HolidayySeeder] does not exist. at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:879)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('Database\\\\Seeder...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('Database\\\\Seeder...', Array, true)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('Database\\\\Seeder...', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('Database\\\\Seeder...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(96): Illuminate\\Foundation\\Application->make('Database\\\\Seeder...')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#14 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (ReflectionException(code: -1): Class Database\\Seeders\\HolidayySeeder does not exist at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php:877)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(877): ReflectionClass->__construct('Database\\\\Seeder...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(758): Illuminate\\Container\\Container->build('Database\\\\Seeder...')
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->resolve('Database\\\\Seeder...', Array, true)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(694): Illuminate\\Foundation\\Application->resolve('Database\\\\Seeder...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(826): Illuminate\\Container\\Container->make('Database\\\\Seeder...', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(96): Illuminate\\Foundation\\Application->make('Database\\\\Seeder...')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 07:40:56] local.ERROR: Class 'App\Models\Holiday' not found {"exception":"[object] (Error(code: 0): Class 'App\\Models\\Holiday' not found at /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php:17)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 07:43:52] local.ERROR: Class 'App\Model\Holiday' not found {"exception":"[object] (Error(code: 0): Class 'App\\Model\\Holiday' not found at /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php:17)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 07:44:21] local.ERROR: Class 'App\Model\Holiday' not found {"exception":"[object] (Error(code: 0): Class 'App\\Model\\Holiday' not found at /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php:17)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2021-11-19 07:44:45] local.ERROR: SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID (SQL: insert into "holidays" ("Title", "Video ID", "MP4 Link", "updated_at", "created_at") values (Snow Slowly Falling Down.mp4 ,  vi6o0ibA0F37uxFVBiUi0Ieo ,  https://cdn.api.video/vod/vi6o0ibA0F37uxFVBiUi0Ieo/mp4/1080/source.mp4, 2021-11-19 07:44:45, 2021-11-19 07:44:45)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID (SQL: insert into \"holidays\" (\"Title\", \"Video ID\", \"MP4 Link\", \"updated_at\", \"created_at\") values (Snow Slowly Falling Down.mp4 ,  vi6o0ibA0F37uxFVBiUi0Ieo ,  https://cdn.api.video/vod/vi6o0ibA0F37uxFVBiUi0Ieo/mp4/1080/source.mp4, 2021-11-19 07:44:45, 2021-11-19 07:44:45)) at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php:703)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(663): Illuminate\\Database\\Connection->runQueryCallback('insert into \"ho...', Array, Object(Closure))
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(493): Illuminate\\Database\\Connection->run('insert into \"ho...', Array, Object(Closure))
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(445): Illuminate\\Database\\Connection->statement('insert into \"ho...', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"ho...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2966): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"ho...', Array, 'id')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1641): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1188): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1153): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(994): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(884): Illuminate\\Database\\Eloquent\\Model->save()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/helpers.php(263): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Model\\Holiday))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(885): tap(Object(App\\Model\\Holiday), Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2117): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2129): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php(27): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#31 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php:486)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(486): PDO->prepare('insert into \"ho...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(696): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"ho...', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(663): Illuminate\\Database\\Connection->runQueryCallback('insert into \"ho...', Array, Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(493): Illuminate\\Database\\Connection->run('insert into \"ho...', Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(445): Illuminate\\Database\\Connection->statement('insert into \"ho...', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"ho...', Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2966): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"ho...', Array, 'id')
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1641): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1188): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1153): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(994): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(884): Illuminate\\Database\\Eloquent\\Model->save()
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/helpers.php(263): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Model\\Holiday))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(885): tap(Object(App\\Model\\Holiday), Object(Closure))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2117): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2129): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php(27): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#33 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 {main}
"} 
[2021-11-19 07:45:51] local.ERROR: No arguments expected for "clear" command, got "cache". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): No arguments expected for \"clear\" command, got \"cache\". at /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php:186)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/ArgvInput.php(80): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('cache')
#1 /Users/<USER>/Lara/holiday/vendor/symfony/console/Input/Input.php(55): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#2 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(258): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#4 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ClearCompiledCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2021-11-19 07:45:56] local.ERROR: Command "cache" is not defined.

Did you mean one of these?
    cache:clear
    cache:forget
    cache:table
    config:cache
    event:cache
    route:cache
    view:cache {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"cache\" is not defined.

Did you mean one of these?
    cache:clear
    cache:forget
    cache:table
    config:cache
    event:cache
    route:cache
    view:cache at /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php:682)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(255): Symfony\\Component\\Console\\Application->find('cache')
#1 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2021-11-19 07:47:01] local.ERROR: SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID (SQL: insert into "holidays" ("Title", "Video ID", "MP4 Link", "updated_at", "created_at") values (Snow Slowly Falling Down.mp4 ,  vi6o0ibA0F37uxFVBiUi0Ieo ,  https://cdn.api.video/vod/vi6o0ibA0F37uxFVBiUi0Ieo/mp4/1080/source.mp4, 2021-11-19 07:47:01, 2021-11-19 07:47:01)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID (SQL: insert into \"holidays\" (\"Title\", \"Video ID\", \"MP4 Link\", \"updated_at\", \"created_at\") values (Snow Slowly Falling Down.mp4 ,  vi6o0ibA0F37uxFVBiUi0Ieo ,  https://cdn.api.video/vod/vi6o0ibA0F37uxFVBiUi0Ieo/mp4/1080/source.mp4, 2021-11-19 07:47:01, 2021-11-19 07:47:01)) at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php:703)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(663): Illuminate\\Database\\Connection->runQueryCallback('insert into \"ho...', Array, Object(Closure))
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(493): Illuminate\\Database\\Connection->run('insert into \"ho...', Array, Object(Closure))
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(445): Illuminate\\Database\\Connection->statement('insert into \"ho...', Array)
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"ho...', Array)
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2966): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"ho...', Array, 'id')
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1641): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1188): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1153): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(994): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(884): Illuminate\\Database\\Eloquent\\Model->save()
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/helpers.php(263): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Model\\Holiday))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(885): tap(Object(App\\Model\\Holiday), Object(Closure))
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2117): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2129): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php(27): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#17 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#31 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table holidays has no column named Video ID at /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php:486)
[stacktrace]
#0 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(486): PDO->prepare('insert into \"ho...')
#1 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(696): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"ho...', Array)
#2 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(663): Illuminate\\Database\\Connection->runQueryCallback('insert into \"ho...', Array, Object(Closure))
#3 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(493): Illuminate\\Database\\Connection->run('insert into \"ho...', Array, Object(Closure))
#4 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Connection.php(445): Illuminate\\Database\\Connection->statement('insert into \"ho...', Array)
#5 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"ho...', Array)
#6 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2966): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"ho...', Array, 'id')
#7 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1641): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1188): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1153): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(994): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(884): Illuminate\\Database\\Eloquent\\Model->save()
#12 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/helpers.php(263): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Model\\Holiday))
#13 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(885): tap(Object(App\\Model\\Holiday), Object(Closure))
#14 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2117): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2129): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 /Users/<USER>/Lara/holiday/database/seeders/HolidaySeeder.php(27): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\HolidaySeeder->run()
#19 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(149): Illuminate\\Container\\Container->call(Array, Array)
#24 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(66): Illuminate\\Database\\Seeder->__invoke()
#25 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#26 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(67): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#27 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#28 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\\Container\\Container->call(Array)
#33 /Users/<USER>/Lara/holiday/vendor/symfony/console/Command/Command.php(299): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(978): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(295): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Lara/holiday/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Lara/holiday/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 /Users/<USER>/Lara/holiday/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 {main}
"} 
