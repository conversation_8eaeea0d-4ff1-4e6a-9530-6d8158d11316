![](https://github.com/apivideo/API_OAS_file/blob/master/apivideo_banner.png)

# Upload a video with Laravel 8

api.video is an API that encodes on the go to facilitate immediate playback, enhancing viewer streaming experiences across multiple devices and platforms. You can stream live or on-demand online videos within minutes.

--------------------

This is a simple video uploader. A form collects basic details about the video someone is uploading, and allows them to choose a video to upload. When you submit the form, it collects You upload files to api.video in two parts - first you create a video container, then you upload a video into the container. 

There is a complete blog post walking you through this example here: [Upload a video with <PERSON><PERSON>](https://api.video/blog/tutorials/upload-a-video-with-laravel)

# Requirements

* api.video account - sign up [here](https://my.api.video)
* Laravel 8 
* NPM and node.js
* Composer 
* api.video PHP client - (if you're here, you've found it!)

# Installation Requirements

Your project needs to have these items installed: 

* composer require api-video/php-api-client
* composer require symfony/http-client
* composer require nyholm/psr7

# Further Reading...

For additional details about the contents of this project, please see the blog post: [Upload a video with <PERSON><PERSON>](https://api.video/blog/tutorials/upload-a-video-with-laravel)
