<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd" backupGlobals="false" colors="true" bootstrap="vendor/autoload.php" failOnRisky="true" failOnWarning="true">
  <coverage>
    <include>
      <directory>./</directory>
    </include>
    <exclude>
      <directory>./tests</directory>
      <directory>./vendor</directory>
    </exclude>
  </coverage>
  <php>
    <ini name="error_reporting" value="-1"/>
  </php>
  <testsuites>
    <testsuite name="api.video PHP client Test Suite">
      <directory>./tests/</directory>
    </testsuite>
  </testsuites>
</phpunit>
