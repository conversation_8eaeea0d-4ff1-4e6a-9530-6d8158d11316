<?php

defined('ABSPATH') || exit;

function include_custom_post_type_in_search($query)
{
    if (!is_admin() && $query->is_search) {
        $query->set('post_type', ['online-vod']);
    }

    return $query;
}

class MembersOnly
{
    public static function init()
    {
        add_action('template_redirect', [__CLASS__, 'restrict_access']);
//		add_filter( 'query_vars', [ __CLASS__, 'add_query_vars' ] );
//		add_action( 'init', [ __CLASS__, 'add_custom_route' ] );
    }

    public static function add_custom_route()
    {
        add_rewrite_rule('^user/dashboard/?$', 'index.php?user_dashboard=1', 'top');
        add_rewrite_rule('^user/?$', 'index.php?user=1', 'top');
        flush_rewrite_rules(); // Ensure rewrite rules are flushed
    }

    public static function add_query_vars($vars)
    {
        $vars[] = 'user';
        $vars[] = 'user_dashboard';

        return $vars;
    }

    public static function restrict_access()
    {
        if (is_singular('online-vod')) {
            if (!is_user_logged_in()) {
                wp_redirect(get_permalink(22));
                exit;
            }
            $is_allowed = MembersOnly::is_allowed(get_the_ID());
            if (!$is_allowed) {
                wp_redirect(get_home_url() . '/user');
                die;
            }
        }

        if (is_page(['user', 'members-only-page-2']) && !is_user_logged_in()) {
            wp_redirect(get_permalink(22));
            die;
        } elseif (get_query_var('s')) {
        } elseif (get_query_var('user_dashboard')) {
            include_once get_template_directory() . '/classes/members/user/dashboard.php';
            die;
        } elseif (is_page(7593) && is_user_logged_in()) {
            include_once get_template_directory() . '/classes/members/user/user.php';
            die;
        }
    }

    public static function getMemberTerms($get_current_user_id)
    {

        $user = get_userdata($get_current_user_id);
        $user_topics = get_field('restricted_topics', 'user_' . $user->ID);
        $args = [
            'number' => 50,
            'taxonomy' => 'topics',
            'hide_empty' => false,
            'orderby' => 'meta_value_num priority',
            'order' => 'ASC',
            'meta_query' => [
                'relation' => 'OR',
                [
                    'key' => 'priority',
                    'compare' => 'EXISTS'
                ],
                [
                    'key' => 'priority',
                    'compare' => 'NOT EXISTS'
                ]
            ],
        ];

        if (!empty($user_topics['limited_topics']) && is_array($user_topics['limited_topics']) && count($user_topics['limited_topics']) > 0) {
            $user_topics = $user_topics['limited_topics'];
            $args['include'] = $user_topics;
        }

        return get_terms($args);
    }

    public static function is_allowed(bool|int $get_the_ID)
    {
        $terms = wp_get_post_terms($get_the_ID, 'topics', ['fields' => 'ids']);
        if (!is_user_logged_in()) {
            return false;
        }

        $user_terms = self::getMemberTerms(get_current_user_id());
        if (!empty($user_terms) && !empty($terms)) {
            $intersect = array_intersect($terms, wp_list_pluck($user_terms, 'term_id'));
            if (!empty($intersect)) {
                return true;
            }
        }

        return false;
    }
}

add_action('after_setup_theme', ['MembersOnly', 'init']);

add_action('after_switch_theme', function () {
    MembersOnly::add_custom_route();
    flush_rewrite_rules();
});

add_action('switch_theme', function () {
    flush_rewrite_rules();
});