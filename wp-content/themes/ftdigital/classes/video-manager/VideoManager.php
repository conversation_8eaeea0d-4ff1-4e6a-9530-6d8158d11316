<?php

class VideoManager
{

    const KEY = '43x8AGqmjlKBpiQ14nKbkJcsIaiqGlmZ4ZFdtv3DoMM',
        API_URL = 'https://ws.api.video';

    const DEBUG_KEY = 'ZwNU8hplHBcfNyWrt42sCmL2oYVGDlJZesNHWS86s0n',
        DEBUG_API_URL = 'https://sandbox.api.video';

    private static $client;
    public static $terms;

    public static function init()
    {
        self::$client = new \ApiVideo\Client\Client(
            self::API_URL,
            self::KEY,
            new \Symfony\Component\HttpClient\Psr18Client()
        );

        add_action('init', [__CLASS__, 'videoManagerPosts']);
        add_action('init', [__CLASS__, 'videoManagerTaxonomy']);
        add_action('wp_ajax_ajax-main-videos', [__CLASS__, 'ajaxMainVideos']);
        add_action('wp_ajax_watchedVideo', [__CLASS__, 'userWatchedVideoList']);
        add_action('wp_ajax_load-video', [__CLASS__, 'loadVideo']);
        add_action('wp_ajax_fetch-video-thumbnails', [__CLASS__, 'fetchVideoThumbnails']);
        add_action('wp_ajax_nopriv_fetch-video-thumbnails', [__CLASS__, 'fetchVideoThumbnails']);
        add_action('save_post', [__CLASS__, 'setVideoInformation'], 100, 2);
    }

    public static function fetchVideoThumbnails()
    {
        if (empty($_POST['video_codes']) || !is_array($_POST['video_codes'])) {
            wp_send_json_error('Invalid video codes');
        }

        $video_codes = array_map('sanitize_text_field', $_POST['video_codes']);
        $thumbnails = [];

        foreach ($video_codes as $code) {
            if ($code) {
                $video = self::$client->videos()->get($code);
                $status = self::$client->videos()->getStatus($code);
                $duration = gmdate('i:s', $status->getEncoding()->getMetadata()->getDuration());
                $thumbnails[$code] = [
                    'thumbnail' => $video->getAssets()->getThumbnail(),
                    'duration' => $duration
                ];
            }
        }

        wp_send_json_success($thumbnails);
    }

    public static function setVideoInformation($post_id, $data)
    {
        $client = self::$client;
        if ($data->post_type === "online-vod" && in_array($data->post_status, [
                'publish',
                'draft',
                'pending'
            ])) {

            if ($code = get_field('api_video_code', $post_id)) {
                try {
                    /*$is_private = get_field( 'private-video', $post_id );
                    $client->videos()->update( $code, ( new \ApiVideo\Client\Model\VideoUpdatePayload() )
                        ->setPublic( (bool) $is_private )->setMp4Support( (bool) $is_private ) );*/
                    $is_private = get_field('private-video', $post_id);
                    $client->videos()->update($code, (new \ApiVideo\Client\Model\VideoUpdatePayload())
                        ->setPublic(false)->setMp4Support(false));

                    $video = $client->videos()->get($code);
                    update_field('api_video_assets', $video->jsonSerialize(), $post_id);
                    $attachment_id = upload_image_from_url($video->getAssets()->getThumbnail(), $post_id);
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                    }
                } catch (Exception $exception) {
                }
            }
        }

        return true;
    }

    public static function loadVideo()
    {

        if (empty($_POST['video'])) {
            wp_send_json_error('Invalid Video Code');
        }

        $video = sanitize_text_field($_POST['video']);
        if (is_numeric($video) && $video > 0) {
            $apiVideo = trim(get_field('api_video_code', (int)$video));
        } else {
            $apiVideo = $video; // Direct api_video_code
        }
        if ($apiVideo) {
            $videoData = self::getVideoData($apiVideo);
            if ($videoData) {
                wp_send_json_success($videoData);
            }
        }
        wp_send_json_error('Video not found.');
    }

    public static function getVideoData($video_id)
    {
//        dd($video_id);
        try {

            $videoHsl = self::$client->videos()->get($video_id);
            $player = $videoHsl->getAssets()->getPlayer();
            parse_str($player, $token);
            $token = reset($token);
            if (!$token) {
                return [
                    'hls_url' => $videoHsl->getAssets()->getHls(),
                    'thumbnail' => $videoHsl->getAssets()->getThumbnail(),
                    'player' => $videoHsl->getAssets()->getPlayer(),
                    'iframe' => $videoHsl->getAssets()->getIframe(),
                    'mp4' => $videoHsl->getAssets()->getMp4(),
                    'video_id' => $video_id,
                    'token' => $token,
                ];
            }

            $ch = curl_init('https://vod.api.video/vod/' . $video_id . '/token/' . $token . '/session');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $result = curl_exec($ch);
            if (!curl_error($ch)) {
                $session = @json_decode($result);
                if (!empty($session->session_token)) {
                    return [
                        'hls_url' => $videoHsl->getAssets()->getHls() . '?avh=' . $session->session_token,
                        'thumbnail' => $videoHsl->getAssets()->getThumbnail() . '?avh=' . $session->session_token,
                        'player' => $videoHsl->getAssets()->getPlayer() . '&avh=' . $session->session_token,
                        'iframe' => $videoHsl->getAssets()->getIframe() . '&avh=' . $session->session_token,
                        'mp4' => $videoHsl->getAssets()->getMp4() . '?avh=' . $session->session_token,
                        'session' => $session->session_token,
                        'video_id' => $video_id,
                        'token' => $token,
                    ];
                }
            }
        } catch (\Exception $e) {
        }

        return null;
    }

    public static function userWatchedVideoList()
    {
        $post_id = sanitize_text_field($_POST['video_id']);
        if (!empty($post_id) && is_numeric($post_id) && $post_id > 0) {
            if ($post = get_post($post_id)) {
                delete_user_meta(get_current_user_id(), 'watched_videos', $post->ID);
                add_user_meta(get_current_user_id(), 'watched_videos', $post->ID);
                wp_send_json_success('list updated');
            }
        }
    }

    public static function is_video_watched($post_id)
    {
        $watchedList = get_user_meta(get_current_user_id(), 'watched_videos');

        return (is_array($watchedList) && in_array($post_id, $watchedList));
    }

    public static function ajaxMainVideos()
    {
        ob_start();
        $taxonomy = 'topics';
        $terms = MembersOnly::getMemberTerms(get_current_user_id());
        ?>
        <?php if (count($terms) > 0): ?>
        <?php foreach ($terms as $index => $term):
            $term_link = get_term_link($term->term_id, $taxonomy);
            ?>
            <div class="col-12 px-0">
                <h2 class="display-3 text-center fw-bold mb-2"><?= $term->name; ?></h2>
                <div class="SessionSlider swiper-container overflow-hidden position-relative py-4 px-3 px-sm-0">
                    <div class="swiper-wrapper">
                        <?php
                        $args = [
                            'post_type' => 'online-vod',
                            'posts_per_page' => 6,
                            'orderby' => 'ID',
                            'order' => 'DESC',
                            'tax_query' => [
                                [
                                    'taxonomy' => 'topics',
                                    'field' => 'term_id',
                                    'terms' => $term->term_id,
                                    'include_children' => false
                                ]
                            ]
                        ];

                        $q = new WP_Query($args);
                        $videos = $q->get_posts();
                        wp_reset_query();
                        ?>
                        <?php if (count($videos) > 0): ?>
                            <?php foreach ($videos as $key => $video):
                                $apiVideo = trim(get_field('api_video_code', $video->ID));
                                if (!$apiVideo) {
                                    continue;
                                }
                                $icon = self::is_video_watched($video->ID);
                                $thumbnail = get_the_post_thumbnail_url($video->ID, 'full');
                                ?>
                                <div class="swiper-slide">
                                    <div class="VideoPlayBox shadow-md bg-white">
                                        <div class="VideoPlayer">
                                            <a href="<?= esc_attr(get_permalink($video->ID)); ?>">
                                                <img src="<?= get_template_directory_uri(); ?>/assets/images/playpause-color.svg"
                                                     alt="Play Pause Icon"
                                                     data-video="<?= $video->ID; ?>"
                                                     class="play-pause-icon z-3">
                                                <?php if ($thumbnail): ?>
                                                    <img src="<?= esc_attr($thumbnail); ?>"
                                                         alt="Thumbnail"
                                                         class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
                                                <?php else: ?>
                                                    <img src="<?= get_template_directory_uri(); ?>/assets/images/pricing-bg.png"
                                                         alt="Thumbnail"
                                                         class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
                                                <?php endif; ?>
                                            </a>
                                        </div>
                                        <div class="px-4 pb-4 pt-2">
                                            <a href="<?= esc_attr(get_permalink($video->ID)); ?>" class="text-dark text-decoration-none video-link">
                                                <h5 class="fs-4 fw-bold mb-1"><?= get_field('video_name', $video->ID); ?></h5>
                                                <p class="content"><?= get_field('trainer_name', $video->ID); ?></p>
                                            </a>
                                            <div class="d-flex gap-4 justify-content-between external-watch">
                                                <a href="#" data-video="<?= $video->ID; ?>" data-external="true"
                                                   class="readmore watch-video-external-link">
                                                    <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/new-tab.svg"
                                                         alt="New Tab">
                                                    <span>צפייה בחלונית נפרדת</span>
                                                </a>
                                                <div class="d-flex watched-wrap">
                                                    <!--                                                        <span class="fs-4 lh-1 fw-bold mx-2">00:32</span>-->
                                                    <?php if ($icon): ?>
                                                        <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/video.svg"
                                                             alt="Video Camera Icon" class="img-fluid">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if (!empty($apiVideo)): ?>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <div class="swiper-button-next">
                        <svg width="70" height="70" viewBox="0 0 70 70" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                                  fill="white"/>
                        </svg>
                    </div>
                    <div class="swiper-button-prev">
                        <svg width="70" height="70" viewBox="0 0 70 70" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                                  fill="white"/>
                        </svg>
                    </div>
                    <div class="swiper-pagination position-static my-lg-3 my-4"></div>
                </div>
                <a href="<?= esc_attr($term_link); ?>"
                   class="btn btn-link view-all-link mx-auto text-center d-block"><span>לכל האימונים</span>
                    &#187;</a>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
        <?php

        header('Content-Type: text/html; charset=utf8');
        echo ob_get_clean();
        die;
    }

    public static function videoManagerTaxonomy()
    {
        $labels = [
            'name' => _x('Topics', 'ebtech'),
            'singular_name' => _x('Topic', 'ebtech'),
            'search_items' => __('Search Topics'),
            'popular_items' => __('Popular Topics'),
            'all_items' => __('All Topics'),
            'parent_item' => null,
            'parent_item_colon' => null,
            'edit_item' => __('Edit Topic'),
            'update_item' => __('Update Topic'),
            'add_new_item' => __('Add New Topic'),
            'new_item_name' => __('New Topic Name'),
            'separate_items_with_commas' => __('Separate topics with commas'),
            'add_or_remove_items' => __('Add or remove topics'),
            'choose_from_most_used' => __('Choose from the most used topics'),
            'menu_name' => __('Topics'),
        ];

        register_taxonomy('topics', ['online-vod'], [
            'hierarchical' => true,
            'labels' => $labels,
            'show_ui' => true,
            'show_in_rest' => true,
            'show_admin_column' => true,
            'update_count_callback' => '_update_post_term_count',
            'query_var' => true,
            'rewrite' => ['slug' => 'topic', 'with_front' => true],
            'public' => true,
            "show_in_menu" => true,
            'show_in_nav_menus' => true,
        ]);

    }

    public static function videoManagerPosts()
    {
        $labels = [
            "name" => __("Private VOD", 'video-manager'),
            "singular_name" => __("Private VOD", 'video-manager'),
            "menu_name" => __("Private VOD", 'video-manager'),
            "all_items" => __("All Videos", 'video-manager'),
            "add_new" => __("New Video", 'video-manager'),
            "add_new_item" => __("Add New Video", 'video-manager'),
            "edit_item" => __("Edit Video", 'video-manager'),
            "new_item" => __("New Video", 'video-manager'),
            "view_item" => __("View Video", 'video-manager'),
            "view_items" => __("View Videos", 'video-manager'),
        ];
        $args = [
            "label" => __("Private VOD", 'video-manager'),
            "labels" => $labels,
            "description" => "",
            "public" => true,
            "publicly_queryable" => true,
            "show_ui" => true,
            "show_in_rest" => true,
            "rest_base" => "",
            "has_archive" => true,
            "show_in_menu" => true,
            "show_in_nav_menus" => true,
            "rest_controller_class" => "WP_REST_Posts_Controller",
            "exclude_from_search" => false,
            "capability_type" => "post",
            "map_meta_cap" => true,
            "menu_icon" => 'dashicons-format-video',
            "hierarchical" => true,
            "rewrite" => ["slug" => "online-vod", "with_front" => true],
            "query_var" => true,
            "supports" => [
                'title',
                'thumbnail',
            ],
        ];
        register_post_type("online-vod", $args);
    }
}

VideoManager::init();