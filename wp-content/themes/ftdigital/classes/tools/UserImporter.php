<?php

class UserImporter {
	private static $file_path;

	public static function init() {
		add_action( 'template_redirect', [ __CLASS__, 'import_users' ] );
	}

	public static function set_file_path( $file_path ) {
		self::$file_path = $file_path;
	}

	public static function import_users() {
		if ( ! isset( $_GET['import_users'] ) ) {
			return;
		}

		if ( ! file_exists( self::$file_path ) || ! is_readable( self::$file_path ) ) {
			return false;
		}

		$file = new SplFileObject( self::$file_path );
		$file->setFlags( SplFileObject::READ_CSV );
		$header = null;
		$data   = [];

		foreach ( $file as $row ) {
			if ( ! $header ) {
				$header = $row;
			} else {
				if ( count( $header ) != count( $row ) ) {
					continue;
				}
				$data[] = array_combine( $header, $row );
			}
		}

		foreach ( $data as $row ) {
			if ( ! empty( $row['role'] ) && $row['role'] == 'administrator' ) {
				continue;
			}
			$user_data = [
				'user_login' => $row['user_login'],
				'user_pass'  => wp_generate_password( 12, true, true ), // Generate a random strong password
				'user_email' => $row['email'],
				'first_name' => $row['first_name'],
				'nickname'   => $row['nickname'],
				'last_name'  => $row['last_name'],
				'role'       => $row['role'],
			];


			$user_id = wp_insert_user( $user_data );

			if ( is_wp_error( $user_id ) ) {
				echo 'Error importing user: ' . $user_data['user_login'] . ' - ' . $user_id->get_error_message() . '<br>';
			} else {
				update_field( 'user_plan', [ 7621 ], 'user_' . $user_id );
				echo 'Successfully imported user: ' . $user_data['user_login'] . '<br>';
			}
		}
		echo 'Import completed';
		die;
	}
}

//UserImporter::set_file_path( __DIR__ . '/users20241215112935.csv' );
//UserImporter::init();