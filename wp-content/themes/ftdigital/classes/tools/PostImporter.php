<?php
function remove_all_online_vod_posts()
{
    $args = [
        'post_type' => 'online-vod',
        'posts_per_page' => -1,
        'post_status' => 'any',
    ];

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            wp_delete_post(get_the_ID(), true);
        }
    }

    wp_reset_postdata();
}

// Call the function to remove all posts
function upload_image_from_url($image_url, $post_id)
{
    // Check if the URL is valid
    if (!filter_var($image_url, FILTER_VALIDATE_URL)) {
        return new WP_Error('invalid_url', 'Invalid image URL');
    }

    // Download the image
    $image_data = file_get_contents($image_url);
    if ($image_data === false) {
        return new WP_Error('download_failed', 'Failed to download image');
    }

    // Get the file name and extension
    $file_name = basename($image_url);
    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['path'] . '/' . $post_id . '-' . $file_name;
    $file_path = explode('?', $file_path)[0];
    $file_name = $post_id . '-' . explode('?', $file_name)[0];

    // Save the image to the uploads directory
    file_put_contents($file_path, $image_data);

    // Check the file type and get the correct mime type
    $file_type = wp_check_filetype($file_name, null);

    // Prepare an array of post data for the attachment
    $attachment = [
        'guid' => $upload_dir['url'] . '/' . $file_name,
        'post_mime_type' => $file_type['type'],
        'post_title' => sanitize_file_name($file_name),
        'post_content' => '',
        'post_status' => 'inherit'
    ];

    // Insert the attachment into the WordPress media library
    $attach_id = wp_insert_attachment($attachment, $file_path);

    // Include the image.php file to make the wp_generate_attachment_metadata function available
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    // Generate the metadata for the attachment and update the database record
    $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
    wp_update_attachment_metadata($attach_id, $attach_data);

    return $attach_id;
}

class PostImporter
{
    public static function init()
    {
//        add_action('admin_init', [__CLASS__, 'import_posts_from_csv']);
//        add_action('admin_init', [__CLASS__, 'iterate_vod_posts']);
    }

    public static function iterate_vod_posts()
    {
        if (isset($_GET['iterate_vod_postsdebug'])) {
            ini_set('max_execution_time', -1);
            ini_set('memory_limit', -1);

            $args = [
                'post_type' => 'online-vod',
                'posts_per_page' => -1,
                'order' => 'DESC',
                'orderby' => 'ID',
            ];

            $vod = new WP_Query($args);
            $r = [];
            foreach ($vod->get_posts() as $index => $post) {
                $has_thumbnail = has_post_thumbnail($post->ID);
                if ($has_thumbnail) {
                    continue;
                }

                $api_video_code = get_field('api_video_code', $post->ID);
                if (!$api_video_code) {
                    $r[] = $api_video_code;
                    continue;
                }

                $apiVideo = VideoManager::getVideoData($api_video_code);
                if (!$apiVideo) {
                    $r[] = $api_video_code;
//                    wp_delete_post($post->ID, true);
                }

                if (!empty($apiVideo['thumbnail'])) {
                    $attachment_id = upload_image_from_url($apiVideo['thumbnail'], $post->ID);
                    if ($attachment_id) {
                        set_post_thumbnail($post->ID, $attachment_id);
                    }
                }
            }
            dd('done thumbnails');
        }
    }

    public static function import_posts_from_csv()
    {
        if (isset($_GET['admindebugeliran'])) {

            ini_set('max_execution_time', -1);
            ini_set('memory_limit', -1);
            $file = __DIR__ . '/exported_posts.csv';

            if (!file_exists($file) || !is_readable($file)) {
                return false;
            }
            $fileObject = new SplFileObject($file);
            $fileObject->setFlags(SplFileObject::READ_CSV);
            $header = null;
            $data = [];

            foreach ($fileObject as $key => $row) {
                if (!$header) {
                    $header = $row;
                } else {
                    if (count($header) != count($row)) {
                        continue;
                    }
                    $data[] = array_combine($header, $row);
                }
            }

            remove_all_online_vod_posts();
//            dd('done remove');
//            dd($data);

            foreach ($data as $row) {
                $post_data = [
                    'post_title' => $row['post_title'],
                    'post_status' => 'publish',
                    'post_type' => 'online-vod',
                ];

                //video name is api code for some reason
                if (empty($row['video_name'])) {
                    continue;
                }

                $post_id = wp_insert_post($post_data);
                if (!$post_id) {
                    dd($row);
                }

                update_field('api_video_code', $row['video_name'], $post_id);
                if (!empty($row['terms'])) {
                    $topics = unserialize($row['terms']);
                    if (!empty($topics)) {
                        $topics = array_map(function ($topic) {
                            return urldecode($topic->slug);
                        }, $topics);

                        wp_set_object_terms($post_id, $topics, 'topics');
                    }
                }

                if (isset($row['api_video_code'])) {
                    update_field('video_name', $row['api_video_code'], $post_id);
                }
                if (isset($row['trainer_name'])) {
                    update_field('trainer_name', $row['trainer_name'], $post_id);
                }
                if (isset($row['accessory'])) {
                    update_field('accessory', $row['accessory'], $post_id);
                }
                if (isset($row['info'])) {
                    update_field('info', $row['info'], $post_id);
                }
                if (isset($row['video_duration'])) {
                    update_field('video_duration', $row['video_duration'], $post_id);
                }
            }
            dd('done');
        }
    }
}

PostImporter::init();