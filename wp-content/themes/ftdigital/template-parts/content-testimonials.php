<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$testimonials = get_field( 'testimonials', 'option' );
?>

<!-- Main Content : Starts  -->
<div class="container-fluid py-4 position-relative">
    <div class="row py-4 ">
	    <?php if ( ! empty( $testimonials['add-testimonials'] ) && is_array( $testimonials['add-testimonials'] ) ): ?>
		    <?php foreach ( $testimonials['add-testimonials'] as $add_testimonial ): ?>
                <div class="col-md-4">
                    <div class="testCard rounded-4">
                        <div class="mb-2 d-flex">
                            <div class="review">
                                <h3 class="fs-5 fw-bold mb-1"><?= $add_testimonial['name'] ?? ''; ?></h3>
                                <div class="d-flex">
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M10.3043 0.951172L12.6863 8.02042H20.3946L14.1584 12.3895L16.5404 19.4587L10.3043 15.0897L4.06809 19.4587L6.45009 12.3895L0.213913 8.02042H7.92226L10.3043 0.951172Z"
                                                fill="#F1C840"/>
                                    </svg>
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M10.3043 0.951172L12.6863 8.02042H20.3946L14.1584 12.3895L16.5404 19.4587L10.3043 15.0897L4.06809 19.4587L6.45009 12.3895L0.213913 8.02042H7.92226L10.3043 0.951172Z"
                                                fill="#F1C840"/>
                                    </svg>
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M10.3043 0.951172L12.6863 8.02042H20.3946L14.1584 12.3895L16.5404 19.4587L10.3043 15.0897L4.06809 19.4587L6.45009 12.3895L0.213913 8.02042H7.92226L10.3043 0.951172Z"
                                                fill="#F1C840"/>
                                    </svg>
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M10.3043 0.951172L12.6863 8.02042H20.3946L14.1584 12.3895L16.5404 19.4587L10.3043 15.0897L4.06809 19.4587L6.45009 12.3895L0.213913 8.02042H7.92226L10.3043 0.951172Z"
                                                fill="#F1C840"/>
                                    </svg>
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M10.3043 0.951172L12.6863 8.02042H20.3946L14.1584 12.3895L16.5404 19.4587L10.3043 15.0897L4.06809 19.4587L6.45009 12.3895L0.213913 8.02042H7.92226L10.3043 0.951172Z"
                                                fill="#F1C840"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <p class="content"><?= $add_testimonial['testimonial'] ?? ''; ?></p>
                    </div>
                </div>
		    <?php endforeach; ?>
	    <?php endif; ?>
        <div class="col-12 text-center">
            <a href="#" class="btn-hover color-2 xl show-plans-modal">הירשמי</a>
        </div>
    </div>
</div>
<!-- Offer Strip : Starts  -->
<div class="marquee-wrapper bggrad-light py-3">
    <div class="marquee">
        <div class="marquee-content">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
            <h2>סטודיו מקוון</h2>
            <img src="<?= get_template_directory_uri() ?>/assets/images/icons/heart.svg" alt="Heart">
        </div>
    </div>
</div>
<?php get_template_part( 'template-parts/content', 'our-benefits' ) ?>