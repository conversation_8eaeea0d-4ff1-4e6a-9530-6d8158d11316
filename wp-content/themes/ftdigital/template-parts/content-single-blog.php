<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

?>
<article class="singleBlog" id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
				<?php if ( has_post_thumbnail() ): ?>
					<?php the_post_thumbnail( 'full', array( 'class' => 'img-fluid rounded-4 my-3' ) ); ?>
				<?php endif; ?>
                <div>
					<?php the_content(); ?>
                </div>
            </div>
        </div>
    </div>
</article>
<div class="container py-5 mt-lg-5">
    <div class="row justify-content-center OurBlogs">
        <div class="col-12 text-center">
            <h2 class="display-3 fw-bold mb-md-5 mb-3"><?php echo esc_html( __( 'בלוגים קשורים', 'text-domain' ) ); ?></h2>
        </div>
        <div class="col-12 position-relative ">
            <div class="px-3 overflow-hidden">
                <div class="BlogDetailsSlider swiper-container py-2">
                    <div class="swiper-wrapper">
						<?php
						// Get current post data
						$current_id = get_the_ID();
						$current_tags = wp_get_post_tags($current_id, ['fields' => 'ids']);
						$current_cats = wp_get_post_categories($current_id);

						// Build relevance query
						$tax_query = ['relation' => 'OR'];
						if (!empty($current_tags)) {
							$tax_query[] = ['taxonomy' => 'post_tag', 'field' => 'term_id', 'terms' => $current_tags];
						}
						if (!empty($current_cats)) {
							$tax_query[] = ['taxonomy' => 'category', 'field' => 'term_id', 'terms' => $current_cats];
						}

						$related_blogs = new WP_Query([
							'post_type' => 'post',
							'posts_per_page' => 12,
							'post__not_in' => [$current_id],
							'tax_query' => count($tax_query) > 1 ? $tax_query : [],
							'orderby' => 'date',
							'order' => 'DESC'
						]);

						// Fallback if no related posts found
						if (!$related_blogs->have_posts()) {
							$related_blogs = new WP_Query([
								'post_type' => 'post',
								'posts_per_page' => 6,
								'post__not_in' => [$current_id],
								'orderby' => 'date',
								'order' => 'DESC'
							]);
						}

						if ($related_blogs->have_posts()) :
							while ($related_blogs->have_posts()) : $related_blogs->the_post();
								?>
                                <div class="swiper-slide">
									<?php get_template_part('template-parts/content', 'blog-box') ?>
                                </div>
							<?php
							endwhile;
							wp_reset_postdata();
						endif;
						?>
                    </div>
                </div>
            </div>
            <div class="swiper-button-next blogsliderbtn">
                <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                          fill="black"/>
                </svg>
            </div>
            <div class="swiper-button-prev blogsliderbtn">
                <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                          fill="black"/>
                </svg>
            </div>
        </div>
    </div>
</div>