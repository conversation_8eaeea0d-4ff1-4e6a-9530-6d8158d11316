<section class="container formSection">
    <div class="row">
        <div class="col-12">
            <form id="footer-contact-form" action="#" class="bg-white formSectionWrap shadow-md">
                <div class="text-center">
                    <svg width="60" height="53" viewBox="0 0 60 53" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M55.7143 16.1484C55.7143 14.2852 55.4743 12.6404 54.9944 11.2142C54.5145 9.78798 53.9007 8.65506 53.1529 7.81543C52.4051 6.9758 51.4955 6.29145 50.4241 5.76237C49.3527 5.23329 48.3036 4.87674 47.2768 4.69271C46.25 4.50868 45.1562 4.41667 43.9955 4.41667C42.8348 4.41667 41.5848 4.70996 40.2455 5.29655C38.9062 5.88314 37.673 6.61925 36.5458 7.50488C35.4185 8.39052 34.4531 9.21864 33.6496 9.98926C32.846 10.7599 32.1763 11.4672 31.6406 12.1113C31.2388 12.6174 30.692 12.8704 30 12.8704C29.308 12.8704 28.7612 12.6174 28.3594 12.1113C27.8237 11.4672 27.154 10.7599 26.3504 9.98926C25.5469 9.21864 24.5815 8.39052 23.4542 7.50488C22.327 6.61925 21.0938 5.88314 19.7545 5.29655C18.4152 4.70996 17.1652 4.41667 16.0045 4.41667C14.8437 4.41667 13.75 4.50868 12.7232 4.69271C11.6964 4.87674 10.6473 5.23329 9.57589 5.76237C8.50446 6.29145 7.59487 6.9758 6.8471 7.81543C6.09933 8.65506 5.48549 9.78798 5.00558 11.2142C4.52567 12.6404 4.28571 14.2852 4.28571 16.1484C4.28571 20.013 6.37277 24.0961 10.5469 28.3978L30 47.7207L49.4196 28.4323C53.6161 24.1076 55.7143 20.013 55.7143 16.1484ZM60 16.1484C60 21.2322 57.4442 26.408 52.3326 31.6758L31.4732 52.3789C31.0714 52.793 30.5804 53 30 53C29.4196 53 28.9286 52.793 28.5268 52.3789L7.63393 31.6068C7.41071 31.4227 7.10379 31.1237 6.71317 30.7096C6.32254 30.2956 5.70312 29.5422 4.85491 28.4495C4.0067 27.3569 3.24777 26.2355 2.57812 25.0853C1.90848 23.9351 1.31138 22.5434 0.78683 20.9102C0.262277 19.2769 0 17.6897 0 16.1484C0 11.0877 1.41741 7.13108 4.25223 4.27865C7.08705 1.42622 11.0045 0 16.0045 0C17.3884 0 18.8002 0.247287 20.24 0.741862C21.6797 1.23644 23.019 1.90354 24.2578 2.74316C25.4967 3.58279 26.5625 4.37066 27.4554 5.10677C28.3482 5.84288 29.1964 6.625 30 7.45312C30.8036 6.625 31.6518 5.84288 32.5446 5.10677C33.4375 4.37066 34.5033 3.58279 35.7422 2.74316C36.981 1.90354 38.3203 1.23644 39.76 0.741862C41.1998 0.247287 42.6116 0 43.9955 0C48.9955 0 52.9129 1.42622 55.7478 4.27865C58.5826 7.13108 60 11.0877 60 16.1484Z" fill="black"/>
                    </svg>
                    <h3 class="mb-0 title">רוצה לשמוע פרטים נוספים?</h3>
                    <p class="mb-0 fw-semibold">מלאי את השדות למטה ואנו נחזור אלייך בהקדם האפשרי</p>
                    <div class="row mt-lg-5 mt-3">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <input type="text" class="form-control rounded-pill text-start" id="fullname-footer" name="fullname" placeholder="שם מלא">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <input type="tel" class="form-control rounded-pill text-start" id="phone-footer" name="phone" placeholder="מספר טלפון">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <input type="email" class="form-control rounded-pill text-start" id="email-footer" name="email" placeholder="כתובת אימייל">
                        </div>
                    </div>
                    <div class="row mt-md-3 mt-1 pt-1">
                        <div class="col-12">
                            <div class="form-check">
                                <input id="receiveemails-footer" name="receiveemails" type="checkbox" value="yes">
                                <label for="receiveemails-footer">אני מאשר/ת קבלת אימיילים.</label>
                            </div>
                            <div class="form-check">
                                <input id="receiptsms-footer" name="receiptsms" type="checkbox" value="yes">
                                <label for="receiptsms-footer">אני מאשר/ת קבלת סמסים.</label>
                            </div>
                            <div class="form-check mt-2 ps-1">
                                <label>ע״י הרשמתך, את/ה נותנ/ת את הסכמתך על תנאי השימוש וכי קראת את מדיניות פרטיות.</label>
                            </div>
                        </div>
                    </div>
                    <div class="row text-start mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn-hover color-2 px-5 fs-3">שליחה</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
    jQuery(document).ready(function($) {
        $('#footer-contact-form').on('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            $('.border-danger').removeClass('border-danger');

            // Validate inputs
            let errors = [];
            const fullname = $('#fullname-footer').val().trim();
            const email = $('#email-footer').val().trim();
            const phone = $('#phone-footer').val().trim();
            const receiveemails = $('#receiveemails-footer').is(':checked');
            const receiptsms = $('#receiptsms-footer').is(':checked');

            if (!fullname) {
                errors.push('שם מלא נדרש');
                $('#fullname-footer').addClass('border-danger');
            }
            if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                errors.push('כתובת אימייל לא תקינה');
                $('#email-footer').addClass('border-danger');
            }
            if (!phone || !/^\d{9,12}$/.test(phone.replace(/\D/g, ''))) {
                errors.push('מספר טלפון לא תקין');
                $('#phone-footer').addClass('border-danger');
            }
            if (!receiveemails && !receiptsms) {
                errors.push('יש לאשר לפחות אחת מהאפשרויות: קבלת אימיילים או סמסים');
                $('#receiveemails-footer, #receiptsms-footer').addClass('border-danger');
            }

            if (errors.length) {
                Toastify({
                    text: errors.join('\n'),
                    duration: 3000,
                    gravity: 'top',
                    position: 'right',
                    backgroundColor: '#dc3545',
                }).showToast();
                return;
            }

            // AJAX submission
            $.ajax({
                url: EBUrls.ajaxurl,
                type: 'POST',
                data: {
                    action: 'handle_contact_form',
                    nonce: '<?= wp_create_nonce('contact_form_nonce') ?>',
                    fullname: fullname,
                    email: email,
                    phone: phone,
                    receiveemails: receiveemails ? 'yes' : 'no',
                    receiptsms: receiptsms ? 'yes' : 'no'
                },
                success: function(response) {
                    if (response.success) {
                        Toastify({
                            text: 'ההודעה נשלחה בהצלחה!',
                            duration: 3000,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#28a745',
                        }).showToast();
                        $('#footer-contact-form')[0].reset();
                    } else {
                        Toastify({
                            text: response.data.message || 'שגיאה בשליחת ההודעה',
                            duration: 3000,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#dc3545',
                        }).showToast();
                    }
                },
                error: function() {
                    Toastify({
                        text: 'שגיאה בהתחברות לשרת',
                        duration: 3000,
                        gravity: 'top',
                        position: 'right',
                        backgroundColor: '#dc3545',
                    }).showToast();
                }
            });
        });
    });
</script>