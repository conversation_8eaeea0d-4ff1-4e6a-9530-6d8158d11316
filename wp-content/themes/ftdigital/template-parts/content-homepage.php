<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

global $plans;

$plans = get_posts([
    'post_type' => 'studio-subscribers',
    'posts_per_page' => -1,
]);

usort($plans, function ($a, $b) {
    $priceA = get_field('recurring_info', $a->ID)['total_price'] ?? 0;
    $priceB = get_field('recurring_info', $b->ID)['total_price'] ?? 0;

    return $priceB - $priceA;
});

$highest_priced_plan = array_shift($plans);

$middle_index = floor(count($plans) / 2);
array_splice($plans, $middle_index, 0, [$highest_priced_plan]);

$consulting = get_field('free_consulting');
$experience_training = get_field('experience_training');
$experience_training2 = get_field('experience_training_2');
$experience_training3 = get_field('experience_training_3');
$about = get_field('about_section');
$example_training = get_field('example_training');
$ourTraining = get_field('video_gallery', 8643);
$latest_blogs = get_field('latest_blogs');
$experience_training_text = get_field('experience_training_text');

$data = [];
$data['experience_training'] = [];

if ($experience_training) {
    $icon = VideoManager::is_video_watched($experience_training->ID);
    $thumbnail = get_the_post_thumbnail_url($experience_training->ID, 'full');
    $data['experience_training'] = [
        'thumbnail' => $thumbnail,
        'icon' => $icon,
        'ID' => $experience_training->ID,
    ];
}

if ($experience_training2) {
    $icon = VideoManager::is_video_watched($experience_training2->ID);
    $thumbnail = get_the_post_thumbnail_url($experience_training2->ID, 'full');
    $data['experience_training2'] = [
        'thumbnail' => $thumbnail,
        'icon' => $icon,
        'ID' => $experience_training2->ID,
    ];
}

if ($experience_training3) {
    $icon = VideoManager::is_video_watched($experience_training3->ID);
    $thumbnail = get_the_post_thumbnail_url($experience_training3->ID, 'full');
    $data['experience_training3'] = [
        'thumbnail' => $thumbnail,
        'icon' => $icon,
        'ID' => $experience_training3->ID,
    ];
}
$blog_page_url = get_permalink(get_option('page_for_posts'));
$latest_posts = new WP_Query([
    'posts_per_page' => 4,
    'post_status' => 'publish',
]);

$commercial_banner = get_field('marketing_banner_and_register');
if (!empty($commercial_banner['video'])) {
    $c_video_id = get_field('api_video_code', $commercial_banner['video']->ID);
    $c_video = VideoManager::getVideoData($c_video_id ?? 0);
    if ($c_video) {
        $commercial_banner['video']->video_data = $c_video;
    }
}
$marketing_content = get_field('marketing_content', 'option');
$main_top_banner = get_field('main_top_banner');

?>

<section class="Banner">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-12 px-0 overflow-hidden">
                <div class="swiper-container hero-swiper position-relative">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide bg-1 d-flex align-items-center">
                            <div class="container">
                                <div class="row h-100">
                                    <div class="col-lg-6 order-last order-lg-first align-self-end">
                                        <!--<img src="<?php /*= get_template_directory_uri(); */ ?>/assets/images/banner/hero-1.png"
                                             alt="Slide 1 Image"
                                             class="img-fluid ps-lg-5">-->
                                    </div>
                                    <div class="col-lg-6 align-self-center">
                                        <div class="content text-white text-md-end text-center">
                                            <span class="subtitle mb-3"><?= $main_top_banner['small_title'] ?? ''; ?></span>
                                            <h2 class="title"><?= $main_top_banner['title'] ?? ''; ?></h2>
                                            <a href="#" class="btn-hover color-1 xl mt-lg-5 show-plans-modal">להרשמה</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="bgLadyImage position-relative overflow-hidden">
    <img src="<?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt="Heart Icon"
         class="heartIcon">
    <section class="container py-5 my-lg-5" id="about">
        <div class="row">
            <div class="col-md-6 pe-md-4 order-last order-md-first">
                <article class="entry-content">
                    <?= $about['content'] ?? ''; ?>
                </article>
                <button class="btn-hover color-2 xl show-plans-modal">להרשמה</button>
            </div>
            <div class="col-md-6  ps-md-4 mb-4">
                <h2 class="title mb-4"><?= esc_attr($about['title'] ?? ''); ?></h2>
                <img src="<?= esc_attr($about['main_image']['url']); ?>"
                     width="<?= esc_html($about['main_image']['width']); ?>"
                     height="<?= esc_html($about['main_image']['height']); ?>"
                     alt="<?= esc_html($about['main_image']['alt']); ?>"
                     class="img-fluid rounded-4">
            </div>
        </div>
    </section>
</div>
<?php get_template_part('template-parts/content', 'infinite-scroll'); ?>

<section class="serviceSliderSec overflow-hidden py-5 position-relative">
    <svg width="527" height="197" viewBox="0 0 527 197" fill="none" xmlns="http://www.w3.org/2000/svg"
         class="position-absolute top-0 ">
        <path
                d="M112.841 142.657C114.516 142.103 116.192 141.549 117.681 140.995C139.832 132.132 161.982 123.454 184.133 114.591C237.555 93.3567 290.978 71.9377 344.4 50.8879C364.317 42.9484 384.42 35.5624 405.081 29.6537C414.016 27.0687 423.137 24.853 432.259 23.5604C442.684 22.0833 452.919 24.4837 462.226 29.4691C469.672 33.5313 472.465 40.3633 469.858 48.4876C467.809 55.1348 464.089 60.6744 458.133 64.367C452.174 67.8756 446.218 71.3837 440.077 74.1535C424.255 81.1699 408.246 87.8171 392.238 94.4642C367.295 104.62 342.166 114.591 317.037 124.746C299.354 131.947 281.857 139.887 265.29 149.304C258.217 153.366 251.516 157.983 244.815 162.414C242.209 164.261 239.789 166.661 237.183 169.061C238.858 170.169 240.347 169.615 241.464 169.246C248.165 167.4 255.052 165.738 261.753 163.707C282.787 156.875 303.077 148.566 323.18 139.518C378.091 114.96 432.817 90.0329 487.542 65.2905C492.566 63.0747 497.409 60.6744 502.619 58.643C505.971 57.3506 509.506 56.4275 512.858 56.0579C518.813 55.504 524.21 58.2738 526.073 62.5207C528.307 67.8756 526.259 72.4917 522.538 76.369C519.558 79.508 515.834 81.5391 512.113 83.201C481.959 97.0493 451.987 110.898 421.647 124.562C382.745 142.103 344.027 160.198 304.38 176.078C287.069 183.095 269.757 189.742 251.702 194.173C245.187 195.835 238.486 197.312 231.599 196.943C228.248 196.758 224.898 196.389 221.919 195.281C212.054 191.957 206.284 182.91 207.214 172.57C207.586 167.4 209.448 162.783 212.24 158.352C215.218 153.551 218.941 149.489 223.222 145.796C230.296 139.333 238.3 134.163 246.676 129.362C268.082 116.991 290.605 106.836 313.501 97.6032C345.516 84.6781 377.533 71.7529 409.55 58.8277C412.898 57.5354 416.437 55.8735 419.785 54.3964C420.53 54.0269 421.275 53.6577 421.834 52.5497C420.158 52.9189 418.485 53.2885 416.809 53.8425C396.147 59.7509 375.857 67.1368 355.755 75.0767C304.752 95.203 253.935 115.514 202.933 135.825C177.432 145.981 152.117 156.321 126.615 166.107C116.564 169.985 106.14 172.939 95.7162 176.078C90.8765 177.555 86.0369 178.109 81.011 177.555C66.1199 175.893 59.2328 160.937 67.9814 148.75C69.6567 146.534 71.7041 144.503 73.7514 142.657C82.5 134.532 91.9936 127.331 102.045 120.684C118.798 109.605 136.295 99.6343 154.35 90.7717C185.994 75.2611 217.824 59.9357 249.654 44.6103C255.611 41.6556 261.381 38.7014 267.338 35.5624C268.454 35.0085 269.385 34.2699 270.502 33.716C270.316 33.5313 270.316 33.162 270.13 32.9774C268.454 33.3467 266.779 33.9006 265.104 34.2699C239.975 41.6556 215.777 51.4418 191.579 61.4128C143.741 81.1699 97.5778 103.512 51.787 126.777C43.5967 131.024 35.0344 134.902 26.6581 138.779C24.0521 140.072 21.26 140.995 18.4679 141.734C15.3036 142.657 11.953 142.657 8.6025 141.364C3.76283 139.518 0.598446 135.825 0.0400239 130.655C-0.332257 125.485 1.90143 121.423 6.36881 118.653C7.11335 118.099 7.85792 117.73 8.78861 117.36C22.1907 112.56 34.6621 105.728 47.1336 99.4499C96.0883 74.5227 145.788 51.0726 196.604 30.3923C220.43 20.6061 244.629 11.7431 269.571 5.28051C279.251 2.69547 288.93 0.664371 298.982 0.110432C303.263 -0.0742134 307.73 -0.0742134 312.011 0.479723C314.803 0.849015 317.782 1.77224 320.388 3.06476C328.95 7.68091 331.742 17.6518 326.344 25.7762C324.483 28.7305 321.877 31.5002 319.085 33.5313C312.57 38.3321 306.055 43.3175 298.982 47.3797C282.043 56.7967 265.104 66.2137 247.793 74.7071C217.08 90.0329 186.18 104.435 155.281 119.576C142.251 126.039 129.594 133.055 116.75 139.703C115.447 140.441 114.144 141.18 112.841 142.103C112.841 142.288 112.841 142.472 112.841 142.657Z"
                fill="#EEB9B9" fill-opacity="0.1"/>
    </svg>
    <h3 class="title text-center mb-4"><?= $example_training['title'] ?? ''; ?></h3>
    <div class="position-relative">
        <?php if (!empty($ourTraining['video']) && is_array($ourTraining['video'])): ?>
            <div class="swiper serviceSlider pb-lg-5 mb-lg-3 px-3">
                <div class="swiper-wrapper align-items-center">
                    <?php foreach ($ourTraining['video'] as $video): ?>
                        <div class="swiper-slide">
                            <div class="bg-1 cardSlide"
                                 data-video-code="<?php echo esc_attr($video['api_video_code'] ?? ''); ?>">
                                <a href="#" data-fancybox="gallery">
                                    <h4 class="sTitle"><?php echo esc_html($video['video_title'] ?? ''); ?></h4>
                                    <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/placeholder.jpg'); ?>"
                                         class="img-fluid thumbnail-img"
                                         alt="Video Thumbnail">
                                </a>
                                <a href="#" class="btn read-more">קרא עוד</a>
                                <div class="video-description">
                                    <?php echo esc_html($video['video_description'] ?? ''); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        <div class="swiper-button-next d-block d-lg-none">
            <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                      fill="white" stroke="white" stroke-width="2"/>
            </svg>
        </div>
        <div class="swiper-button-prev d-block d-lg-none">

            <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                      fill="white" stroke="white" stroke-width="2"/>
            </svg>
        </div>
    </div>
</section>


<div class="bgImgSec position-relative overflow-hidden">
    <img src=" <?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt=""
         class="position-absolute z-n1 end-0">
    <img src=" <?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt=""
         class="position-absolute z-n1 start-50 bottom-0">
    <img src=" <?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt=""
         class="position-absolute z-n1 start-0 top-0">
    <img src=" <?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt=""
         class="position-absolute z-n1 end-50 top-0">
    <?php if (!empty($consulting['title'])): ?>
        <section class="container py-lg-5">
            <div class="row justify-content-center py-lg-5 my-lg-5">
                <div class="col-md-6">
                    <?php if (!empty($consulting['image'])): ?>
                        <img src="<?= esc_attr($consulting['image']['url']); ?>"
                             alt="<?= esc_attr($consulting['image']['alt']); ?>"
                             width="<?= esc_attr($consulting['image']['width']); ?>"
                             height="<?= esc_attr($consulting['image']['height']); ?>"
                             class="img-fluid">
                    <?php endif; ?>
                </div>
                <div class="col-md-6 text-end  order-first order-lg-last">
                    <div class="text-lg-end text-center mb-5">
                        <h3 class="title"><?= $consulting['title'] ?? ''; ?></h3>
                        <p class="content"><?= $consulting['content'] ?? ''; ?></p>
                        <a href="#" class="btn-hover color-2 xl">קבל ייעוץ חינם של 15 דקות</a>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
</div>
<div class="container-fluid py-lg-5 pt-3 mb-4" id="experience_training">
    <div class="row py-lg-5 justify-content-center">
        <div class="col-lg ps-lg-5">
            <div class="VideoPlayer">
                <?php if (!empty($data['experience_training']['thumbnail'])):
                    $thumbnail = $data['experience_training']['thumbnail'];
                    ?>
                    <img src="<?= get_template_directory_uri(); ?>/assets/images/playpause.png"
                         data-video="<?= $data['experience_training']['ID']; ?>"
                         alt="Play Pause Icon"
                         class="play-pause-icon z-3">
                    <img src="<?= (esc_attr($thumbnail) ?: get_template_directory_uri() . '/assets/images/pricing-bg.png'); ?>"
                         alt="Thumbnail"
                         class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
                <?php endif; ?>
            </div>
        </div>
        <div class="col-lg-6 row mx-0 px-0 order-first order-lg-last mb-3 mb-lg-0">
            <div class="col-12 pb-5 bg-crunch">
                <div class="w-lg-75 pb-5 mb-5 ms-lg-4">
                    <h3 class="title"><?= $experience_training_text['title'] ?? ''; ?></h3>
                    <p class="content"><?= $experience_training_text['excerpt'] ?? ''; ?></p>
                    <a href="#" class="btn-hover color-2 xl show-plans-modal">להרשמה</a>
                </div>
            </div>
            <div class="col-12">
                <div class="row">
                    <div class="col videoFrame2">
                        <div class="VideoPlayer">
                            <?php if (!empty($data['experience_training2']['thumbnail'])):
                                $thumbnail = $data['experience_training2']['thumbnail'];
                                ?>
                                <img src="<?= get_template_directory_uri(); ?>/assets/images/playpause.png"
                                     alt="Play Pause Icon"
                                     data-video="<?= $data['experience_training2']['ID']; ?>"
                                     class="play-pause-icon z-3">
                                <img src="<?= (esc_attr($thumbnail) ?: get_template_directory_uri() . '/assets/images/pricing-bg.png'); ?>"
                                     alt="Thumbnail"
                                     class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col videoFrame2">
                        <div class="VideoPlayer">
                            <?php if (!empty($data['experience_training3']['thumbnail'])):
                                $thumbnail = $data['experience_training3']['thumbnail'];
                                ?>
                                <img src="<?= get_template_directory_uri(); ?>/assets/images/playpause.png"
                                     alt="Play Pause Icon"
                                     data-video="<?= $data['experience_training3']['ID']; ?>"
                                     class="play-pause-icon z-3">
                                <img src="<?= (esc_attr($thumbnail) ?: get_template_directory_uri() . '/assets/images/pricing-bg.png'); ?>"
                                     alt="Thumbnail"
                                     class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail object">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<section class="pricing overflow-hidden">
    <div class="container-fluid py-5 mb-lg-5">
        <div class="row justify-content-center">
            <div class="col-lg-5  py-5 text-white text-center">
                <h2 class="title">המסלולים שלנו</h2>
                <p class="subtitle2 "></p>
            </div>
        </div>
        <div class="row justify-content-center ">
            <div class="col-lg-9 px-5 px-lg-0  position-relative ">
                <div class="PricingSlider swiper-container overflow-hidden plans-swiper">
                    <div class="swiper-wrapper align-items-end">
                        <?php if (!empty($plans)): ?>
                            <?php foreach ($plans as $plan):
                                $recurring_info = get_field('recurring_info', $plan->ID);
                                ?>
                                <div class="swiper-slide">
                                    <div class="PricingSlide">
                                        <div class="px-4 pt-5">
                                            <?php if (!empty($recurring_info['new_customer_price'])): ?>
                                                <h2 class="title text-center">
                                                    <small>₪</small><?= $recurring_info['new_customer_price']; ?>
                                                </h2>
                                                <p class="content fw-bold">
                                                    <small>₪</small><?= $recurring_info['total_price'] ?? ''; ?>
                                                    ש״ח
                                                    החל מהחודש השני ואילך</p>
                                            <?php else: ?>
                                                <h2 class="title text-center">
                                                    <small>₪</small><?= $recurring_info['total_price'] ?? ''; ?></h2>
                                            <?php endif; ?>
                                            <p class="content fw-bold text-center"><?= $plan->post_title ?? ''; ?></p>
                                            <?php if (!empty($recurring_info['recurring_properties']) && is_array($recurring_info['recurring_properties'])): ?>
                                                <ul>
                                                    <?php foreach ($recurring_info['recurring_properties'] as $recurring_property): ?>
                                                        <li>
                                                            <img src="<?= get_template_directory_uri(); ?>/assets/images/check.svg"
                                                                 alt="Check">
                                                            <span><?= $recurring_property['property'] ?? ''; ?></span>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            <?php endif; ?>
                                        </div>
                                        <a href="#" class="btn-hover color-1 show-plans-modal">הירשמי עכשיו</a>
                                        <span class="topoffer">מבצע!</span>
                                        <?php if (!empty($recurring_info['new_customer_price'])): ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php if (!empty($plans) && count($plans) > 3): ?>
                    <div class="swiper-button-next pricingSliderBtn">
                        <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                                  fill="white"/>
                        </svg>
                    </div>
                    <div class="swiper-button-prev pricingSliderBtn">
                        <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M2 35C2 53.2254 16.7746 68 35 68C53.2254 68 68 53.2254 68 35C68 16.7746 53.2254 2 35 2C16.7746 2 2 16.7746 2 35ZM35 0C15.67 0 0 15.67 0 35C0 54.33 15.67 70 35 70C54.33 70 70 54.33 70 35C70 15.67 54.33 0 35 0ZM16.5 34.7969C16.5 33.9684 17.1716 33.2969 18 33.2969H49.1756L41.9393 26.0607C41.3536 25.4749 41.3536 24.5251 41.9393 23.9393C42.5251 23.3536 43.4749 23.3536 44.0607 23.9393L53.8607 33.7393C54.4464 34.3251 54.4464 35.2749 53.8607 35.8607L44.0607 45.6607C43.4749 46.2464 42.5251 46.2464 41.9393 45.6607C41.3536 45.0749 41.3536 44.1251 41.9393 43.5393L49.1818 36.2969H18C17.1716 36.2969 16.5 35.6253 16.5 34.7969Z"
                                  fill="white"/>
                        </svg>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<section class="my-5 ">
    <div class="VideoSection mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h2 class="text-center title">
                        <svg width="181" height="68" viewBox="0 0 181 68" fill="none" class="flex-shrink-0"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                    d="M38.7556 49.1026C38.7556 49.1712 38.8228 49.2197 38.8879 49.1981C39.4188 49.0215 39.9456 48.845 40.4178 48.6684C48.0257 45.6091 55.6334 42.6136 63.2411 39.5542C81.5892 32.2247 99.9373 24.8313 118.285 17.5654C125.126 14.8248 132.03 12.2754 139.126 10.2358C142.195 9.34351 145.328 8.57869 148.461 8.13254C152.041 7.62265 155.557 8.45122 158.753 10.1721C161.31 11.5743 162.27 13.9325 161.374 16.7368C160.671 19.0313 159.393 20.9435 157.347 22.218C155.301 23.4291 153.255 24.6401 151.146 25.5961C145.712 28.018 140.214 30.3125 134.716 32.6069C126.149 36.1125 117.518 39.5542 108.888 43.0596C102.814 45.5453 96.8046 48.286 91.1148 51.5365C88.6855 52.9387 86.3839 54.5321 84.0825 56.0616C83.5705 56.4263 83.0794 56.8534 82.5854 57.3073C82.0086 57.8372 82.1885 58.667 82.9317 58.4199C85.2333 57.7826 87.5986 57.2089 89.9001 56.5079C97.1243 54.1496 104.093 51.2815 110.997 48.1586C129.857 39.6817 148.653 31.0774 167.448 22.5368C169.174 21.772 170.837 20.9435 172.626 20.2422C173.778 19.7962 174.992 19.4775 176.143 19.3499C178.188 19.1587 180.042 20.1148 180.681 21.5808C181.449 23.4291 180.745 25.0225 179.468 26.3609C178.444 27.4444 177.165 28.1455 175.887 28.7191C165.531 33.4992 155.237 38.2795 144.816 42.9959C131.455 49.0508 118.157 55.2969 104.54 60.7781C98.5947 63.2001 92.6491 65.4946 86.4479 67.0242C84.2103 67.5978 81.9089 68.1077 79.5433 67.9803C78.3927 67.9165 77.2419 67.7891 76.2189 67.4066C72.8307 66.2593 70.8488 63.1364 71.1685 59.5672C71.2963 57.7826 71.9356 56.1892 72.8947 54.6595C73.9175 53.0025 75.1961 51.6002 76.6665 50.3254C79.0959 48.0948 81.8449 46.3102 84.7217 44.653C92.0738 40.3827 99.8093 36.8773 107.673 33.6905C118.669 29.229 129.665 24.7675 140.661 20.306C141.811 19.8599 143.027 19.2863 144.177 18.7764C144.525 18.6029 144.312 18.2629 143.935 18.359C143.675 18.4253 143.415 18.4987 143.155 18.5852C136.058 20.6247 129.09 23.1741 122.185 25.9148C104.668 32.8619 87.215 39.8729 69.698 46.8838C60.9395 50.3892 52.245 53.9584 43.4865 57.3364C40.0343 58.6749 36.4542 59.6946 32.8741 60.7781C31.2119 61.288 29.5497 61.4792 27.8235 61.288C22.7091 60.7143 20.3437 55.5518 23.3484 51.3453C23.9238 50.5804 24.627 49.8794 25.3302 49.2421C28.3349 46.4376 31.5955 43.9519 35.0477 41.6575C40.8015 37.8333 46.8109 34.3915 53.0122 31.3324C63.8805 25.9784 74.8126 20.6885 85.7447 15.3985C87.7904 14.3786 89.7723 13.3589 91.8181 12.2753C92.1394 12.1151 92.4159 11.9102 92.7228 11.7355C92.811 11.6853 92.8585 11.5428 92.8188 11.4494C92.799 11.4028 92.7471 11.3896 92.6978 11.401C92.1488 11.5279 91.5998 11.7076 91.0508 11.8292C82.4203 14.3786 74.1093 17.7566 65.7984 21.1983C49.3682 28.018 33.5134 35.73 17.7864 43.7607C14.9735 45.2267 12.0327 46.565 9.15582 47.9035C8.26079 48.3496 7.30184 48.6684 6.34288 48.9233C5.25606 49.2421 4.10531 49.2419 2.95456 48.7958C1.29236 48.1585 0.205538 46.8838 0.0137464 45.0992C-0.114115 43.3146 0.653052 41.9123 2.18739 40.9564C2.4431 40.7652 2.69883 40.6377 3.01848 40.5102C7.62148 38.8532 11.9048 36.4949 16.1882 34.3279C33.0019 25.7236 50.0714 17.6291 67.5244 10.4908C75.7075 7.11277 84.0185 4.05347 92.5853 1.82271C95.9097 0.930417 99.2339 0.229326 102.686 0.0381188C104.157 -0.0256168 105.691 -0.0256168 107.161 0.16559C108.12 0.293061 109.143 0.611739 110.038 1.05789C112.979 2.65128 113.938 6.093 112.084 8.89736C111.445 9.91713 110.55 10.8732 109.591 11.5743C107.353 13.2314 105.116 14.9522 102.686 16.3544C96.8686 19.6049 91.0508 22.8555 85.1053 25.7872C74.5569 31.0774 63.9443 36.0487 53.3318 41.275C48.8566 43.5059 44.5094 45.9277 40.0983 48.2222C39.6648 48.4692 39.2314 48.716 38.7979 49.0209C38.7713 49.0396 38.7556 49.0701 38.7556 49.1026Z"
                                    fill="url(#paint0_linear_1_297)"/>
                            <defs>
                                <linearGradient id="paint0_linear_1_297" x1="181" y1="34" x2="-6.30586e-07" y2="34"
                                                gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#F36360"/>
                                    <stop offset="1" stop-color="#FD12C3"/>
                                </linearGradient>
                            </defs>
                        </svg>
                        <span><?= $commercial_banner['title'] ?? ''; ?></span>
                    </h2>
                </div>
                <div class="col-12 my-5">
                    <div class="VideoPlayer">
                        <video src="<?= get_template_directory_uri(); ?>/assets/videos/sample.mp4"
                               class="w-100 h-100 object-fit-cover rounded-3"
                               playsinline></video>

                        <img src="<?= esc_attr($commercial_banner['image']['url'] ?? ''); ?>"
                             alt="<?= esc_attr($commercial_banner['image']['alt'] ?? ''); ?>"
                             class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail rounded-3">
                    </div>
                </div>
                <div class="col-12 text-center sameRBBtn">
                    <a href="" class="btn-hover color-1 xl mx-5 my-1 mx-lg-3 show-plans-modal"> להרשמה </a>
                    <!--                    <a href="" class="btn-hover color-2 xl mx-5 my-2 mx-lg-3"> להזמין עכשיו </a>-->
                </div>
            </div>
        </div>
    </div>
</section>
<?php get_template_part('template-parts/content', 'testimonials-slider'); ?>
<section class="bgStrip pt-5 ">
    <div class="container pt-lg-5 ">
        <div class="row">
            <div class="col-12 OurBlogs">
                <h2 class="title text-center mb-0"><?= $latest_blogs['main_title'] ?? ''; ?></h2>
                <p class="text-center subtitle2"><?= $latest_blogs['subtitle'] ?? ''; ?></p>
                <div class="row mt-lg-4 pt-2">
                    <div class="col-lg-7 horizontalCards order-last order-lg-first">
                        <?php if ($latest_posts->have_posts()) :
                            $first_post = true;
                            while ($latest_posts->have_posts()) : $latest_posts->the_post();
                                if ($first_post) {
                                    $first_post = false;
                                    continue;
                                }
                                ?>
                                <div class="card mb-3">
                                    <?php if (has_post_thumbnail()): ?>
                                        <a href="<?php the_permalink(); ?>" class="featuredImg">
                                            <?php the_post_thumbnail('full', [
                                                'class' => 'card-img-top',
                                                'alt' => get_the_title()
                                            ]); ?>
                                        </a>
                                    <?php endif; ?>
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a href="<?= esc_attr(get_the_permalink()); ?>"><?php the_title(); ?></a>
                                        </h5>
                                        <p>
                                            <a href="<?= esc_attr(get_the_permalink()); ?>">
                                                <?= get_the_excerpt(); ?>
                                            </a>
                                        </p>
                                        <div class="d-flex dateComments">
                                            <div class="date">
                                                <span><?php the_time('M j, Y'); ?></span>
                                                <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/calender.svg"
                                                     alt="calender">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php endif;
                        wp_reset_postdata(); ?>
                        <div class="text-center text-lg-end">
                            <a href="<?= esc_attr($blog_page_url); ?>" class="btn-hover color-2 px-lg-5 fs-4"> למידע
                                נוסף בבלוג </a>
                        </div>
                    </div>
                    <div class="col-lg mb-3 mb-lg-0">
                        <?php if (isset($latest_posts->posts[0])) : ?>
                            <div class="card">
                                <?php if (!empty(get_the_post_thumbnail_url($latest_posts->posts[0]->ID, 'full'))): ?>
                                    <a href="<?php echo get_permalink($latest_posts->posts[0]->ID); ?>"
                                       class="featuredImg">
                                        <img src="<?= get_the_post_thumbnail_url($latest_posts->posts[0]->ID, 'full'); ?>"
                                             class="card-img-top"
                                             alt="<?= get_the_title($latest_posts->posts[0]->ID); ?>"></a>
                                <?php endif; ?>
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="<?php echo get_permalink($latest_posts->posts[0]->ID); ?>"><?php echo get_the_title($latest_posts->posts[0]->ID); ?></a>
                                    </h5>
                                    <?php echo get_the_excerpt($latest_posts->posts[0]->ID); ?>
                                    <div class="d-flex dateComments">
                                        <div class="comments pe-4">
                                            <span><?php echo get_comments_number($latest_posts->posts[0]->ID); ?> הערות</span>
                                            <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/comments.svg"
                                                 alt="comments">
                                        </div>
                                        <div class="date">
                                            <span><?php echo get_the_date('M j, Y', $latest_posts->posts[0]->ID); ?></span>
                                            <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/calender.svg"
                                                 alt="calender">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-lg-5 mt-3">
        <!--		--><?php //get_template_part( 'template-parts/content', 'infinite-scroll' ); ?>
        <!--		--><?php //get_template_part( 'template-parts/content', 'infinite-scroll' ); ?>
    </div>
</section>
