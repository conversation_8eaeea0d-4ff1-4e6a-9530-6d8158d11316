<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$questions = get_field( 'q_and_a' );
?>
<?php get_template_part( 'template-parts/content', 'page-banner' ); ?>
<div class="position-relative overflow-hidden  py-5 ">

    <img src="<?= get_template_directory_uri() ?>/assets/images/dancing-lady.png" alt=""
         class="position-absolute z-n1 end-0"
         style="transform: translateX(-30%);">
    <div class="container">
        <div class="row flex-lg-row-reverse">
            <div class="col-lg-4 thickborder">
                <form action="#" class="shadow-md px-5 py-4 rounded-4">
                    <h3 class="text-center fw-bold mb-4">לא מצאת תשובה – שאלי אותנו</h3>
                    <div class="form-group mb-3">
                        <input type="text" class="form-control rounded-4 text-start" placeholder="שם מלא">
                    </div>
                    <div class="form-group mb-3">
                        <input type="text" class="form-control rounded-4 text-start" placeholder='כתובת דוא"ל'>
                    </div>
                    <div class="form-group mb-3">
                        <input type="text" class="form-control rounded-4 text-start" placeholder="מספר טלפון">
                    </div>
                    <div class="form-group mb-4">
                            <textarea type="text" class="form-control rounded-4 text-start" rows="5"
                                      placeholder="ההודעה שלך..."></textarea>
                    </div>
                    <div class="text-center">
                        <button class="btn btn-hover color-1 px-5">
                            לִשְׁלוֹחַ
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-lg-8 ">
                <div class="accordion FAQAccors" id="accordionExample">
					<?php if ( ! empty( $questions['item'] ) && count( $questions['item'] ) > 0 ): ?>
						<?php foreach ( $questions['item'] as $key => $item ): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <div class="accordion-button" type="button" data-bs-toggle="collapse"
                                         data-bs-target="#collapse-<?= $key; ?>" aria-controls="collapse-<?= $key; ?>">
                                        <div class="icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                 stroke-width="2" stroke="currentColor" class="size-6"
                                                 style="transition: transform 0.3s;">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                      d="m19.5 8.25-7.5 7.5-7.5-7.5" class="arrow"/>
                                            </svg>
                                        </div>
                                        <div class="accorTitle"><?= $item['title'] ?? ''; ?></div>
                                    </div>
                                </h2>
                                <div id="collapse-<?= $key; ?>" class="accordion-collapse collapse"
                                     data-bs-parent="#accordionExample"
                                     onshow="rotateSvg(this)" onhide="resetSvg(this)">
                                    <div class="accordion-body"><?= $item['content'] ?? ''; ?></div>
                                </div>
                            </div>
						<?php endforeach; ?>
					<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php get_template_part( 'template-parts/content', 'our-benefits' ) ?>
<?php get_template_part( 'template-parts/content', 'testimonials-slider' ); ?>
