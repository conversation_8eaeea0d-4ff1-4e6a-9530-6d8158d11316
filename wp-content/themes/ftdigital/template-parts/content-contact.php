<?php
if (!defined('ABSPATH')) {
    exit;
}

$contact_page = get_field('contact_page', get_the_ID());
$main_title = esc_html($contact_page['main_title']);
$phone = esc_html($contact_page['phone']);
$address = wp_kses_post($contact_page['address']);
$email = sanitize_email($contact_page['email_address']);
$training_image = esc_url($contact_page['training_image']['url']);
?>

<div class="position-relative overflow-hidden">
    <img src="<?= get_template_directory_uri() ?>/assets/images/home/<USER>" alt="" class="position-absolute z-n1 start-0 top-0" style="transform: rotateY(180deg);">
    <img src="<?= get_template_directory_uri() ?>/assets/images/bg-dance-lady.png" alt="" class="position-absolute z-n1 end-0 top-0" style="transform: translate(-50%);">
    <div class="container py-5 my-lg-5">
        <div class="row">
            <div class="col-lg py-3">
                <h2 class="display-3 fw-bold mb-4"><?= $main_title ?></h2>
                <ul class="m-0 p-0 contactList">
                    <li class="d-flex align-items-center mb-3">
                        <div class="icon">
                            <svg width="19" height="20" viewBox="0 0 19 20" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M10.5895 6.39566C11.2971 6.39566 11.9757 6.69011 12.4761 7.21423C12.9764 7.73835 13.2575 8.44922 13.2575 9.19044C13.2575 9.43751 13.3512 9.67447 13.518 9.84917C13.6848 10.0239 13.911 10.122 14.1469 10.122C14.3827 10.122 14.6089 10.0239 14.7757 9.84917C14.9425 9.67447 15.0362 9.43751 15.0362 9.19044C15.0362 7.95507 14.5677 6.7703 13.7338 5.89676C12.8999 5.02322 11.7689 4.53247 10.5895 4.53247C10.3537 4.53247 10.1275 4.63062 9.96068 4.80533C9.79389 4.98004 9.7002 5.21699 9.7002 5.46406C9.7002 5.71114 9.79389 5.94809 9.96068 6.1228C10.1275 6.29751 10.3537 6.39566 10.5895 6.39566Z"
                                        fill="white"/>
                                <path
                                        d="M10.5922 2.67251C12.2433 2.67251 13.8267 3.35956 14.9942 4.58251C16.1617 5.80547 16.8176 7.46415 16.8176 9.19367C16.8176 9.44074 16.9113 9.6777 17.0781 9.8524C17.2448 10.0271 17.471 10.1253 17.7069 10.1253C17.9428 10.1253 18.169 10.0271 18.3358 9.8524C18.5025 9.6777 18.5962 9.44074 18.5962 9.19367C18.5962 6.97 17.753 4.83741 16.2519 3.26504C14.7509 1.69267 12.715 0.809326 10.5922 0.809326C10.3564 0.809326 10.1302 0.907476 9.96338 1.08218C9.7966 1.25689 9.7029 1.49385 9.7029 1.74092C9.7029 1.98799 9.7966 2.22495 9.96338 2.39966C10.1302 2.57436 10.3564 2.67251 10.5922 2.67251ZM18.3739 13.7678C18.325 13.6182 18.2405 13.4842 18.1285 13.3783C18.0165 13.2723 17.8806 13.198 17.7336 13.1623L12.3976 11.886C12.2527 11.8516 12.102 11.8557 11.9591 11.898C11.8162 11.9403 11.6856 12.0195 11.5794 12.1282C11.4549 12.2493 11.446 12.2586 10.8679 13.4138C8.94982 12.4879 7.41287 10.8713 6.53688 8.85829C7.66633 8.26207 7.67522 8.26207 7.79084 8.12233C7.89463 8.01106 7.97017 7.87433 8.01056 7.72462C8.05095 7.57491 8.0549 7.41699 8.02206 7.26527L6.80368 1.74092C6.76957 1.58692 6.69861 1.44455 6.59748 1.32721C6.49635 1.20987 6.36838 1.12141 6.22561 1.07017C6.01792 0.992468 5.80345 0.936303 5.58529 0.902485C5.36051 0.84789 5.13114 0.816685 4.9005 0.809326C3.81552 0.809326 2.77498 1.26082 2.00778 2.06447C1.24058 2.86813 0.80957 3.95812 0.80957 5.09466C0.814277 8.89808 2.25873 12.5443 4.82616 15.2337C7.39358 17.9232 10.8744 19.4363 14.5053 19.4412C15.0425 19.4412 15.5745 19.3304 16.0708 19.115C16.5672 18.8996 17.0182 18.584 17.398 18.1861C17.7779 17.7881 18.0792 17.3157 18.2848 16.7958C18.4904 16.2759 18.5962 15.7186 18.5962 15.1559C18.5965 14.9187 18.5787 14.682 18.5429 14.4479C18.5055 14.2165 18.449 13.9889 18.3739 13.7678Z"
                                        fill="white"/>
                            </svg>
                        </div>
                        <a href="tel:<?= preg_replace('/[^0-9]/', '', $phone) ?>" class="content ms-2"><?= $phone ?></a>
                    </li>
                    <li class="d-flex align-items-center mb-3">
                        <div class="icon">
                            <svg width="19" height="26" viewBox="0 0 19 26" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M0 9.75C3.75443e-08 7.16414 1.00089 4.68419 2.78249 2.85571C4.56408 1.02723 6.98044 0 9.5 0C12.0196 0 14.4359 1.02723 16.2175 2.85571C17.9991 4.68419 19 7.16414 19 9.75C19 16.25 9.5 26 9.5 26C9.5 26 0 16.25 0 9.75ZM5.54167 9.75C5.54167 10.8274 5.9587 11.8608 6.70104 12.6226C7.44337 13.3845 8.45018 13.8125 9.5 13.8125C10.5498 13.8125 11.5566 13.3845 12.299 12.6226C13.0413 11.8608 13.4583 10.8274 13.4583 9.75C13.4583 8.67256 13.0413 7.63925 12.299 6.87738C11.5566 6.11551 10.5498 5.6875 9.5 5.6875C8.45018 5.6875 7.44337 6.11551 6.70104 6.87738C5.9587 7.63925 5.54167 8.67256 5.54167 9.75Z"
                                        fill="white"/>
                            </svg>
                        </div>
                        <a href="mailto:<?= $email ?>" class="content ms-2"><?= $email ?></a>
                    </li>
                    <!--<li class="d-flex align-items-center mb-3">
                        <div class="icon">
                            <svg width="21" height="16" viewBox="0 0 21 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                        d="M11.0008 8.59473L10.8745 8.65696H10.8151C10.7468 8.68896 10.6742 8.70994 10.5998 8.71919C10.5381 8.72733 10.4758 8.72733 10.4141 8.71919H10.3547L10.2285 8.65696L0.277387 1.92029C0.229843 2.10818 0.204896 2.30156 0.203125 2.49594V13.3866C0.203125 14.0056 0.437845 14.5992 0.855649 15.0368C1.27345 15.4745 1.84012 15.7203 2.43098 15.7203H18.7686C19.3594 15.7203 19.9261 15.4745 20.3439 15.0368C20.7617 14.5992 20.9964 14.0056 20.9964 13.3866V2.49594C20.9947 2.30156 20.9697 2.10818 20.9222 1.92029L11.0008 8.59473Z"
                                        fill="white"/>
                                <path
                                        d="M10.5971 7.00961L20.0878 0.622994C19.7062 0.3232 19.2429 0.159596 18.7659 0.15625H2.42831C1.95135 0.159596 1.48801 0.3232 1.10645 0.622994L10.5971 7.00961Z"
                                        fill="white"/>
                            </svg>
                        </div>
                        <div class="content ms-2 lh-1"><?php /*= $address */?></div>
                    </li>-->
                </ul>
            </div>
            <div class="col-lg-5 py-3">
                <img src="<?= $training_image ?>" alt="Contact Image" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<div class="ContactForm position-relative overflow-hidden">
    <div class="container pt-5 pt-lg-0">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <form id="contact-form" class="p-5 bg-white rounded-4 shadow-lg border" novalidate>
                    <h2 class="fw-bold mb-4 text-right">לא מצאת תשובה? שאלי אותנו</h2>
                    <div class="row g-3">
                        <div class="col-12">
                            <input type="text" id="fullname" name="fullname" placeholder="שם מלא" class="form-control text-start rounded-4">
                        </div>
                        <div class="col-12">
                            <input type="email" id="email" name="email" placeholder='כתובת דוא"ל' class="form-control text-start rounded-4">
                        </div>
                        <div class="col-12">
                            <input type="text" id="phone" name="phone" placeholder="מספר טלפון" class="form-control text-start rounded-4">
                        </div>
                        <div class="col-12">
                            <textarea id="message" name="message" rows="4" placeholder="ההודעה שלך..." class="form-control text-start rounded-4"></textarea>
                        </div>
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-hover color-1 px-5">שליחה</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-lg">
                <img src="<?= get_template_directory_uri() ?>/assets/images/banner/hero-1.png" alt="Slide 1 Image" class="img-fluid pt-lg-4 formImg">
            </div>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        $('#contact-form').on('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            $('.border-danger').removeClass('border-danger');

            // Validate inputs
            let errors = [];
            const fullname = $('#fullname').val().trim();
            const email = $('#email').val().trim();
            const phone = $('#phone').val().trim();
            const message = $('#message').val().trim();

            if (!fullname) {
                errors.push('שם מלא נדרש');
                $('#fullname').addClass('border-danger');
            }
            if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                errors.push('כתובת דוא"ל לא תקינה');
                $('#email').addClass('border-danger');
            }
            if (!phone || !/^\d{9,12}$/.test(phone.replace(/\D/g, ''))) {
                errors.push('מספר טלפון לא תקין');
                $('#phone').addClass('border-danger');
            }
            if (!message) {
                errors.push('הודעה נדרשת');
                $('#message').addClass('border-danger');
            }

            if (errors.length) {
                Toastify({
                    text: errors.join('\n'),
                    duration: 3000,
                    gravity: 'top',
                    position: 'right',
                    backgroundColor: '#dc3545',
                }).showToast();
                return;
            }

            // AJAX submission
            $.ajax({
                url: EBUrls.ajaxurl,
                type: 'POST',
                data: {
                    action: 'handle_contact_form',
                    nonce: '<?= wp_create_nonce('contact_form_nonce') ?>',
                    fullname: fullname,
                    email: email,
                    phone: phone,
                    message: message
                },
                success: function(response) {
                    if (response.success) {
                        Toastify({
                            text: 'ההודעה נשלחה בהצלחה!',
                            duration: 3000,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#28a745',
                        }).showToast();
                        $('#contact-form')[0].reset();
                    } else {
                        Toastify({
                            text: response.data.message || 'שגיאה בשליחת ההודעה',
                            duration: 3000,
                            gravity: 'top',
                            position: 'right',
                            backgroundColor: '#dc3545',
                        }).showToast();
                    }
                },
                error: function() {
                    Toastify({
                        text: 'שגיאה בהתחברות לשרת',
                        duration: 3000,
                        gravity: 'top',
                        position: 'right',
                        backgroundColor: '#dc3545',
                    }).showToast();
                }
            });
        });
    });
</script>