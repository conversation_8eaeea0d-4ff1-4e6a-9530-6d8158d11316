<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}
?>

<div class="container py-5">
    <h1 class="text-center mb-4"><?php the_title(); ?></h1>
    <div class="video-gallery row">
        <?php
        if (have_rows('video_gallery')) :
            while (have_rows('video_gallery')) : the_row();
                if (have_rows('video')) :
                    while (have_rows('video')) : the_row();
                        $video_code = get_sub_field('api_video_code');
                        $video_title = get_sub_field('video_title');
                        $video_description = get_sub_field('video_description');
                        ?>
                        <div class="col-md-4 mb-4 VideoPlayBox">
                            <div class="video-card shadow-sm bg-white VideoPlayer d-flex flex-column"
                                 data-video-code="<?php echo esc_attr($video_code); ?>">
                                <div class="video-thumbnail position-relative h-100">
                                    <div class="loader"></div>
                                    <img src="" alt="<?php echo esc_attr($video_title); ?>"
                                         class="thumbnail-img d-none">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/playpause-color.svg"
                                         alt="Play video"
                                         class="play-pause-icon position-absolute"
                                         aria-label="Play video">
                                    <div class="video-info">
                                        <h5 class="fw-bold"><?php echo esc_html($video_title); ?></h5>
                                        <p class=""><?php echo esc_html($video_description); ?></p>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between px-4 pb-3 pt-2 mt-auto">
                                    <span class="fs-4 lh-1 fw-bold mx-2" data-duration="00:00"></span>
                                    <svg width="29" height="22" viewBox="0 0 29 22" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path
                                                d="M27.5378 17.9851C27.0985 18.1829 26.6107 18.2473 26.1352 18.1702C25.6596 18.093 25.2172 17.8779 24.8629 17.5514L21.7543 14.6597V16.7562C21.7543 17.9066 21.2973 19.0098 20.4839 19.8233C19.6704 20.6368 18.5671 21.0938 17.4167 21.0938H4.40399C3.25359 21.0938 2.15031 20.6368 1.33685 19.8233C0.5234 19.0098 0.0664063 17.9066 0.0664062 16.7562V5.18928C0.0664063 4.03889 0.5234 2.9356 1.33685 2.12215C2.15031 1.3087 3.25359 0.851704 4.40399 0.851704H17.4167C18.5671 0.851704 19.6704 1.3087 20.4839 2.12215C21.2973 2.9356 21.7543 4.03889 21.7543 5.18928V7.28578L24.8774 4.39406C25.3371 3.97788 25.9344 3.74617 26.5546 3.74342C26.8987 3.74422 27.2388 3.81815 27.5522 3.9603C27.9787 4.13281 28.344 4.42854 28.6015 4.8097C28.8591 5.19087 28.9971 5.64016 28.9981 6.10018V15.8453C28.996 16.307 28.8558 16.7575 28.5956 17.1388C28.3353 17.5202 27.9669 17.8149 27.5378 17.9851ZM18.8626 5.18928C18.8626 4.80582 18.7103 4.43806 18.4391 4.16691C18.168 3.89575 17.8002 3.74342 17.4167 3.74342H4.40399C4.02052 3.74342 3.65276 3.89575 3.38161 4.16691C3.11046 4.43806 2.95813 4.80582 2.95813 5.18928V16.7562C2.95813 17.1396 3.11046 17.5074 3.38161 17.7785C3.65276 18.0497 4.02052 18.202 4.40399 18.202H17.4167C17.8002 18.202 18.168 18.0497 18.4391 17.7785C18.7103 17.5074 18.8626 17.1396 18.8626 16.7562V5.18928ZM26.0919 7.21349L22.029 10.9727L26.0919 14.732V7.21349Z"
                                                fill="#FE0EC9"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    <?php
                    endwhile;
                endif;
            endwhile;
        endif;
        ?>
    </div>
</div>

<?php get_footer(); ?>