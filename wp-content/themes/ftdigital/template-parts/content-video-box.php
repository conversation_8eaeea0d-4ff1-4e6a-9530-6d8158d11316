<?php
$apiVideo = trim(get_field('api_video_code'));
$icon = VideoManager::is_video_watched(get_the_ID());
$thumbnail = get_the_post_thumbnail_url(get_the_ID(), 'full');
?>
<div class="col-12 col-md-4 mb-4">
    <div class="VideoPlayBox shadow-md bg-white" data-video="<?= get_the_ID(); ?>">
        <div class="VideoPlayer">
            <video src=""
                   class="w-100 h-100 object-fit-cover"
                   playsinline></video>
            <img src="<?= get_template_directory_uri(); ?>/assets/images/playpause-color.svg"
                 alt="Play Pause Icon"
                 data-video="<?= esc_attr(get_the_ID()); ?>"
                 class="play-pause-icon z-3">
            <?php if ($thumbnail): ?>
                <img src="<?= esc_attr($thumbnail); ?>"
                     alt="Thumbnail"
                     class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
            <?php else: ?>
                <img src="<?= get_template_directory_uri(); ?>/assets/images/pricing-bg.png"
                     alt="Thumbnail"
                     class="position-absolute top-0 start-0 h-100 w-100 object-fit-cover thumbnail">
            <?php endif; ?>
        </div>
        <div class="px-4 pb-4 pt-2">
            <a href="<?= esc_attr(get_the_permalink()); ?>" class="text-dark text-decoration-none video-link">
                <h5 class="fs-4 fw-bold mb-1"><?= get_field('video_name'); ?></h5>
                <p class="content"><?= get_field('trainer_name'); ?></p>
            </a>
            <div class="d-flex  align-items-center justify-content-between">
                <a href="#" data-video="<?= get_the_ID(); ?>" class="readmore watch-video-external-link">
                    <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/new-tab.svg"
                         alt="New Tab">
                    <span>צפייה בחלונית נפרדת</span>
                </a>
                <div class="d-flex align-items-center watched-wrap">
                    <!--                                                        <span class="fs-4 lh-1 fw-bold mx-2">00:32</span>-->
                    <?php if ($icon): ?>
                    <?php endif; ?>
                    <img src="<?= get_template_directory_uri(); ?>/assets/images/icons/video.svg"
                         alt="Video Camera Icon" class="<?= (($icon) ? 'd-block' : 'd-none'); ?>">
                </div>
            </div>
        </div>
    </div>
</div>