<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}
$testimonials = get_field( 'testimonials', 'option' );
?>
<section class="slider single-slide text-white py-5">
    <div class="container my-lg-5 py-lg-5">
        <div class="row justify-content-center">
            <div class="col-8 mb-lg-5 mb-3 pb-lg-5">
                <h2 class="text-center title"><?= $testimonials['main-title'] ?? ''; ?></h2>
                <p class="text-center subtitle2"><?= $testimonials['subtitle'] ?? ''; ?></p>
            </div>
            <div class="col-12">
				<?php if ( ! empty( $testimonials['add-testimonials'] ) && is_array( $testimonials['add-testimonials'] ) ): ?>
                    <div class="carousel slide" id="carousel" data-bs-wrap="true" data-bs-ride="carousel">
                        <div class="carousel-inner">
							<?php foreach ( $testimonials['add-testimonials'] as $i => $add_testimonial ): ?>
                                <div class="carousel-item <?= ( $i === 0 ? 'active' : '' ); ?>" data-bs-interval="3000">
                                    <div class="d-flex flex-column justify-content-center align-self-center text-center text-white">
                                        <div class="container">
                                            <p class="slider_content"><?= $add_testimonial['testimonial']; ?></p>
                                            <h2 class="review my-md-4 my-3"><?= $add_testimonial['name']; ?></h2>
                                        </div>
                                    </div>
                                </div>
							<?php endforeach; ?>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#carousel"
                                data-bs-slide="prev">
                            <div class="left-arrow">
                                <svg viewBox="0 0 16 16" fill="white" xmlns="http://www.w3.org/2000/svg"
                                     class="icon-svg">
                                    <path fill-rule="evenodd"
                                          d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"/>
                                </svg>
                            </div>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#carousel"
                                data-bs-slide="next">
                            <div class="right-arrow">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 16 16"
                                     class="icon-svg">
                                    <path fill-rule="evenodd"
                                          d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                                </svg>
                            </div>
                        </button>
                        <div class="carousel-indicators indications">
							<?php foreach ( $testimonials['add-testimonials'] as $i => $add_testimonial ): ?>
                                <button type="button" class="indication-btn <?= ( $i === 0 ? 'active' : '' ); ?>"
                                        data-bs-target="#carousel"
                                        data-bs-slide-to="<?= $i; ?>"></button>
							<?php endforeach; ?>
                        </div>
                    </div>
				<?php endif; ?>
            </div>
        </div>
    </div>
</section>

