/*var swiper = new Swiper(".hero-swiper", {
    loop: true,
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
});*/

jQuery(document).ready(function ($) {
    // Initialize AOS
    AOS.init({
        duration: 800,
        once: true
    });
    var $searchForm = $('.search-form-container');
    var $searchToggle = $('.mobile-search-toggle');
    var $searchInput = $('#search-field');

    /*$('.play-pause-icon').on('click', function () {
        const $card = $(this).closest('.video-card');
        const videoCode = $card.data('video-code');

        $.ajax({
            url: EBUrls.ajaxurl + '?action=load-video',
            method: 'POST',
            dataType: 'json',
            data: {video: videoCode},
            success: function (json) {
                if (json.success && json.data.player) {
                    Fancybox.show([{src: json.data.player, type: 'video'}]);
                } else {
                    console.error('Video data not found');
                }
            },
            error: function () {
                console.error('Failed to load video');
            }
        });
    });*/

    function loadVideoThumbnails() {
        const videoCards = $('.video-card');
        const videoCodes = videoCards.map(function () {
            return $(this).data('video-code');
        }).get();

        if (videoCodes.length > 0) {
            $.ajax({
                url: EBUrls.ajaxurl + '?action=fetch-video-thumbnails',
                method: 'POST',
                dataType: 'json',
                data: {video_codes: videoCodes},
                success: function (json) {
                    if (json.success) {
                        videoCards.each(function () {
                            const $card = $(this);
                            const code = $card.data('video-code');
                            const thumbnailUrl = json.data[code];
                            if (thumbnailUrl) {
                                $card.find('.thumbnail-img').attr('src', thumbnailUrl);
                            }
                        });
                    }
                },
                error: function () {
                    console.error('Failed to fetch thumbnails');
                }
            });
        }
    }

    loadVideoThumbnails();

    $('.video-js').each(function () {
        let $this = $(this);
        let videoId = $this.attr('id');
        let player = videojs(videoId, {
            fluid: true,
            responsive: true,
            html5: {
                vhs: {
                    overrideNative: true,
                    enableLowInitialPlaylist: false,
                    smoothQualityChange: false,
                    useBandwidthFromLocalStorage: false,
                    bandwidth: 999999999,
                    limitRenditionByPlayerDimensions: false,
                    useDevicePixelRatio: true,
                    allowSeeksWithinUnsafeLiveWindow: true,
                    experimentalBufferBasedABR: false
                },
                nativeVideoTracks: false,
                nativeAudioTracks: false,
                nativeTextTracks: false
            },
            techOrder: ['html5'],
            controlBar: {
                children: [
                    'playToggle',
                    'volumePanel',
                    'currentTimeDisplay',
                    'timeDivider',
                    'durationDisplay',
                    'progressControl',
                    'qualitySelector',
                    'fullscreenToggle'
                ]
            }
        });

        // Enable quality selector for HLS
        if (typeof player.hlsQualitySelector === 'function') {
            player.hlsQualitySelector({
                displayCurrentQuality: true
            });
        }

        // Force maximum quality on ready
        player.ready(function() {
            player.on('loadedmetadata', function() {
                // Force highest quality
                const qualityLevels = player.qualityLevels();
                if (qualityLevels && qualityLevels.length > 0) {
                    // Disable all but highest quality
                    for (let i = 0; i < qualityLevels.length - 1; i++) {
                        qualityLevels[i].enabled = false;
                    }
                    qualityLevels[qualityLevels.length - 1].enabled = true;
                }
            });
        });

        // Add keyboard shortcuts for skip functionality
        player.on('keydown', function(event) {
            switch(event.which) {
                case 37: // Left arrow - skip back 10 seconds
                    event.preventDefault();
                    player.currentTime(Math.max(0, player.currentTime() - 10));
                    break;
                case 39: // Right arrow - skip forward 20 seconds
                    event.preventDefault();
                    player.currentTime(Math.min(player.duration(), player.currentTime() + 20));
                    break;
            }
        });

        $this.closest('.VideoPlayer').find('.play-pause-icon').on('click', function () {
            let $icon = $(this);
            let videoId = $icon.data('video');
            let player = videojs('video-player-' + videoId);

            // Play the video
            player.play();

            // Hide thumbnail and icon
            $icon.hide();
            $icon.siblings('.thumbnail').hide();

            // Mark as watched
            $.post(EBUrls.ajaxurl, {
                action: 'watchedVideo',
                video_id: videoId
            }, function (json) {
                if (json.success) {
                    $icon.parents('.VideoPlayBox').find('.watched-wrap > img').removeClass('d-none');
                }
            });
        });
    });

    $searchToggle.on('click', function (e) {
        e.stopPropagation(); // Prevent click from bubbling up
        $searchForm.toggleClass('active');
        if ($searchForm.hasClass('active')) {
            $searchInput.focus(); // Focus on input when opened
        }
    });

    // Close the search input when clicking outside
    $(document).on('click', function (e) {
        if (!$searchForm.is(e.target) && !$searchForm.has(e.target).length && $searchForm.hasClass('active')) {
            $searchForm.removeClass('active');
        }
    });

    function openInNewTab(url) {
        var a = document.createElement('a');
        a.href = url;
        a.target = '_blank';
        a.rel = 'noopener noreferrer'; // Optional: for security reasons
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    function SessionSlider() {
        new Swiper('.SessionSlider', {
            // centeredSlides: true,
            slidesPerView: 'auto',
            spaceBetween: 35,
            loop: true,
            /*autoplay: {
                delay: 2500,
                disableOnInteraction: false,
            },*/
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                768: {
                    slidesPerView: 3,
                },
                576: {
                    slidesPerView: 1,
                }
            }
        });
    }

    function setVideoBinders($this) {
        let video = $this.find('video');
        let playButton = $this.find('.playButton');
        let pauseButton = $this.find('.pauseButton');
        let muteButton = $this.find('.muteButton');
        let unmuteButton = $this.find('.unmuteButton');

        playButton.on('click', function () {
            video[0].play();
            $this.hide();
            pauseButton.show();
        });

        pauseButton.on('click', function () {
            video[0].pause();
            $this.hide();
            playButton.show();
        });

        muteButton.on('click', function () {
            video[0].muted = true;
            $this.hide();
            unmuteButton.show();
        });

        unmuteButton.on('click', function () {
            video[0].muted = false;
            $this.hide();
            muteButton.show();
        });

        video.on('ended', function () {
            playButton.show();
            pauseButton.hide();
        });
    }

    function loadVideo() {
        if ($(this).data('video')) {
            $.ajax({
                url: EBUrls.ajaxurl + '?action=load-video',
                method: 'POST',
                dataType: 'json',
                data: {video: $(this).data('video')},
                success: function (json) {
                    if (typeof Fancybox !== 'undefined') {
                        const videoUrl = json.data.player;
                        // Fancybox.show([{src: videoUrl, type: 'iframe'}]);
                        Fancybox.show([{src: videoUrl, type: 'video'}]);
                    } else {
                        console.error('Fancybox is not loaded.');
                    }
                },
                error: function () {
                },
            });
        }
    }

    let currentHeight = $('.PricingSlider').height();

    $('.cardSlide > .read-more').on('click', function (e) {
        e.preventDefault();
        $('.video-description').toggleClass('hide');
        $(this).parent().find('.video-description').toggleClass('show');
    });

    $('.show-plans-modal').on('click', function (e) {
        e.preventDefault();
        $('#PlansModal').modal('show');
    });

    $('.PricingSlider').css('height', (currentHeight + 100) + 'px');

    $(document).on('click', '.watch-video-external-link', function (e) {
        e.preventDefault();
        $.ajax({
            url: EBUrls.ajaxurl + '?action=load-video',
            method: 'POST',
            dataType: 'json',
            data: {video: $(this).data('video')},
            success: function (json) {
                try {
                    if (json.data && json.data.player) {
                        openInNewTab(json.data.player);
                    }
                } catch (e) {
                    alert('An error occurred while loading the video.');
                }
            },
            error: function () {
            },
        });
    });

    $('.play-pause-icon').on('click', loadVideo);

    $('.videos-container').load(EBUrls.ajaxurl + '?action=ajax-main-videos', function () {
        SessionSlider();

        let currentHeight = $('.PricingSlider').height();
        $('.PricingSlider').css('height', (currentHeight + 100) + 'px');
        $('.VideoPlayer').find('video').css('opacity', 0);

        $('.VideoPlayer').each(function () {
            let $this = $(this);
            var video = $(this).find('video')[0];
            var icon = $(this).find('.play-pause-icon');
            var thumbnail = $(this).find('.thumbnail');

            icon.on('click', function () {
                $.ajax({
                    url: EBUrls.ajaxurl + '?action=load-video',
                    method: 'POST',
                    dataType: 'json',
                    data: {video: $(this).data('video')},
                    success: function (json) {
                        if (typeof Fancybox !== 'undefined') {
                            const videoUrl = json.data.player;
                            // Fancybox.show([{src: videoUrl, type: 'iframe'}]);
                            Fancybox.show([{src: videoUrl, type: 'video'}]);
                        } else {
                            console.error('Fancybox is not loaded.');
                        }
                    },
                    error: function () {
                    },
                });
            });

        });
    });

    $(document).on('click', '.play-pause-icon', function () {
        let $this = $(this);
        let videoId = $this.data('video');

        try {
            let player = videojs('video-player-' + videoId);
            player.play();
            $this.hide();
            $this.siblings('.thumbnail').hide();
        } catch (e) {

        }

        $.post(EBUrls.ajaxurl, {
            action: 'watchedVideo',
            video_id: videoId
        }, function (json) {
            if (json.success) {
                $this.parents('.VideoPlayBox').find('.watched-wrap > img').removeClass('d-none');
            }
        });
    });

    const PopSliderPrice = new Swiper('.PopSliderPrice', {
        slidesPerView: 1,  // Show only one slide at a time
        spaceBetween: 20,   // Space between slides
        centeredSlides: true,
        loop: true,
        navigation: {
            nextEl: '.PopSliderPrice .swiper-button-next',
            prevEl: '.PopSliderPrice .swiper-button-prev',
        },
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
        },
    });
    $('#collapseOne').on('show.bs.collapse', function () {
        $(this).prev().find('svg').css('transform', 'rotate(90deg)');
    });

    $('#collapseOne').on('hide.bs.collapse', function () {
        $(this).prev().find('svg').css('transform', 'rotate(0deg)');
    });

    // For other accordion items
    $('.accordion-collapse').on('show.bs.collapse', function () {
        $(this).prev().find('svg').css('transform', 'rotate(90deg)');
    });

    $('.accordion-collapse').on('hide.bs.collapse', function () {
        $(this).prev().find('svg').css('transform', 'rotate(0deg)');
    });

    const Teamsliderr = new Swiper(".teamSlider", {
        loop: true,
        // autoplay: {
        //     delay: 100,
        //     disableOnInteraction: false,
        // },
        navigation: {
            nextEl: '.teamSliderSec .swiper-button-next',
            prevEl: '.teamSliderSec .swiper-button-prev',
        },
        pagination: {
            el: '.teamSliderSec .swiper-pagination',
            clickable: true,
        },
        speed: 1600,
        centeredSlides: true,
        spaceBetween: 25,
        breakpoints: {
            1200: {
                slidesPerView: 4.5,
            },
            768: {
                slidesPerView: 2,
            },
            576: {
                slidesPerView: 1,
            }
        }
    });


    new Swiper('.BlogDetailsSlider', {
        loop: true,
        spaceBetween: 20,
        speed: 1500,
        autoplay: {
            delay: 2000
        },
        centeredSlides: true,
        breakpoints: {
            1200: {
                slidesPerView: 3,
            },
            992: {
                slidesPerView: 2,
            },
            567: {
                slidesPerView: 1,
            }
        },
        navigation: {
            nextEl: '.blogsliderbtn.swiper-button-next',
            prevEl: '.blogsliderbtn.swiper-button-prev',
        },
    });

    /*new Swiper(".hero-swiper", {
        loop: true,
        // dots: true,
        speed: 2000,
        autoplay: {
            delay: 4000
        },
        nextButton: ".swiper-button-next",
        prevButton: ".swiper-button-prev",
        slidesPerView: 1,
        paginationClickable: true,
        spaceBetween: 20,
        breakpoints: {
            767: {
                slidesPerView: 1,
                spaceBetween: 10
            }
        }
    });*/

    const pricingSlider = new Swiper('.PricingSlider', {
        loop: true,
        spaceBetween: 20,
        speed: 1500,
        autoplay: {
            delay: 2000
        },
        // centeredSlides: true,
        breakpoints: {
            1200: {
                slidesPerView: 3,
            },
            992: {
                slidesPerView: 2,
            },
            567: {
                slidesPerView: 1,
            }
        },
        navigation: {
            nextEl: '.pricing .swiper-button-next',
            prevEl: '.pricing .swiper-button-prev',
        },
        on: {
            slideChange: function () {
                const activeIndex = this.activeIndex;
                const slides = this.slides;
                const centerSlide = slides[activeIndex];
            }
        }
    });

    /*new Swiper('.plans-swiper', {
        slidesPerView: 3,
        spaceBetween: 20,
        speed: 1500,
        autoplay: {
            delay: 2000
        },
        breakpoints: {
            1200: {
                slidesPerView: 3,
            },
            992: {
                slidesPerView: 2,
            },
            567: {
                slidesPerView: 1,
            }
        },
        navigation: {
            nextEl: '.pricing .swiper-button-next',
            prevEl: '.pricing .swiper-button-prev',
        }
    });*/

    /*new Swiper('.PricingSlider', {
        loop: true,
        spaceBetween: 20,
        speed: 1500,
        autoplay: {
            delay: 2000
        },
        centeredSlides: true,
        breakpoints: {
            1200: {
                slidesPerView: 3,
            },
            992: {
                slidesPerView: 2,
            },
            567: {
                slidesPerView: 1,
            }
        },
        navigation: {
            nextEl: '.pricing .swiper-button-next',
            prevEl: '.pricing .swiper-button-prev',
        },
    });*/

    const swiper = new Swiper(".serviceSlider", {
        loop: true,
        navigation: {
            nextEl: '.serviceSliderSec .swiper-button-next',
            prevEl: '.serviceSliderSec .swiper-button-prev',
        },
        speed: 1600,
        centeredSlides: true,
        spaceBetween: 25,
        breakpoints: {
            1200: {
                slidesPerView: 4.5,
            },
            768: {
                slidesPerView: 2,
            },
            576: {
                slidesPerView: 1,
            }
        }
    });

    try {
        document.querySelector('.serviceSlider').addEventListener('mouseover', () => {
            swiper.autoplay.stop();
        });

        document.querySelector('.serviceSlider').addEventListener('mouseout', () => {
            swiper.autoplay.start();
        });

        new Swiper(".clients", {
            loop: true,
            dots: true,
            speed: 3000,
            autoplay: {
                delay: 4000
            },
            nextButton: ".swiper-button-next",
            prevButton: ".swiper-button-prev",
            slidesPerView: 4,
            pagination: {
                el: '.swiper-pagination',
                dynamicBullets: true,
            },
            spaceBetween: 20,
            breakpoints: {
                1920: {
                    slidesPerView: 4,
                    spaceBetween: 30
                },
                1199: {
                    slidesPerView: 3,
                    spaceBetween: 30
                },
                767: {
                    slidesPerView: 1,
                    spaceBetween: 10
                }
            }
        });
    } catch (e) {

    }
});
try {
    Fancybox.bind("a.video-fancybox", {});
    /*document.addEventListener('DOMContentLoaded', function () {
        var swiperSlides = document.querySelectorAll('.swiper-slide');
        var maxHeight = 0;

        swiperSlides.forEach(function (slide) {
            var slideHeight = slide.offsetHeight;
            if (slideHeight > maxHeight) {
                maxHeight = slideHeight;
            }
        });

        swiperSlides.forEach(function (slide) {
            slide.style.height = maxHeight + 'px';
        });
    });*/
} catch (e) {

}