jQuery(document).ready(function ($) {
    let searchField = $('#search-field');

    if (searchField.length > 0) {
        searchField.autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: ftAjax.ajaxurl + '?action=autocomplete_search',
                    dataType: 'json',
                    data: {
                        term: request.term
                    },
                    success: function (data) {
                        response($.map(data, function (item) {
                            return {
                                label: item.label,
                                link: item.link,
                                thumbnail: item.thumbnail
                            };
                        }));
                    },
                    error: function (xhr, err, msg) {
                        console.log(xhr, err, msg);
                    }
                });
            },
            minLength: 2,
            select: function (event, ui) {
                window.location.href = ui.item.link;
            }
        }).data('ui-autocomplete')._renderItem = function (ul, item) {
            var $li = $('<li class="autocomplete-item">');
            if (item.thumbnail) {
                $li.append('<img src="' + item.thumbnail + '" alt="' + item.label + '" class="autocomplete-thumbnail">');
            }
            $li.append('<a>' + item.label + '</a>');
            return $li.appendTo(ul);
        };
    }
});