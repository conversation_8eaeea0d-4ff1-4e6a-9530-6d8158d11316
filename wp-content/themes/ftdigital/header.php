<?php
!defined('ABSPATH') && exit;

?>
<!DOCTYPE html>
<html class="no-js" <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css"/>
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
	<?php wp_head(); ?>
    <link rel="profile" href="https://gmpg.org/xfn/11">
</head>
<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<header>
    <div class="row mx-0 justify-content-xl-center">
        <div class="col-xl-auto col-12">
            <nav class="navbar navbar-expand-xl py-3">
                <div class="container-fluid">
					<?php if ( function_exists( 'the_custom_logo' ) && has_custom_logo() ) : ?>
						<?php the_custom_logo(); ?>
					<?php else : ?>
                        <a class="btn btn-link" href="<?php echo home_url(); ?>"><?php bloginfo( 'name' ); ?></a>
					<?php endif; ?>
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse"
                            data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false"
                            aria-label="Toggle navigation">
                        <svg width="24" height="24" viewBox="0 0 18 18" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1_3894)">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M4 6.17133C4 5.60371 4.3731 5.14355 4.83333 5.14355H16.5C16.9602 5.14355 17.3333 5.60371 17.3333 6.17133C17.3333 6.73896 16.9602 7.19911 16.5 7.19911H4.83333C4.3731 7.19911 4 6.73896 4 6.17133Z"
                                      fill="url(#paint0_linear_1_3894)"/>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M0.666016 1.03266C0.666016 0.465035 1.03911 0.00488281 1.49935 0.00488281H16.4993C16.9596 0.00488281 17.3327 0.465035 17.3327 1.03266C17.3327 1.60029 16.9596 2.06044 16.4993 2.06044H1.49935C1.03911 2.06044 0.666016 1.60029 0.666016 1.03266Z"
                                      fill="url(#paint1_linear_1_3894)"/>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M0.666016 11.311C0.666016 10.7434 1.03911 10.2832 1.49935 10.2832H16.4993C16.9596 10.2832 17.3327 10.7434 17.3327 11.311C17.3327 11.8786 16.9596 12.3388 16.4993 12.3388H1.49935C1.03911 12.3388 0.666016 11.8786 0.666016 11.311Z"
                                      fill="url(#paint2_linear_1_3894)"/>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M4 16.4497C4 15.882 4.3731 15.4219 4.83333 15.4219H16.5C16.9602 15.4219 17.3333 15.882 17.3333 16.4497C17.3333 17.0173 16.9602 17.4774 16.5 17.4774H4.83333C4.3731 17.4774 4 17.0173 4 16.4497Z"
                                      fill="url(#paint3_linear_1_3894)"/>
                            </g>
                            <defs>
                                <linearGradient id="paint0_linear_1_3894" x1="10.6667" y1="5.14355" x2="10.6667"
                                                y2="7.19911" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FC17BC"/>
                                    <stop offset="1" stop-color="#F3665D"/>
                                </linearGradient>
                                <linearGradient id="paint1_linear_1_3894" x1="8.99935" y1="0.00488281" x2="8.99935"
                                                y2="2.06044" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FC17BC"/>
                                    <stop offset="1" stop-color="#F3665D"/>
                                </linearGradient>
                                <linearGradient id="paint2_linear_1_3894" x1="8.99935" y1="10.2832" x2="8.99935"
                                                y2="12.3388" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FC17BC"/>
                                    <stop offset="1" stop-color="#F3665D"/>
                                </linearGradient>
                                <linearGradient id="paint3_linear_1_3894" x1="10.6667" y1="15.4219" x2="10.6667"
                                                y2="17.4774" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FC17BC"/>
                                    <stop offset="1" stop-color="#F3665D"/>
                                </linearGradient>
                                <clipPath id="clip0_1_3894">
                                    <rect width="18" height="18" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </button>
                    <div class="collapse navbar-collapse justify-between" id="navbarNav">
						<?php
						wp_nav_menu( array(
							'theme_location' => 'primary',
							'container'      => false,
							'menu_class'     => 'navbar-nav',
							'fallback_cb'    => '__return_false',
							'items_wrap'     => '<ul id="%1$s" class="%2$s">%3$s</ul>',
							'depth'          => 2,
							'walker'         => new WP_Bootstrap_Navwalker(),
						) );
						?>
                        <div class="ms-3 ">
							<?php if ( is_user_logged_in() ): ?>
                                <a href="<?= esc_attr( get_permalink( 7593 ) ); ?>"
                                   class="btn-hover color-2">אימונים</a>
							<?php else: ?>
                                <a href="<?= esc_attr( get_permalink( 22 ) ); ?>" class="btn-hover color-2">להתחבר</a>
                                <a class="btn-hover color-1">הירשם</a>
							<?php endif; ?>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </div>
</header>
<?php if ( ! is_home() && ! is_front_page() && ! is_single() && !is_search()):
	$term = get_queried_object();

	?>
    <section class="pageBanner text-center text-white">
        <div class="col-12 col-md-8 col-lg-6 mx-auto">
            <h1 class="title"><?= is_tax() ? $term->name : get_the_title(); ?></h1>
        </div>
        <nav style="--bs-breadcrumb-divider: '|';" aria-label="breadcrumb" class="custom-breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                    <a href="<?= esc_attr( get_home_url() ); ?>">בית</a>
                </li>
				<?php if ( is_singular( 'post' ) ): ?>
                    <li class="breadcrumb-item">
                        <a href="<?= esc_attr( get_permalink( get_option( 'page_for_posts' ) ) ); ?>">בלוג</a>
                    </li>
				<?php endif; ?>
				<?php
				if ( is_page() && $post->post_parent ) {
					$ancestors = get_post_ancestors( $post->ID );
					$ancestors = array_reverse( $ancestors );
					foreach ( $ancestors as $ancestor ) {
						echo '<li class="breadcrumb-item"><a href="' . get_permalink( $ancestor ) . '">' . get_the_title( $ancestor ) . '</a></li>';
					}
				}
				?>
                <li class="breadcrumb-item active"
                    aria-current="page"><?= is_tax() ? $term->name : get_the_title(); ?></li>
            </ol>
        </nav>
    </section>
<?php endif; ?>
