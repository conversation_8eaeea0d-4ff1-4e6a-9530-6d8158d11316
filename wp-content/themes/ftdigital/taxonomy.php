<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}



get_header();

global $taxonomy;
$term = get_queried_object();
?>
    <main id="main" class="site-main" role="main">
        <div class="position-relative overflow-hidden">
            <img src="<?= get_template_directory_uri(); ?>/assets/images/home/<USER>" alt=""
                 class="position-absolute z-n1 start-0 bottom-0"
                 style="transform: rotateY(180deg);">
            <img src="<?= get_template_directory_uri(); ?>/assets/images/bg-dance-lady.png" alt=""
                 class="position-absolute z-n1 end-0 top-0"
                 style="transform: translate(-50%);">
            <div class="container-xl">
                <div class="row mb-4 justify-content-center">
                    <div class="col-12 col-xl-8 my-4 my-lg-5">
                        <?php get_search_form(); ?>
                    </div>
                </div>
            </div>
            <section class="container-fluid py-5">
                <div class="row">
                    <?php while (have_posts()) : the_post(); ?>
                        <?php get_template_part('template-parts/content', 'video-box') ?>
                    <?php endwhile; ?>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <?php get_bootstrap_pagination(); ?>
                    </div>
                </div>
            </section>
        </div>
    </main>
<?php
get_footer();