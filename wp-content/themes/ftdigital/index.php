<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

get_header();
$blog = get_field( 'blog_main', 17 );
?>
    <section class="bgStrip pt-5 position-relative overflow-hidden">
        <img src="<?= get_template_directory_uri() ?>/assets/images/home/<USER>" alt=""
             class="position-absolute z-n1 start-0 top-50" style="transform: rotateY(180deg);">
        <div class="container pt-lg-5 ">
            <div class="row">
                <div class="col-12  OurBlogs">
                    <h2 class="title text-center mb-0"><?= get_the_title( 17 ); ?></h2>
                    <p class="text-center subtitle2"><?= $blog['subtitle'] ?? ''; ?></p>
                    <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 mt-lg-4 pt-2 g-4">
						<?php while ( have_posts() ) : the_post(); ?>
                            <div class="col">
								<?php get_template_part( 'template-parts/content', 'blog-box' ) ?>
                            </div>
						<?php endwhile; ?>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-md-6">
							<?php
							$links = paginate_links( array(
								'type'      => 'array',
								'prev_next' => true,
								'prev_text' => '<span aria-hidden="true">' . wp_kses_post( __( '<i class="lqd-icn-ess icon-ion-ios-arrow-forward"></i>', 'ftdigital' ) ) . '</span>',
								'next_text' => '<span aria-hidden="true">' . wp_kses_post( __( '<i class="lqd-icn-ess icon-ion-ios-arrow-back"></i>', 'ftdigital' ) ) . '</span>'
							) );

							if ( ! empty( $links ) ) {
								printf( '<div class="page-nav"><nav aria-label="' . esc_attr__( 'Page navigation', 'ftdigital' ) . '"><ul class="pagination"><li>%s</li></ul></nav></div>', join( "</li>\n\t<li>", $links ) );
							}
							?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-lg-5 mt-3"><?php get_template_part( 'template-parts/content', 'infinite-scroll' ); ?></div>
    </section>
<?php
get_footer();
