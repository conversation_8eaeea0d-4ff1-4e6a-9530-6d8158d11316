<?php

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

if ( post_password_required() ) {
	return;
}
?>

<section class="commentSection">
    <div class="container commetsMyWrap">
        <div class="row">
            <div class="col-sm-12">
				<?php if ( $comments ): ?>
                    <div class="comments" id="comments">
						<?php $comments_number = absint( get_comments_number() ); ?>
                        <div class="comments-header section-inner small max-percentage">
                            <h2 class="comment-reply-title">
								<?php
								if ( ! have_comments() ) {
									_e( 'Leave a comment', 'ebtech' );
								} elseif ( '1' === $comments_number ) {
									/* translators: %s: Post title. */
									printf( _x( 'One reply on &ldquo;%s&rdquo;', 'comments title', 'ebtech' ), get_the_title() );
								} else {
									printf(
									/* translators: 1: Number of comments, 2: Post title. */
										_nx(
											'%1$s reply on &ldquo;%2$s&rdquo;',
											'%1$s replies on &ldquo;%2$s&rdquo;',
											$comments_number,
											'comments title',
											'ebtech'
										),
										number_format_i18n( $comments_number ),
										get_the_title()
									);
								}
								?>
                            </h2>
                        </div>
                        <div class="comments-inner section-inner thin max-percentage">
							<?php
							wp_list_comments(
								[
									'avatar_size' => 120,
									'style'       => 'div',
								]
							);
							$comment_pagination = paginate_comments_links(
								[
									'echo'      => false,
									'end_size'  => 0,
									'mid_size'  => 0,
									'next_text' => __( 'Newer Comments', 'ebtech' ) . ' <span aria-hidden="true">&rarr;</span>',
									'prev_text' => '<span aria-hidden="true">&larr;</span> ' . __( 'Older Comments', 'ebtech' ),
								]
							);

							if ( $comment_pagination ) {
								$pagination_classes = '';

								// If we're only showing the "Next" link, add a class indicating so.
								if ( false === strpos( $comment_pagination, 'prev page-numbers' ) ) {
									$pagination_classes = ' only-next';
								}
								?>

                                <nav class="comments-pagination pagination<?php echo $pagination_classes; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- static output ?>"
                                     aria-label="<?php esc_attr_e( 'Comments', 'ebtech' ); ?>">
									<?php echo wp_kses_post( $comment_pagination ); ?>
                                </nav>
								<?php
							}
							?>
                        </div>
                    </div>
				<?php endif; ?>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
				<?php
				if ( comments_open() || pings_open() ) {
					if ( $comments ) {
						echo '<hr class="styled-separator is-style-wide" aria-hidden="true" />';
					}
					comment_form(
						[
							'class_form'         => 'section-inner thin max-percentage',
							'title_reply_before' => '<h2 id="reply-title" class="comment-reply-title">',
							'title_reply_after'  => '</h2>',
						]
					);
				} elseif ( is_single() ) {
					if ( $comments ) {
						echo '<hr class="styled-separator is-style-wide" aria-hidden="true" />';
					}
					?>
                    <div class="comment-respond" id="respond">
                        <p class="comments-closed"><?php _e( 'Comments are closed.', 'ebtech' ); ?></p>
                    </div>
					<?php
				}
				?>
            </div>
        </div>
    </div>
</section>


