<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

get_header();

$apiVideo = trim(get_field('api_video_code', get_the_ID()));
$watchedList = get_user_meta(get_current_user_id(), 'watched_videos');
$videoData = VideoManager::getVideoData($apiVideo);

$thumbnail = get_the_post_thumbnail_url(get_the_ID(), 'full');
$terms = wp_get_post_terms(get_the_ID(), 'topics', ['fields' => 'ids']);
$relevant_videos = new WP_Query([
    'post_type' => 'online-vod',
    'posts_per_page' => 5,
    'post__not_in' => [get_the_ID()],
    'tax_query' => [
        [
            'taxonomy' => 'topics',
            'field' => 'term_id',
            'terms' => $terms,
        ],
    ],
]);
?>

    <main id="main" class="site-main container-fluid" role="main">
        <div class="row mb-4 justify-content-center">
            <div class="col-12 col-lg-7">
                <?php get_search_form(); ?>
            </div>
            <div class="col-lg-3 d-flex align-items-center">
                <?php if (function_exists('the_custom_logo') && has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <a class="btn btn-link" href="<?php echo home_url(); ?>"><?php bloginfo('name'); ?></a>
                <?php endif; ?>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-7">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                        <header class="entry-header mb-4">
                            <h1 class="entry-title h3"><?php the_title(); ?></h1>
                        </header>
                        <div class="entry-content">
                            <div class="VideoPlayBox shadow-md bg-white mb-4">
                                <div class="VideoPlayer position-relative">
                                    <?php
                                    $video_id = get_the_ID();
                                    $apiVideo = trim(get_field('api_video_code', $video_id));
                                    $videoData = VideoManager::getVideoData($apiVideo);
                                    $thumbnail = get_the_post_thumbnail_url($video_id, 'full') ?: get_template_directory_uri() . '/assets/images/pricing-bg.png';

                                    ?>
                                    <!-- Video.js Player -->
                                    <video
                                            id="video-player-<?php echo esc_attr($video_id); ?>"
                                            class="video-js vjs-default-skin vjs-big-play-centered"
                                            controls
                                            autoplay
                                            muted
                                            poster="<?php echo esc_attr($thumbnail); ?>"
                                            data-setup='{"fluid": true}'
                                    >
                                        <source src="<?php echo esc_attr($videoData['hls_url']); ?>"
                                                type="application/x-mpegURL">
                                        <p class="vjs-no-js">To view this video, please enable JavaScript, and consider
                                            upgrading to a web browser that supports HTML5 video.</p>
                                    </video>
                                    <!-- Play/Pause Icon Overlay (Optional) -->
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/playpause-color.svg"
                                         alt="Play Pause Icon"
                                         data-video="<?php echo esc_attr($video_id); ?>"
                                         class="play-pause-icon z-3 position-absolute"
                                         style="display: none;">
                                </div>
                                <div class="px-4 pb-4 pt-2">
                                    <h5 class="fs-5 fw-bold mb-1"><?php echo esc_html(get_field('video_name', $video_id)); ?></h5>
                                    <p class="content"><?php echo esc_html(get_field('trainer_name', $video_id)); ?></p>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <a href="<?php echo esc_attr($videoData['player'] ?? ''); ?>"
                                           data-video="<?php echo esc_attr($video_id); ?>"
                                           class="readmore watch-video-external-link">
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/icons/new-tab.svg"
                                                 alt="New Tab">
                                            <span><?php echo esc_html(get_field('video_name', $video_id)); ?></span>
                                        </a>
                                        <div class="d-flex align-items-center watched-wrap"
                                             id="watched-wrap-<?php echo esc_attr($video_id); ?>">
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/icons/video.svg"
                                                 alt="Video Camera Icon" class="d-block">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
            <?php if ($relevant_videos->have_posts()) : ?>
                <div class="col-lg-3">
                    <aside class="relevant-videos mb-5 mb-lg-0">
                        <h2 class="h4 header mb-4">אימונים דומים</h2>
                        <div class="list-group">
                            <?php while ($relevant_videos->have_posts()) : $relevant_videos->the_post(); ?>
                                <div class="card mb-3 shadow-sm">
                                    <div class="row g-0">
                                        <div class="col-4">
                                            <img src="<?= get_the_post_thumbnail_url(get_the_ID(), 'thumbnail'); ?>"
                                                 alt="<?php the_title(); ?>" class="img-fluid rounded-start">
                                        </div>
                                        <div class="col-8">
                                            <div class="card-body">
                                                <h5 class="card-title h6"><?php the_title(); ?></h5>
                                                <p class="card-text"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                                                <div class="d-flex align-items-center watched-wrap mt-2"
                                                     id="watched-wrap-<?php echo esc_attr(get_the_ID()); ?>">
                                                    <?php if (VideoManager::is_video_watched(get_the_ID())): ?>
                                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/icons/video.svg"
                                                             alt="Video Camera Icon" class="d-block me-2">
                                                    <?php endif; ?>
                                                </div>
                                                <a href="<?php the_permalink(); ?>" class="stretched-link"></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                            <?php wp_reset_postdata(); ?>
                        </div>
                        <?php if ($terms): ?>
                            <div class="text-center text-lg-start">
                                <a href="<?= get_term_link($terms[0]); ?>" class="btn-hover color-2 mt-3 mx-auto">צפייה
                                    בכל האימונים בקטגוריה</a>
                            </div>
                        <?php endif; ?>
                    </aside>
                </div>
            <?php endif; ?>
        </div>
    </main>
    <script>
        jQuery(document).ready(function ($) {
            $.post('<?= esc_attr(admin_url('admin-ajax.php')); ?>', {
                action: 'watchedVideo',
                video_id: '<?= esc_attr(get_the_ID()); ?>'
            }, function (json) {

            });
        });
    </script>
<?php get_footer(); ?>