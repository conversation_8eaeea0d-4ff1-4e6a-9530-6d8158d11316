<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}
global $wp_query;
?>
    <form role="search" method="get" class="row search-form-container" action="<?php echo esc_url(home_url('/')); ?>">
        <div class="SearchForm">
            <div class="search-input-group">
                <input type="search" id="search-field" name="s" class="form-control rounded-pill text-start"
                       placeholder="הזיני שם של אימון / מאמנת / אביזר." value="<?php echo get_search_query(); ?>">
                <input type="hidden" name="post_type" value="online-vod"/>
            </div>
            <button type="submit"
                    class="btn-hover color-1 search-btn d-flex justify-content-center align-items-center d-none d-md-block">
                <span class="btn-text">חפשי אימון</span>
                <span class="search-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm-8 6a8 8 0 1 1 14.32 4.906l5.387 5.387a1 1 0 0 1-1.414 1.414l-5.387-5.387A8 8 0 0 1 2 10z"
                          fill="white"/>
                </svg>
            </span>
            </button>
            <div class="mobile-search-toggle">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm-8 6a8 8 0 1 1 14.32 4.906l5.387 5.387a1 1 0 0 1-1.414 1.414l-5.387-5.387A8 8 0 0 1 2 10z"
                          fill="var(--primary)"/>
                </svg>
            </div>
        </div>
    </form>

<?php if (!empty(get_search_query())): ?>
    <div class="row mt-3">
        <div class="col-12">
            <!--            <p class="mb-2"><i>תוצאות ל:</i> <b class="keyword">-->
            <?php //= get_search_query(); ?><!--</b></p>-->
            <p><i>סה"כ תוצאות:</i> <b class="total-results"><?= $wp_query->found_posts ?? ''; ?></b></p>
        </div>
    </div>
<?php endif; ?>

<?php if (!is_page('user')): ?>
    <div class="row mt-4 position-relative">
        <div class="col-12 text-center">
            <a href="<?php echo esc_url(get_permalink(7593)); ?>" class="btn btn-link view-all-link">
                <span>לכל האימונים</span> »
            </a>
        </div>
    </div>
<?php endif; ?>