/*
 Theme Name: ftdigital
 Author: <PERSON><PERSON>
 Description: FTDigital theme
 Version: 1.0
 License: GNU General Public License v2 or later
 Text Domain: ftdigital
*/


:root {
    --primary: #F46065;
    --secondary: #FE0EC9;
    --p-rad-1: #FFFBFB;
    --p-rad-2: #FEF1F0;
    --grad-light: radial-gradient(var(--p-rad-1), var(--p-rad-2));
    --glinear-light: linear-gradient(to bottom, #fff, var(--p-rad-2));
    --grad-dark: linear-gradient(90deg, var(--primary), var(--secondary));
    --black: #000;
    --white: #fff;
    --transition: all .18s linear;
    --bs-border-radius-xl: 1rem;
}

.bggrad-light {
    background-image: var(--grad-light);
}

.bgglinear-light {
    background-image: var(--glinear-light);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Assistant", sans-serif;
}

a {
    text-decoration: none;
}

.navbar-toggler {
    outline: none !important;
    box-shadow: none !important;
}

.btn-hover {
    color: var(--white);
    cursor: pointer;
    text-align: center;
    border: none;
    background-size: 300% 100%;
    border-radius: 50px;
    moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -webkit-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
    padding: 12px 26px;
    font-weight: bold;
    display: inline-block;
}

.btn-hover:hover {
    background-position: 100% 0;
    moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -webkit-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
}

.btn-hover:focus {
    outline: none;
}

.btn-hover.color-1 {
    background-image: linear-gradient(to right, var(--primary), var(--secondary), var(--secondary), var(--primary));
}

.btn-hover.color-2 {
    background-image: linear-gradient(to left, var(--secondary), var(--primary), var(--primary), var(--secondary));
}

.nav-link {
    font-weight: bold;
    font-size: 18px;
    color: var(--black);
}

.swiper-slide.bg-1 {
    /* background: url('../images/bg-shape.svg') bottom center/100% auto no-repeat,url('../images/banner/banner1.png') no-repeat center/cover; */
    background: url('./assets/images/banner/banner-2.jpeg') no-repeat top center/cover;
    position: relative;
    min-height: 750px;
}

/*.swiper-slide.bg-1::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 20%;
    background: url('./assets/images/bg-shape.svg') bottom center/100% auto no-repeat;
}*/
@keyframes moveBackground {
    0% {
        background-position: 100% bottom;
    }
    100% {
        background-position: 0 bottom;
    }
}

.hero-swiper::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 50px;
    background-image: url(./assets/images/wave-svg.svg);
    background-size: auto 100%;
    background-repeat: repeat-x;
    animation: moveBackground 10s linear infinite;
    z-index: 1;
}

.subtitle {
    font-size: 38px;
    font-weight: 600;
}

.subtitle2 {
    font-size: 22px;
    font-weight: 600;
}

.title {
    font-size: 70px;
    position: relative;
    font-weight: bold;
    line-height: 1.125;
}

.title svg {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(100%, 30%);
}

.xl.btn-hover {
    padding: 14px 48px;
    font-size: 24px;
}

/* Utitlities */
.heading {
    font-size: 90px;
    font-weight: bold;
}

p.content,
article.entry-content > p,
article.singleBlog p,
article.singleBlog .wp-block-list,
.contactList a {
    font-size: 24px;
    /*font-weight: 600;*/
}

article.singleBlog h3 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 2.5rem;
    margin-top: 3rem;
}

.swiper-rtl .swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
    content: '';
}


.swiper-rtl .swiper-button-next,
.swiper-rtl .swiper-button-prev {
    width: 70px;
    height: 70px;
    bottom: initial;
    top: 50%;
}


.swiper-button-lock {
    display: initial !important;
}

.strip {
    gap: 42px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.strip > * {
    flex-shrink: 0;
}

.strip h2 {
    font-size: 104px;
    font-weight: bolder;
    width: max-content;
}

/* About Us SEctioon : Starts*/
.bgLadyImage {
    background: url("./assets/images/dancing-lady.png") no-repeat -30% top/auto 80%;
}

.bgLadyImage .heartIcon {
    position: absolute;
    right: 5%;
    bottom: 9%;
    transform: rotate(27deg);
    z-index: -1;
}

.serviceSliderSec {
    background: url("./assets/images/bg-shapeseerv.svg") no-repeat center bottom/contain, var(--glinear-light);
}


.cardSlide {
    height: 450px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 100%;
    z-index: 0;
    transition: var(--transition);
}

.cardSlide > a[data-fancybox="gallery"] {
    margin: auto;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    display: flex;
}

.cardSlide::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #0008;
    z-index: -1;
    opacity: .2;
    transition: var(--transition);
}

.serviceSlider .swiper-slide.swiper-slide-active .cardSlide::after {
    opacity: 1;
}

.cardSlide.bg-1 {
    background: url("./assets/images/bg-serv.png") no-repeat center/cover;
}

.serviceSlider .swiper-slide {
    /* border: 2px solid red; */
    transition: all .2s linear;
    display: grid;
    place-items: center;
}


.serviceSlider .swiper-slide .cardSlide .sTitle {
    font-size: 30px;
    font-weight: bold;
    color: var(--white);
    /* text-shadow: 0 0 10px #000; */
}

.cardSlide .btn {
    background: var(--grad-dark);
    border-radius: 0;
    position: absolute;
    bottom: 0;
    right: -1px;
    font-size: 24px;
    color: var(--white);
    font-weight: bold;
    padding: 5px 28px;
    transition: var(--transition);
}

.cardSlide .btn:hover {
    box-shadow: 0 0 10px #0005;
    transform: scale(1.1);
}

/* Pricing Slider */
.pricing {
    background: linear-gradient(#0D1121dd, #0D1121dd), url('./assets/images/pricing-bg.png') no-repeat center/cover;
    position: relative;
}

/*.pricing.bottomAbs::after,
.pricing::before {
    z-index: 2;
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("./assets/images/bg-shape-op.svg") no-repeat center top/100% auto;
}*/

.pricing.bottomAbs::after,
.pricing::before {
    z-index: 2;
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    width: 100%;
    height: 60px;
    /*transform:translateY(-100%);*/
    /*background: url("../images/bg-shape-op.svg") no-repeat center top/100% auto;*/
    background-image: url('./assets/images/wave-svg-2.svg');
    background-size: auto 100%;
    background-repeat: repeat-x;
    animation: moveBackground 10s linear infinite;
}


.pricing.bottomAbs::after {
    top: initial !important;
    bottom: -2px;
    background: url("./assets/images/bg-shape.svg") no-repeat center bottom/100% auto;
}

.PricingSlider .swiper-slide {
    height: initial;
    transition: var(--transition);
}

.PricingSlider .swiper-slide.swiper-slide-active {
    /*height: 100%;*/
}

.PricingSlider .swiper-slide.swiper-slide-next {
    height: 100%;
}

.PricingSlider .PricingSlide {
    display: flex;
    flex-direction: column;
    font-size: 20px;
    color: var(--black);
    background: var(--white);
    box-shadow: 0 2px 10px #0005;
    transition: var(--transition);
    text-align: right !important;
    position: relative;
    height: 100%;
    justify-content: space-between;
    padding: 20px 0 0;
    min-height: 390px;
}

.PricingSlider .PricingSlide ul {
    list-style: none;
    font-weight: 600;
    padding: 0;
    border-top: 1px solid #949494;
    padding-top: 24px;
}

.topoffer {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--black);
    color: var(--white);
    padding: 10px 30px;
    transform: translate(2px, -20%);
}

.PricingSlider .swiper-slide ul img {
    padding-left: 6px;
}

.PricingSlider .swiper-slide ul li {
    margin-bottom: 26px;
    display: flex;
    align-items: start;
    line-height: 1;
    gap: 5px;
}

.PricingSlider .swiper-slide .btn-hover {
    width: max-content;
    border-radius: 4px 4px 0 4px;
    margin-top: 20px;
}

.swiper-button-prev:after,
.swiper-button-next:after {
    content: '';
}

/* .pricing .swiper-button-next svg {
    transform: translateX(120%);
}

.pricing .swiper-button-prev svg {
    transform: translateX(-120%) rotate(180deg);
} */


/*
.PricingSlide.swiper-slide-active .btn-hover {
    margin-top: 100px;
} */

/* .swiper-button-next:not(.pricingSliderBtn) {
    transform: translateX(120%);
}

.swiper-button-prev:not(.pricingSliderBtn) {
    transform: translateX(-120%) rotate(180deg);
} */

.swiper-button-prev {
    transform: rotate(180deg);
}

@keyframes marquee {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-50%, 0);
    }
}

/* Simple Section */
.bgImgSec {
    /* background-color: #FFFBFD; */
}

/* Video Frame Section */
.bg-crunch {
    background: url('./assets/images/bg-crunch.png') no-repeat 40% 90%/80% auto;
}

.VideoSection {
    background: url("./assets/images/bg-dance-lady.png") no-repeat -40% 0/50% auto,
    url("./assets/images/home/<USER>") no-repeat 90% 100%/15% auto,
    url('./assets/images/icons/bg-apple.png') no-repeat 60vw 50%/auto #fff;

}


.videoFrame2 {
}

/* Our Blogs : Starts  */
.bgStrip {
    /* border: 1px solid red; */
    background: url('./assets/images/icons/bg-apple.png') no-repeat top left/auto,
    url('./assets/images/icons/bg-female.png') no-repeat bottom center/auto;
}

.OurBlogs .card {
    background-color: var(--white);
    border-radius: 8px !important;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 25px;
    border: none !important;
    transition: var(--transition);
}

.OurBlogs .horizontalCards .card {
    display: flex;
    padding: 14px;
    flex-direction: row;
}

.OurBlogs .horizontalCards .card .featuredImg {
    width: 25%;
}

.OurBlogs .card .card-img-top {
    transition: var(--transition);
    max-height: 271px;
    display: flex;
    object-fit: contain;
    object-position: top;
}

.OurBlogs .card:hover .card-img-top {
    filter: brightness(.7);
}

.OurBlogs .card-title {
    min-height: 53px;
}

.OurBlogs .card-title a {
    color: var(--black);
    font-size: 22px;
    transition: var(--transition);
    font-weight: bold;
}

.OurBlogs .card-text {
    font-size: 18px;
    font-weight: 600;
}

.OurBlogs .card-body {
    padding: 28px 18px;
}

.OurBlogs .card-body .dateComments span {
    font-size: 14px;
    font-weight: 600;
    color: #A9A7A7;
    padding-left: 6px;
}

.OurBlogs .card-body .dateComments .comments,
.OurBlogs .card-body .dateComments .date {
    display: flex;
    align-items: center;
}

.OurBlogs .card-title a:hover {
    color: var(--secondary);
}

.OurBlogs .horizontalCards .card .card-body {
    width: 75%;
    padding: 0px 20px;
}

.OurBlogs .card:hover {
    transform: scale(1.02);
    box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 25px;
}

.OurBlogs .horizontalCards .card .card-img-top {
    border-radius: 14px;
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.OurBlogs .horizontalCards .card .card-title a {
    font-size: 18px;
}


.OurBlogs .horizontalCards .card .card-text,
.OurBlogs .horizontalCards .card .card-title {
    margin: 0;
}

.OurBlogs .horizontalCards .card .card-text {
    font-size: 16px;
    margin: 8px 0;
}

/* Stripes */
/* Container styles */
.scrolling-text-container {
    background-color: #eff5ff;
    border-radius: 4px;
    overflow: hidden;
}

/* Inner container styles */
.scrolling-text-inner {
    display: flex;
    white-space: nowrap;
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0;
}

form .form-control {
    padding: 14px 30px;
    text-align: right;
    font-weight: 600;
    box-shadow: none !important;
    border: 2px solid #E0E0E0;
    direction: rtl;
}

form .form-control:focus {
    border: 2px solid #FE0EC955;
}

/* Custom Check Boxes */
.formSectionWrap {
    padding: 46px 36px;
    border-radius: 20px;
}

.form-check {
    padding: 0;
    text-align: right;
}

.form-check input[type=checkbox] {
    position: relative;
    border: 2px solid #A8A7A8;
    background: none;
    cursor: pointer;
    line-height: 0;
    margin: 0 .15rem 0 0.6em;
    outline: 0;
    padding: 0 !important;
    vertical-align: text-top;
    height: 20px;
    width: 20px;
    -webkit-appearance: none;
    opacity: .5;
}

.form-check input[type=checkbox]:hover {
    opacity: 1;
}

.form-check input[type=checkbox]:checked {
    border: 2px solid var(--secondary);
    background-color: var(--secondary);
    opacity: 1;
}


.form-check input[type=checkbox]:before {
    content: '';
    position: absolute;
    right: 50%;
    top: 50%;
    width: 6px;
    height: 11px;
    border: solid #FFF;
    border-width: 0 2px 2px 0;
    margin: -1px -1px 0 -1px;
    transform: rotate(45deg) translate(-50%, -50%);
    z-index: 2;
}

.form-check label {
    font-size: 20px;
    font-weight: 600;
}

/* Video Player JS */
.VideoPlayer {
    position: relative;
    /*height: 100%;*/
    width: 100%;
    overflow: hidden;
}

.VideoPlayer .play-pause-icon {
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 100px;
    transition: var(--transition);
    transform-origin: bottom;
}

.VideoPlayer .play-pause-icon:hover {
    opacity: .9;
}

.VideoPlayBox .readmore {
    color: var(--secondary);
    font-weight: 600;
    transition: var(--transition);
}

.VideoPlayBox .readmore img {
    margin: 0 5px;
}

.VideoPlayBox .readmore:hover {
    text-decoration: underline;
}

.VideoPlayBox .watch-video-external-link {
    color: var(--secondary);
    font-weight: 600;
    transition: var(--transition);
}

.VideoPlayBox .watch-video-external-link img {
    margin: 0 5px;
}

.VideoPlayBox .watch-video-external-link:hover {
    text-decoration: underline;
}

/* Form */
.shadow-md {
    box-shadow: 0 0 20px 5px #0001;
}

/* Footer */
.LinkList .title {
    font-size: 24px;
    font-weight: 700;
}

.LinkList ul {
    list-style: none;
    padding: 0;
}

.LinkList a {
    font-size: 20px;
    font-weight: 600;
    color: var(--black);
    transition: var(--transition);
}

.LinkList a:hover {
    color: var(--secondary);
}

.LinkList.contact a {
    display: flex;
    margin-bottom: 14px;
}

.LinkList.contact a svg {
    margin-top: 6px;
    margin-left: 8px;
}

.socialLink img {
    max-width: 50px;
}

.copyright {
    font-size: 20px;
    font-weight: 600;
}


.pricing .swiper-button-prev:after,
.pricing .swiper-rtl .swiper-button-next:after,
.pricing .swiper-button-next:after,
.pricing .swiper-rtl .swiper-button-prev:after {
    content: '';
}

.serviceSlider .swiper-button-next,
.serviceSlider .swiper-button-prev {
    transform: initial !important;
}

/* Page Header */
.pageBanner {
    background: linear-gradient(#000a, #000a), url("./assets/images/inneerpage.png") no-repeat center/cover;
    padding: 80px 0;
}

.thickborder .form-control {
    border: 2px solid #FE0EC955 !important;
}

.thickborder .form-control:focus {
    border-color: var(--secondary) !important;
}

.breadcrumb .breadcrumb-item {
    /*font-size: 24px;*/
    font-weight: 600;
}

.breadcrumb .breadcrumb-item,
.breadcrumb .breadcrumb-item::before {
    color: white;
}

.breadcrumb .breadcrumb-item a {
    color: var(--secondary);
}

/* Blog Details Page : Starts */
.teamSliderSec .swiper-button-next,
.teamSliderSec .swiper-button-prev,
.swiper-button-next.blogsliderbtn,
.swiper-button-prev.blogsliderbtn {
    height: 50px;
    width: 50px;
}

.BlogDetailsSlider .card,
.testCard {
    box-shadow: 0 0 5px #0002;
}

.BlogDetailsSlider .card:hover {
    box-shadow: 0 0 10px #0002;
}

/* Blog Details Page : Ends */
.testCard {
    padding: 35px 30px;
    margin-bottom: 30px;
}

.testCard .content {
    font-size: 15px;
    color: #615F5F;
    font-weight: 600;
}

.imgBox {
    height: 100%;
}

.imgBox .box {
    background: linear-gradient(#FE0EC926, #F4606526) #FFFC;
    padding: 24px 24px 15px;
    height: 280px;
    overflow: hidden;
}

.imgBox .icon {
    height: 130px;
    margin: auto;
    margin-bottom: 20px;
}

.imgBox .icon img {
    height: 100%;
}

.imgBox .box {
    height: calc(100% - 150px);
}

/* Contact Page */

.contactList .icon {
    background: var(--secondary);
    height: 36px;
    width: 36px;
    display: grid;
    place-items: center;
    border-radius: 50%;
}

.contactList a {
    color: var(--black);
    transition: var(--transition);
}

.contactList a:hover {
    color: var(--secondary);
}

.ContactForm {
    background: linear-gradient(#0D1121dd, #0D1121dd), url("./assets/images/conatct-form-bg.png") no-repeat center/cover;
}

.ContactForm .formImg {
    transform: rotateY(180deg);
}

.MemberBox {
    position: relative;
    min-height: 500px;
    background-size: cover;
    background-repeat: no-repeat;
    padding: 30px;
    display: flex;
    justify-content: end;
    align-items: start;
    flex-direction: column;
    position: relative;
}

.MemberBox::after {
    position: absolute;
    top: 0;
    left: 0;
    content: '';
    height: 100%;
    width: 100%;
    background: linear-gradient(#fff0 40%, #2E2D2D);
}

.VideoPlayBox {
    border-radius: 0 0 12px 12px;
    height: 100%;
}

.VideoPlayBox .VideoPlayer video {
    aspect-ratio: 1/1;
}

.SessionSlider .swiper-slide {
    /* border: 2px solid red; */
}

.SessionSlider .swiper-pagination .swiper-pagination-bullet {
    height: 14px;
    width: 20px;
    border-radius: 50px;
}

.SessionSlider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 32px;
    border-radius: 50px;
    background: var(--grad-dark);
}

/* fAq Page : Starts  */
.FAQAccors .accordion-button,
.FAQAccors .accordion-item {
    box-shadow: none !important;
    background-color: #FFF;
    border: 0 !important;
}

.FAQAccors .accordion-button {
    border: 0 !important;
    display: flex;
    align-items: stretch;
    font-size: 20px;
    font-weight: 600;
    padding: 0;

}

.FAQAccors .accordion-button .accorTitle {
    border: 1px solid #b5b5b5;
    height: 60px;
    line-height: 60px;
    flex-grow: 1;
    border-right: none;
    padding: 0 20px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    color: var(--black);
}

.FAQAccors .accordion-item {
    margin-bottom: 28px;
}

.FAQAccors .accordion-button::after {
    display: none;

}


.FAQAccors .accordion-button .icon svg {
    transition: var(--transition);
    height: 23px;
    color: var(--white);

}

.FAQAccors #collapseOne svg {
    transform: rotate(0);
}

.FAQAccors .accordion-button .icon {
    height: 60px;
    width: 60px;
    background-color: var(--black);
    display: grid;
    place-items: center;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.FAQAccors .accordion-body {
    font-size: 18px;
    font-weight: 600;
    color: #6f6f6f;
    box-shadow: 0 0 10px #0001;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.RModal .modal-content {
    padding: 22px;
}

.RModal .modal-content .btn-close {
    position: absolute;
    background: var(--white) !important;
    border-radius: 50%;
    box-shadow: 0 0 5px #0001;
    opacity: 1;
    width: 32px;
    height: 32px;
    left: 4px;
    top: 4px;
    z-index: 9;
}

.RModal .modal-content .btn-close img {
}

.RModal .modal-body {
    border: 3px solid var(--primary);
}

.RModal .swiper-rtl .swiper-button-next,
.RModal .swiper-rtl .swiper-button-prev {
    height: 45px;
    width: 45px;
}

.RModal .swiper-rtl .swiper-button-next {
    left: 0;
}

.RModal .swiper-rtl .swiper-button-prev {
    right: 0;
}

.PopSliderPrice ul li {
    display: flex;
    margin-bottom: 12px;
    align-items: start;
}

.PopSliderPrice ul li img {
    margin-top: 6px;
}

.PopSliderPrice ul li span {
    font-size: 18px;
    color: #29282D;
    font-weight: 600;
    padding-right: 6px;

}

.rounded-img {
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
}

@media (min-width: 992px) {

    .pricing .swiper-button-next,
    .swiper-button-next.blogsliderbtn {
        transform: translateX(180%);
    }

    .pricing .swiper-button-prev,
    .swiper-button-prev.blogsliderbtn {
        transform: translateX(-180%) rotate(180deg);
    }

    .serviceSlider {
        height: 700px;
    }

    .serviceSlider .swiper-slide.swiper-slide-active .cardSlide {
        height: 560px;
    }


    .w-lg-75 {
        width: 75% !important;
    }

    .pricing .swiper-button-next,
    .pricing .swiper-button-prev {
        width: 70px;
        height: 70px;
    }

    .swiper-rtl .swiper-button-next {
        left: 80px;
        right: initial;
    }


    .swiper-rtl .swiper-button-prev {
        left: initial;
        right: 80px;
    }


    .formSection {
        transform: translateY(65px);
    }


}

@media (max-width: 1199.98px) {

    .subtitle {
        font-size: 20px;
    }

    .title {
        font-size: 48px;
    }

    .swiper-rtl .swiper-button-next,
    .swiper-rtl .swiper-button-prev {
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 992.98px) {

    .rounded-img {
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
    }

    .serviceSlider {
        height: auto !important;
    }

    .serviceSlider .swiper-slide.swiper-slide-active .cardSlide,
    .serviceSlider .swiper-slide .cardSlide {
        height: 350px !important;
    }

    .OurBlogs .card .card-text {
        font-size: 13px !important;
    }

    .OurBlogs .card .card-title a {
        font-size: 15px !important;
    }

    .OurBlogs .card .card-title {
        font-weight: 800;
    }

    .subtitle {
        font-size: 16px;
    }

    .title {
        font-size: 36px;
    }

    .Banner .swiper-slide {
        height: calc(100vh - 93px);
        display: flex;
    }

    .teamSliderSec .swiper-button-next,
    .teamSliderSec .swiper-button-prev,
    .swiper-button-next.blogsliderbtn,
    .swiper-button-prev.blogsliderbtn,
    .swiper-rtl .swiper-button-next,
    .swiper-rtl .swiper-button-prev {
        width: 40px;
        height: 40px;
    }

    .swiper-button-next.blogsliderbtn {
        right: 40px;
        top: 45%;
    }

    .swiper-button-prev.blogsliderbtn {
        left: 40px;
        top: 45%;
    }

    .swiper-button-next.blogsliderbtn svg path,
    .swiper-button-prev.blogsliderbtn svg path {
        fill: white;
    }

    .OurBlogs .card-body {
        padding: 16px 18px;
    }

    .Banner .swiper-slide {
        padding-top: 40px;
    }

    .xl.btn-hover {
        padding: 12px 32px;
        font-size: 18px;
    }

    .heading {
        font-size: 48px;
    }

    .navbar-brand img {
        max-height: 30px;
    }

    .subtitle2 {
        font-size: 16px;
    }

    .title svg {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(20%, 40%);
        max-width: 66px;
    }

    .sameRBBtn .btn-hover {
        width: 60%;
        min-width: max-content;
    }

    p.content,
    article.entry-content > p,
    .contactList a {
        font-size: 16px;
    }

    .strip h2 {
        font-size: 46px;
    }

    .strip svg {
        max-width: 40px;
    }

    .copyright {
        font-size: 14px;
        padding-bottom: 15px;
    }

    .VideoPlayer .play-pause-icon {
        max-width: 50px;
    }

    .PricingSlider .swiper-slide ul li {
        margin-bottom: 10px;
        font-size: 14px;
    }

    .PricingSlider .swiper-slide .title {
        font-size: 54px;
    }

    .PricingSlide .btn-hover {
        margin-top: 30px !important;
    }

    .serviceSlider {
        height: 500px;
    }

    .serviceSlider .swiper-slide.swiper-slide-active .cardSlide {
        height: 400px;
    }

    .PricingSlider .PricingSlide {
        padding-top: 0;
    }

    .PricingSlider {
        height: initial !important;
    }

    /* -----------  */
    .serviceSliderSec .swiper-button-next {
        transform: translateX(-80%);
    }

    .serviceSliderSec .swiper-button-prev {
        transform: translateX(80%) rotate(180deg) translateY(-50%);
    }

    .form-check label {
        font-size: 14px;
    }

}

@media (max-width: 575.98px) {
    .title {
        font-size: 24px;
    }

    .formSection .formSectionWrap svg {
        height: 40px;
    }

    .formSectionWrap {
        padding: 26px 26px;
        border-radius: 20px;
    }

    /* .imgBox .box h4 {
        font-size: 16px;
    }

    .imgBox .box p {
        font-size: 14px;
    } */
    .imgBox .icon {
        height: 80px;
    }

    .imgBox .box {
        height: initial;
    }

    .OurBlogs .horizontalCards .card {
        flex-direction: column;
    }

    .OurBlogs .horizontalCards .card .featuredImg,
    .OurBlogs .horizontalCards .card .card-body {
        width: 100%;
    }

    .OurBlogs .horizontalCards .card .card-body {
        padding: 0px;
    }

    .PopSliderPrice .PricingSlide {
        width: 80% !important;
    }

    .PopSliderPrice .fs-5 {
        font-size: 16px !important;
    }

    .PopSliderPrice ul li span {
        font-size: 14px;
    }
}

@media (min-width: 1200px) and (max-width: 1300px) {
    #navbarNav {
        width: 80%;
    }

    .navbar-brand {
        width: 22%;
    }

    .navbar-brand img {
        width: 100%;
    }
}


.infinite-scroll-action {
    height: 350px;
}

.infinite-scroll-action {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.infinite-scroll-action .wrapper {
    width: 110%;
    height: 90px;
    position: absolute;
    background: linear-gradient(#FFFBFB, #FEF1F0);
}

@keyframes scrollLeft {
    to {
        left: -310px;
    }
}

@keyframes scrollRight {
    to {
        right: -310px;
    }
}

.first {
    transform: rotate(7deg);

}

.second {
    transform: rotate(-7deg);
}

.itemLeft,
.itemRight {
    font-weight: bold !important;
    width: 310px;
    font-size: 3rem;
    position: absolute;
    animation-timing-function: linear;
    animation-duration: 27s;
    animation-iteration-count: infinite;
    /* transform: translateY(5px); */
}

.itemLeft {
    left: max(calc(310px * 11), 100%);
    animation-name: scrollLeft;
}

.itemRight {
    right: max(calc(310px * 11), calc(100% + 310px));
    animation-name: scrollRight;
}

.slider {
    /* background: url("../images/bg-shape-op.svg") no-repeat top center/100% auto, url("../images/bg-shape.svg") no-repeat bottom/100% auto, linear-gradient(#0D1121dd, #0D1121dd), url("../images/bg-test.png") no-repeat center/cover; */
    background: linear-gradient(#0D1121dd, #0D1121dd), url("./assets/images/bg-test.png") no-repeat center/cover;
    position: relative;
}

.slider::after,
.slider::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 100px;
    background-image: url('./assets/images/wave-svg.svg');
    background-size: auto 100%;
    background-repeat: repeat-x;
    animation: moveBackground 10s linear infinite;
    z-index: 1;
}

.slider::before {
    background-image: url('./assets/images/wave-svg-2.svg');
    background-size: auto 100%;
    background-repeat: repeat-x;
    animation: moveBackground 10s linear infinite;
    z-index: 1;
    top: -2px;
    bottom: initial;
}


.indication-btn {
    width: 25px !important;
    height: 25px !important;
    border-radius: 50% !important;
    border: none !important;
}

.left-arrow,
.right-arrow {
    border: 2px solid white !important;
    width: 65px;
    height: 65px;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    display: flex;

}

.left-arrow svg,
.right-arrow svg {
    width: 60%;
}

.carousel-control-prev {
    right: initial !important;
    left: 0 !important;
}

.carousel-control-next {
    left: initial !important;
    right: 0 !important;
}

.carousel-control-next,
.carousel-control-prev {
    top: 15%;
    bottom: initial !important;
}

.carousel-indicators [data-bs-target] {
    opacity: 1;
    background-color: #D9D9D9 !important;
}

.carousel-indicators .active {
    background: linear-gradient(#F36360, #FD12C3);
}


.heading_text {
    font-size: 5.75rem;
}

.heading_desc {
    font-size: 1.37rem;
}

.slider_content {
    font-size: 2rem;
}

.review {
    font-size: 2.62rem;
}

.grid-left-heading {
    font-size: 1.21rem;
    margin: auto;
}

.grid-right-heading {
    font-size: 1.62rem;
    margin: auto;
}

.grid-left-desc {
    font-size: 0.8rem;
    margin: auto;
}

.grid-right-desc {
    font-size: 1.11rem;
    align-self: center;
    margin: auto;
    font-weight: 600;

}

.view-all-link {
    color: var(--secondary);
    font-weight: 600;
    transition: var(--transition);
}

.videos-container > div {
    margin-bottom: 2rem;
}

.OurBlogs .card-body > p a {
    color: #212529;
}

.search > header,
.single-online-vod > header {
    display: none;
}

.search .custom-logo,
.single-online-vod .custom-logo {
    display: flex;
    justify-content: center;
}

.video-js {
    width: 100%;
    height: 400px;
    background-color: #000;
}

.video-link:hover {
    color: #000 !important;
    text-decoration: underline !important;
}

@media (min-width: 992px) {

    .single-online-vod .VideoPlayer {
        min-height: 610px;
    }

    .slider .carousel-item {
        padding: 0 120px 90px;
    }
}

@media (max-width: 992px) {

    .infinite-scroll-action {
        height: 200px;
    }

    .infinite-scroll-action .wrapper {
        height: 38px;
    }

    .itemLeft,
    .itemRight {
        width: max-content;
        font-size: 15px;
    }

    .itemLeft {
        left: max(calc(86px * 11), 100%);
        animation-name: scrollLeft;
    }

    .itemRight {
        right: max(calc(86px * 11), calc(100% + 86px));

    }

    .itemLeft img,
    .itemRight img {
        width: 1.25rem;
        margin: 0 0.62rem;
    }

    .first {
        transform: rotate(9deg);

    }

    .second {
        transform: rotate(-9deg);
    }

    .slider_content {
        font-size: 18px;
    }

    .left-arrow,
    .right-arrow {
        width: 38px;
        height: 38px;
    }

    .indication-btn {
        width: 12px !important;
        height: 12px !important;
    }

    .review {
        font-size: 16px;
    }

    .rating img {
        max-width: 14px;
    }

    .slider .carousel-item {
        padding: 0 60px 40px;
    }
}

@media (max-width: 575.98px) {
    .slider_content {
        font-size: 14px;
    }
}

.marquee-wrapper {
    width: 100%;
    overflow: hidden;
}

.marquee {
    white-space: nowrap;
}

.marquee-content {
    display: inline-block;
    animation: scroll 15s linear infinite;
}

@keyframes scroll {
    from {
        transform: translateX(50%);
    }

    to {
        transform: translateX(0%);
    }
}

.marquee h2 {
    font-size: 46px;
    font-weight: 900;
    display: inline;
    margin: 0;
    padding-right: 8px;
}

.marquee img {
    padding-bottom: 14px;
    width: 46px;
}

.marquee.sm h2 {
    font-size: 38px;
}

.marquee.sm img {
    width: 36px;
}

.diagonal .marquee-wrapper:first-child {
    transform: rotate(6deg);
    transform-origin: center;
}

.diagonal .marquee-wrapper:last-child {
    transform: rotate(-6deg);
    transform-origin: center;
}

@media (max-width: 991.98px) {
    .marquee img {
        padding-bottom: 8px;
        width: 36px;
    }

    .marquee h2 {
        font-size: 36px;
    }
}

.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar,
.video-js .vjs-control-bar {
    bottom: 4px !important;
    border-radius: 0 !important;
    height: 4rem !important;
}

.video-js .vjs-control {
    align-items: center;
    justify-content: center;
    display: flex;
}

.video-js {
    width: 100%;
    height: 400px; /* Matches existing style */
    background-color: var(--black);
    border-radius: 12px 12px 0 0; /* Matches .VideoPlayBox */
    box-shadow: 0 0 20px 5px #0001; /* Matches .shadow-md */
    font-family: "Assistant", sans-serif;
    direction: rtl; /* Hebrew support */
}

.vjs-control-bar {
    background: linear-gradient(to top, #000a, #0000); /* Subtle fade */
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    height: 60px;
    padding: 0 10px;
    overflow: visible; /* Prevent clipping */
}

.vjs-control {
    color: var(--white);
    font-size: 18px; /* Slightly smaller for consistency */
    width: 40px; /* Standardize control width */
    height: 60px; /* Match control bar height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.vjs-button > .vjs-icon-placeholder:before {
    line-height: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-around !important;
}

.vjs-play-control .vjs-icon-play,
.vjs-play-control .vjs-icon-pause {
    color: var(--secondary);
    font-size: 18px; /* Match other icons */
}

.vjs-play-control:hover .vjs-icon-play,
.vjs-play-control:hover .vjs-icon-pause {
    color: var(--primary);
    transform: scale(1.1);
}

.vjs-volume-panel {
    width: 40px; /* Same as other controls */
    height: 60px; /* Match control bar */
}

.vjs-mute-control .vjs-icon-volume-high,
.vjs-mute-control .vjs-icon-volume-low,
.vjs-mute-control .vjs-icon-volume-mute {
    font-size: 18px; /* Match other icons */
    line-height: 60px; /* Center vertically */
    color: var(--secondary);
}

.vjs-mute-control:hover .vjs-icon-volume-high,
.vjs-mute-control:hover .vjs-icon-volume-low,
.vjs-mute-control:hover .vjs-icon-volume-mute {
    color: var(--primary);
    transform: scale(1.1);
}

.vjs-volume-panel .vjs-volume-bar,
.vjs-volume-panel .vjs-volume-level {
    background: var(--secondary);
}

.vjs-volume-panel:hover .vjs-volume-bar,
.vjs-volume-panel:hover .vjs-volume-level {
    background: var(--primary);
}

.vjs-progress-control .vjs-progress-holder {
    background: #fff3; /* Light overlay */
    border-radius: 4px;
}

.vjs-progress-control .vjs-play-progress {
    background: var(--grad-dark); /* Pink gradient */
}

.vjs-progress-control .vjs-load-progress {
    background: #fff5;
}

.vjs-progress-control .vjs-time-tooltip,
.vjs-progress-control .vjs-progress-holder .vjs-tooltip {
    background: var(--primary);
    color: var(--white);
    font-weight: 600;
}

.vjs-skip-backward {
    width: 40px;
    height: 60px;
    color: var(--secondary);
}

.vjs-skip-backward:hover {
    color: var(--primary);
    transform: scale(1.1);
}

.vjs-skip-backward .vjs-icon-placeholder:before {
    content: '\f103'; /* Video.js icon font for "skip backward" (similar to -10s) */
    font-family: VideoJS;
    font-size: 18px;
    line-height: 60px;
}

.vjs-replay {
    width: 40px;
    height: 60px;
    color: var(--secondary);
}

.vjs-replay:hover {
    color: var(--primary);
    transform: scale(1.1);
}

.vjs-replay .vjs-icon-placeholder:before {
    content: '\f110'; /* Video.js icon font for "replay" */
    font-family: VideoJS;
    font-size: 18px;
    line-height: 60px;
}

.vjs-skip-forward {
    width: 40px;
    height: 60px;
    color: var(--secondary);
}

.vjs-skip-forward:hover {
    color: var(--primary);
    transform: scale(1.1);
}

.vjs-skip-forward .vjs-icon-placeholder:before {
    content: '\f101'; /* Video.js icon font for "skip forward" (similar to +20s) */
    font-family: VideoJS;
    font-size: 18px;
    line-height: 60px;
}

.vjs-fullscreen-control .vjs-icon-fullscreen-enter,
.vjs-fullscreen-control .vjs-icon-fullscreen-exit {
    color: var(--secondary);
    font-size: 18px; /* Match other icons */
}

.vjs-fullscreen-control:hover .vjs-icon-fullscreen-enter,
.vjs-fullscreen-control:hover .vjs-icon-fullscreen-exit {
    color: var(--primary);
}

.vjs-error-display .vjs-modal-dialog-content {
    background: var(--primary);
    color: var(--white);
    font-size: 18px;
    font-weight: 600;
    border-radius: 8px;
}

.VideoPlayBox .watched-wrap img {
    filter: brightness(0) saturate(100%) invert(67%) sepia(85%) saturate(506%) hue-rotate(300deg) brightness(103%) contrast(103%); /* Matches --secondary */
    transition: var(--transition);
}

.VideoPlayBox .watched-wrap img:hover {
    filter: brightness(0) saturate(100%) invert(47%) sepia(44%) saturate(614%) hue-rotate(325deg) brightness(100%) contrast(97%); /* Matches --primary */
}

.VideoPlayer .play-pause-icon {
    max-width: 80px; /* Slightly larger for emphasis */
    filter: drop-shadow(0 0 5px #0005); /* Matches .shadow-md */
}

.VideoPlayer .play-pause-icon:hover {
    transform: translate(-50%, -50%) scale(1.1); /* Matches .btn-hover */
    opacity: 1;
}

.vjs-skip-backward .vjs-icon-placeholder:before {
    content: '-10';
    font-family: "Assistant", sans-serif;
    font-weight: 600;
}

.vjs-skip-forward .vjs-icon-placeholder:before {
    content: '+20';
    font-family: "Assistant", sans-serif;
    font-weight: 600;
}

.vjs-skip-backward .vjs-control-text,
.vjs-replay .vjs-control-text,
.vjs-skip-forward .vjs-control-text {
    background: var(--grad-dark);
    color: var(--white);
    font-weight: 600;
    border-radius: 4px;
}

.video-js .vjs-mute-control {
    font-size: 11px;
}

@media (min-width: 992px) {
    .video-js {
        height: 610px; /* Matches .single-online-vod .VideoPlayer */
    }
}

@media (max-width: 575.98px) {
    .video-js {
        height: 300px; /* Smaller for mobile */
    }

    .vjs-control-bar {
        height: 50px;
    }

    .vjs-control {
        width: 30px; /* Smaller for mobile */
        height: 50px;
    }

    .vjs-button > .vjs-icon-placeholder:before {
        line-height: 50px;
    }

    .vjs-volume-panel,
    .vjs-skip-backward,
    .vjs-replay,
    .vjs-skip-forward {
        width: 30px;
    }

    .vjs-mute-control .vjs-icon-volume-high,
    .vjs-mute-control .vjs-icon-volume-low,
    .vjs-mute-control .vjs-icon-volume-mute,
    .vjs-skip-backward .vjs-icon-placeholder:before,
    .vjs-replay .vjs-icon-placeholder:before,
    .vjs-skip-forward .vjs-icon-placeholder:before {
        font-size: 16px; /* Slightly smaller for mobile */
        line-height: 50px;
    }
}

.video-js .vjs-big-play-button {
}

.vjs-big-play-button {
    background-color: transparent !important;
    width: 70px !important;
    height: 70px !important;
    background-image: url(https://tmp.finetuning-digital.com/wp-content/themes/ftdigital/assets/images/playpause-color.svg) !important;
    background-repeat: no-repeat !important;
    background-size: contain !important;
    /* background-position: 50% calc(50% - 10px) !important; */
    border: none !important;
}

.local.VideoPlayer .vjs-big-play-button > .vjs-icon-placeholder {
    display: none !important;
}